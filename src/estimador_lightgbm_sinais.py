#!/usr/bin/env python3
"""
Estimador LightGBM para Sinais de Trading
Replica exatamente a funcionalidade do estimador XGBoost usando LightGBM
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import lightgbm as lgb
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import os
import pickle
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Adicionar o diretório src ao path para importar functions e config
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config_loader import config, setup_environment

# Importar cache otimizado se disponível
try:
    from cache_optimized import optimized_cache
    CACHE_OPTIMIZED_AVAILABLE = True
except ImportError:
    CACHE_OPTIMIZED_AVAILABLE = False

# Importar cache unificado se disponível
try:
    from cache_unified import unified_cache
    CACHE_UNIFIED_AVAILABLE = True
except ImportError:
    CACHE_UNIFIED_AVAILABLE = False

# Importar funções de features do módulo compartilhado
from features_xgboost import calcular_features_e_sinais

# Importar funções do classificador para reutilizar lógica de dados
from classificador_xgboost_sinais import (
    carregar_acoes_diversificadas,
    baixar_dados_todas_acoes_unificado,
    baixar_dados_acao_otimizado,
    baixar_dados_acao,
    verificar_status_cache,
    limpar_cache_historico
)

# Configurações globais
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
RANDOM_SEED = 42
np.random.seed(RANDOM_SEED)

def preparar_dataset_regressao(acoes_dados):
    """
    Prepara dataset combinado de todas as ações para treinamento de regressão
    Preserva o índice de data para divisão temporal
    Target: variação percentual da média OHLC em relação ao dia anterior
    """
    datasets = []

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 50:
            # Adicionar coluna do ticker para identificação
            dados_copy = dados.copy()
            dados_copy['Ticker'] = ticker
            # Resetar índice para preservar as datas como coluna
            dados_copy = dados_copy.reset_index()
            datasets.append(dados_copy)

    if not datasets:
        print("❌ Nenhum dataset válido encontrado")
        return None, None, None, None

    # Combinar todos os datasets
    dataset_completo = pd.concat(datasets, ignore_index=True)
    dataset_completo['Date'] = pd.to_datetime(dataset_completo['Date'])
    dataset_completo = dataset_completo.set_index('Date')

    print(f"📊 Dataset combinado criado:")
    print(f"   • Total de registros: {len(dataset_completo)}")
    print(f"   • Ações incluídas: {dataset_completo['Ticker'].nunique()}")
    print(f"   • Período: {dataset_completo.index.min().strftime('%Y-%m-%d')} a {dataset_completo.index.max().strftime('%Y-%m-%d')}")

    # Calcular features básicas (sem usar Close do dia atual)
    basic_features = ['Volume', 'Spread', 'Volatilidade']

    # Calcular features econométricas lagged (versões históricas)
    econometric_features_lagged = []
    econometric_lags = config.get('lightgbm.features.econometric_lags')
    
    # Features econométricas base
    econometric_base = ['MFI', 'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line',
                       'Parkinson_Volatility', 'EMV', 'EMV_MA', 'VO', 'High_Max_50', 'Low_Min_50']
    
    # Adicionar lags das features econométricas
    for feature in econometric_base:
        for lag in range(1, econometric_lags + 1):
            econometric_features_lagged.append(f'{feature}_Lag_{lag}')

    # Combinar apenas features lagged e temporais (SEM features do dia atual)
    feature_cols = basic_features + econometric_features_lagged

    # NOVO TARGET: Variação percentual da média OHLC em relação ao dia anterior
    # Calcular variação percentual: (valor_hoje - valor_ontem) / valor_ontem * 100
    dataset_completo = dataset_completo.sort_values(['Ticker', 'Date'])
    dataset_completo['Media_OHLC_Anterior'] = dataset_completo.groupby('Ticker')['Media_OHLC'].shift(1)

    # Calcular variação percentual
    dataset_completo['Target_PctChange'] = (
        (dataset_completo['Media_OHLC'] - dataset_completo['Media_OHLC_Anterior']) /
        dataset_completo['Media_OHLC_Anterior'] * 100
    )

    # Verificar se todas as features existem no dataset
    features_existentes = [col for col in feature_cols if col in dataset_completo.columns]
    features_faltando = [col for col in feature_cols if col not in dataset_completo.columns]

    if features_faltando:
        print(f"   ⚠️ Features não encontradas: {len(features_faltando)}")
        print(f"   📋 Usando apenas features existentes: {len(features_existentes)}")
        feature_cols = features_existentes

    X = dataset_completo[feature_cols]
    y = dataset_completo['Target_PctChange']

    # Remover registros com NaN no target ou nas features
    mask_validos = ~y.isna() & ~X.isna().any(axis=1)
    X_filtrado = X[mask_validos].copy()
    y_filtrado = y[mask_validos].copy()
    dataset_filtrado = dataset_completo[mask_validos].copy()

    print(f"📊 Dataset de regressão preparado:")
    print(f"   • Total de registros: {len(y_filtrado)}")
    print(f"   • Features utilizadas: {len(feature_cols)}")
    print(f"   • Target: Variação % OHLC (min: {y_filtrado.min():.2f}%, max: {y_filtrado.max():.2f}%, média: {y_filtrado.mean():.2f}%)")

    return X_filtrado, y_filtrado, feature_cols, dataset_filtrado

def treinar_estimador_lightgbm(X, y, feature_cols, dataset_completo=None):
    """
    Treina um estimador LightGBM para predizer variação percentual da média OHLC
    Usa divisão temporal configurável para treino e teste
    """
    # Usar configurações do LightGBM
    use_scaler = config.get('lightgbm.use_standard_scaler')
    lgb_params = config.get('lightgbm.model_params').copy()

    # Configurar parâmetros específicos do LightGBM
    lgb_params.update({
        'objective': 'regression',
        'metric': 'rmse',
        'boosting_type': 'gbdt',
        'verbose': -1,  # Silencioso
        'deterministic': True,  # Para reprodutibilidade
        'force_row_wise': True,  # Para reprodutibilidade
        'feature_fraction': lgb_params.get('colsample_bytree', 1.0),
        'bagging_fraction': lgb_params.get('subsample', 1.0),
        'bagging_freq': 0 if lgb_params.get('subsample', 1.0) == 1.0 else 1,
    })

    # Remover parâmetros que não existem no LightGBM
    lgb_params.pop('colsample_bytree', None)
    lgb_params.pop('subsample', None)

    # Divisão temporal dos dados
    use_temporal_split = config.get('lightgbm.temporal_split.use_temporal_split')
    test_size = config.get('lightgbm.test_size')

    if use_temporal_split and dataset_completo is not None:
        # Divisão temporal: últimos X% dos dados para teste
        dataset_sorted = dataset_completo.sort_index()
        split_date = dataset_sorted.index[int(len(dataset_sorted) * (1 - test_size))]
        
        train_mask = X.index < split_date
        test_mask = X.index >= split_date
        
        X_train, X_test = X[train_mask], X[test_mask]
        y_train, y_test = y[train_mask], y[test_mask]
        
        print(f"   • Divisão temporal: treino até {split_date.strftime('%Y-%m-%d')}")
    else:
        # Divisão aleatória
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=RANDOM_SEED
        )
        print(f"   • Divisão aleatória: {test_size*100:.0f}% para teste")

    print(f"   • Treino: {len(X_train)} registros")
    print(f"   • Teste: {len(X_test)} registros")

    # Normalizar features se configurado
    if use_scaler:
        # StandardScaler com seed fixa para reprodutibilidade
        scaler = StandardScaler()

        # Remover duplicatas no índice se existirem
        if X_train.index.duplicated().any():
            print(f"   🔧 Removendo {X_train.index.duplicated().sum()} índices duplicados do treino")
            X_train = X_train[~X_train.index.duplicated(keep='last')]
            y_train = y_train[~y_train.index.duplicated(keep='last')]

        if X_test.index.duplicated().any():
            print(f"   🔧 Removendo {X_test.index.duplicated().sum()} índices duplicados do teste")
            X_test = X_test[~X_test.index.duplicated(keep='last')]
            y_test = y_test[~y_test.index.duplicated(keep='last')]

        # Garantir ordem determinística dos dados antes do fit
        X_train_sorted = X_train.sort_index()
        y_train_sorted = y_train.reindex(X_train_sorted.index)

        X_train_scaled = scaler.fit_transform(X_train_sorted)
        X_test_scaled = scaler.transform(X_test)
        X_train_scaled = pd.DataFrame(X_train_scaled, columns=feature_cols, index=X_train_sorted.index)
        X_test_scaled = pd.DataFrame(X_test_scaled, columns=feature_cols, index=X_test.index)

        # Restaurar ordem original
        X_train_scaled = X_train_scaled.reindex(X_train.index)
        y_train = y_train_sorted.reindex(X_train.index)
    else:
        scaler = None
        X_train_scaled = X_train
        X_test_scaled = X_test

    # NOVO: Calcular pesos para o treinamento se configurado
    use_weighted_training = config.get('lightgbm.estimator.use_weighted_training')
    weights = None
    if use_weighted_training:
        print("\n⚖️  Usando pesos no treinamento para amenizar o impacto de grandes variações.")
        # Pesos inversamente proporcionais ao módulo do target para amenizar o efeito de grandes variações
        # Adicionar um pequeno epsilon para evitar divisão por zero
        weights = 1/(np.exp(-(y_train**2)/8)+1e-6)
        
        # Normalizar os pesos para que a soma não altere drasticamente a magnitude do gradiente
        weights = weights/np.mean(weights)

    # Treinar estimador LightGBM
    print("\n🚀 Treinando estimador LightGBM...")
    print(f"   • Target: Variação % OHLC (regressão)")
    print(f"   • Função de perda: RMSE")

    modelo = lgb.LGBMRegressor(**lgb_params)
    # Passar os pesos para o método fit (se existirem)
    modelo.fit(X_train_scaled, y_train, sample_weight=weights)

    # Fazer predições
    y_pred = modelo.predict(X_test_scaled)

    # Calcular métricas de regressão
    mse = mean_squared_error(y_test, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)

    print(f"   • RMSE: {rmse:.4f}")
    print(f"   • MAE: {mae:.4f}")
    print(f"   • R²: {r2:.4f}")

    resultados = {
        'modelo': modelo,
        'scaler': scaler,
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'y_test': y_test,
        'y_pred': y_pred,
        'feature_importance': modelo.feature_importances_,
        'X_test': X_test_scaled,
        'X_train': X_train_scaled,
        'y_train': y_train
    }

    return resultados, feature_cols

def criar_grafico_importancia_features(resultados_modelo, feature_cols):
    """
    Cria gráfico de importância das features do estimador LightGBM
    """
    print(f"📊 Criando gráfico de importância das features...")

    # Criar diretório de figuras
    figures_dir = 'results/figures/lightgbm_estimador_analysis'
    os.makedirs(figures_dir, exist_ok=True)

    # Preparar dados de importância
    feature_importance = resultados_modelo['feature_importance']
    feature_importance_df = pd.DataFrame({
        'feature': feature_cols,
        'importance': feature_importance
    }).sort_values('importance', ascending=True)

    # Mostrar apenas top 20 features
    feature_importance_df = feature_importance_df.tail(20)

    # Criar gráfico
    plt.figure(figsize=(12, 10))
    ax = plt.gca()

    bars = ax.barh(range(len(feature_importance_df)), feature_importance_df['importance'],
                   color='lightcoral', alpha=0.8, edgecolor='darkred', linewidth=0.5)

    # Configurações do gráfico
    ax.set_yticks(range(len(feature_importance_df)))
    ax.set_yticklabels(feature_importance_df['feature'], fontsize=9)
    ax.set_xlabel('Importância', fontsize=12, fontweight='bold')
    ax.set_title('Top 20 Features Mais Importantes - Estimador LightGBM\n' +
                f'Total de Features: {len(feature_cols)}',
                fontsize=14, fontweight='bold', pad=20)

    # Adicionar valores nas barras
    for i, bar in enumerate(bars):
        width = bar.get_width()
        ax.text(width + 0.001, bar.get_y() + bar.get_height()/2,
                f'{width:.3f}', ha='left', va='center', fontsize=8)

    ax.grid(True, alpha=0.3, axis='x')
    plt.tight_layout()

    # Salvar gráfico
    plt.savefig(os.path.join(figures_dir, 'feature_importance_estimador.png'),
                dpi=300, bbox_inches='tight')
    plt.close()

    print(f"📊 Gráfico de importância das features salvo")

def criar_graficos_desempenho_estimador(resultados_modelo, feature_cols):
    """
    Cria gráficos para avaliar o desempenho do estimador LightGBM
    """
    print(f"\n📊 Criando gráficos de desempenho do estimador...")

    # Criar diretório de figuras
    figures_dir = 'results/figures/lightgbm_estimador_analysis'
    os.makedirs(figures_dir, exist_ok=True)

    # Limpar figuras antigas
    if config.get('output.clean_old_files'):
        for arquivo in os.listdir(figures_dir):
            if arquivo.endswith('.png'):
                os.remove(os.path.join(figures_dir, arquivo))
        print(f"🗑️ Figuras antigas removidas de {figures_dir}")

    # 1. Gráfico de importância das features
    criar_grafico_importancia_features(resultados_modelo, feature_cols)

    # 2. Gráfico de predições vs valores reais
    y_test = resultados_modelo['y_test']
    y_pred = resultados_modelo['y_pred']
    rmse = resultados_modelo['rmse']
    mae = resultados_modelo['mae']
    r2 = resultados_modelo['r2']

    plt.figure(figsize=(12, 8))
    ax = plt.gca()

    # Scatter plot
    ax.scatter(y_test, y_pred, alpha=0.6, s=20, color='steelblue', label='Predições')

    # Linha de referência (predição perfeita)
    min_val = min(y_test.min(), y_pred.min())
    max_val = max(y_test.max(), y_pred.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='Predição Perfeita')

    # Configurações do gráfico
    ax.set_xlabel('Variação % Real', fontsize=12)
    ax.set_ylabel('Variação % Estimada', fontsize=12)
    ax.set_title('Estimador LightGBM: Variação % Real vs Estimada\n' +
                f'R² = {r2:.4f} | RMSE = {rmse:.2f}% | MAE = {mae:.2f}%',
                fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)

    # Adicionar estatísticas no gráfico
    ax.text(0.05, 0.95, f'Pontos: {len(y_test)}\nR²: {r2:.4f}\nRMSE: {rmse:.2f}\nMAE: {mae:.2f}',
            transform=ax.transAxes, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'predicoes_vs_real.png'),
                dpi=300, bbox_inches='tight')
    plt.close()

    print(f"📊 Gráfico de predições vs real salvo")

    # 3. Histograma dos resíduos
    residuos = y_test - y_pred

    plt.figure(figsize=(10, 6))
    ax = plt.gca()

    ax.hist(residuos, bins=50, alpha=0.7, color='lightgreen', edgecolor='darkgreen')
    ax.axvline(residuos.mean(), color='red', linestyle='--', linewidth=2,
               label=f'Média: {residuos.mean():.3f}')
    ax.axvline(0, color='black', linestyle='-', linewidth=1, alpha=0.5, label='Zero')

    ax.set_xlabel('Resíduos (Real - Predito)', fontsize=12)
    ax.set_ylabel('Frequência', fontsize=12)
    ax.set_title('Distribuição dos Resíduos - Estimador LightGBM', fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'distribuicao_residuos.png'),
                dpi=300, bbox_inches='tight')
    plt.close()

    print(f"📊 Gráfico de distribuição dos resíduos salvo")

def salvar_modelo_estimador(resultados_modelo, feature_cols):
    """
    Salva o modelo treinado e metadados
    """
    print(f"💾 Salvando modelo estimador LightGBM...")

    # Criar diretório de modelos
    models_dir = 'results/models/lightgbm_estimador_analysis'
    os.makedirs(models_dir, exist_ok=True)

    # Salvar modelo
    modelo_path = os.path.join(models_dir, 'modelo_estimador.pkl')
    with open(modelo_path, 'wb') as f:
        pickle.dump({
            'modelo': resultados_modelo['modelo'],
            'scaler': resultados_modelo['scaler'],
            'feature_cols': feature_cols,
            'metricas': {
                'rmse': resultados_modelo['rmse'],
                'mae': resultados_modelo['mae'],
                'r2': resultados_modelo['r2']
            },
            'data_treinamento': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }, f)

    # Salvar resumo do treinamento
    resumo_path = os.path.join(models_dir, 'resumo_treinamento.txt')
    with open(resumo_path, 'w', encoding='utf-8') as f:
        f.write("RESUMO DO TREINAMENTO LIGHTGBM - ESTIMADOR DE VARIAÇÃO\n")
        f.write("=" * 70 + "\n\n")
        f.write("MODELO ESTIMADOR:\n")
        f.write(f"  • Tipo: LightGBM Regressor\n")
        f.write(f"  • Target: Variação % OHLC (regressão)\n")
        f.write(f"  • Função de perda: RMSE\n")
        f.write(f"  • Features básicas: pct_change da média OHLC (variação percentual), Volume, Spread, Volatilidade\n")
        f.write(f"  • Features econométricas: Parkinson, MFI, EMV, Amihud, Roll Spread,\n")
        f.write(f"    Hurst, Vol/Volume, CMF, A/D Line, Volume Oscillator, High Max 50d, Low Min 50d (13 features)\n")
        f.write(f"  • Features lagged: {config.get('lightgbm.features.econometric_lags')} lags para cada feature econométrica\n")
        f.write(f"  • Total de features: {len(feature_cols)}\n")
        f.write(f"  • RMSE: {resultados_modelo['rmse']:.4f}\n")
        f.write(f"  • MAE: {resultados_modelo['mae']:.4f}\n")
        f.write(f"  • R²: {resultados_modelo['r2']:.4f}\n\n")
        f.write(f"DATA DO TREINAMENTO: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

    print(f"   ✅ Modelo salvo em: {modelo_path}")
    print(f"   ✅ Resumo salvo em: {resumo_path}")

def carregar_modelo_estimador():
    """
    Carrega modelo treinado se existir e estiver atualizado
    """
    models_dir = 'results/models/lightgbm_estimador_analysis'
    modelo_path = os.path.join(models_dir, 'modelo_estimador.pkl')

    if not os.path.exists(modelo_path):
        return None, None

    # Verificar se o modelo foi treinado hoje
    modelo_stat = os.path.getmtime(modelo_path)
    modelo_date = datetime.fromtimestamp(modelo_stat).date()
    hoje = datetime.now().date()

    if modelo_date != hoje:
        print(f"📅 Modelo foi treinado em {modelo_date}, retreinando...")
        return None, None

    try:
        with open(modelo_path, 'rb') as f:
            dados_modelo = pickle.load(f)

        print(f"✅ Modelo LightGBM carregado (treinado em {dados_modelo['data_treinamento']})")
        print(f"   • RMSE: {dados_modelo['metricas']['rmse']:.4f}")
        print(f"   • MAE: {dados_modelo['metricas']['mae']:.4f}")
        print(f"   • R²: {dados_modelo['metricas']['r2']:.4f}")

        resultados_modelo = {
            'modelo': dados_modelo['modelo'],
            'scaler': dados_modelo['scaler'],
            'rmse': dados_modelo['metricas']['rmse'],
            'mae': dados_modelo['metricas']['mae'],
            'r2': dados_modelo['metricas']['r2']
        }

        return resultados_modelo, dados_modelo['feature_cols']

    except Exception as e:
        print(f"❌ Erro ao carregar modelo: {e}")
        return None, None

def aplicar_predicoes_estimador(acoes_dados, resultados_modelo, feature_cols):
    """
    Aplica o modelo treinado para fazer predições nas ações
    Gera sinais de compra/venda baseados nas predições
    """
    print(f"\n🎯 Aplicando predições do estimador LightGBM...")

    modelo = resultados_modelo['modelo']
    scaler = resultados_modelo['scaler']

    # Configurações
    prob_threshold = config.get('lightgbm.probability_threshold')

    acoes_com_predicoes = {}
    total_sinais_compra = 0
    total_sinais_venda = 0

    for ticker, dados in acoes_dados.items():
        if dados is None or len(dados) < 50:
            continue

        try:
            # Preparar features para o último dia
            ultimo_dia = dados.index[-1]
            features_ultimo_dia = dados.loc[[ultimo_dia], feature_cols]

            # Verificar se todas as features estão disponíveis
            if features_ultimo_dia.isna().any().any():
                print(f"   ⚠️ {ticker}: Features incompletas no último dia")
                continue

            # Normalizar se necessário
            if scaler is not None:
                features_scaled = scaler.transform(features_ultimo_dia)
                features_scaled = pd.DataFrame(features_scaled, columns=feature_cols, index=features_ultimo_dia.index)
            else:
                features_scaled = features_ultimo_dia

            # Fazer predição
            predicao = modelo.predict(features_scaled)[0]

            # Determinar sinal baseado na predição
            # Se predição > threshold: COMPRA
            # Se predição < -threshold: VENDA
            # Caso contrário: SEM AÇÃO
            if predicao > prob_threshold:
                sinal = 'COMPRA'
                total_sinais_compra += 1
            elif predicao < -prob_threshold:
                sinal = 'VENDA'
                total_sinais_venda += 1
            else:
                sinal = 'SEM_ACAO'

            # Obter preço atual (média OHLC)
            preco_atual = dados['Media_OHLC'].iloc[-1]

            acoes_com_predicoes[ticker] = {
                'dados': dados,
                'predicao': predicao,
                'sinal': sinal,
                'preco_atual': preco_atual,
                'ultimo_dia': ultimo_dia
            }

        except Exception as e:
            print(f"   ❌ Erro ao processar {ticker}: {e}")
            continue

    print(f"   ✅ Predições aplicadas em {len(acoes_com_predicoes)} ações")
    print(f"   📈 Sinais de COMPRA: {total_sinais_compra}")
    print(f"   📉 Sinais de VENDA: {total_sinais_venda}")
    print(f"   ⏸️ SEM AÇÃO: {len(acoes_com_predicoes) - total_sinais_compra - total_sinais_venda}")

    return acoes_com_predicoes

def imprimir_recomendacoes_estimador(acoes_com_predicoes):
    """
    Imprime recomendações de trading baseadas nas predições do estimador
    """
    print(f"\n" + "="*80)
    print(f"🎯 RECOMENDAÇÕES DE TRADING - ESTIMADOR LIGHTGBM")
    print(f"="*80)

    # Separar por tipo de sinal
    sinais_venda = []
    sinais_compra = []

    for ticker, info in acoes_com_predicoes.items():
        if info['sinal'] == 'VENDA':
            sinais_venda.append((ticker, info['predicao'], info['preco_atual']))
        elif info['sinal'] == 'COMPRA':
            sinais_compra.append((ticker, info['predicao'], info['preco_atual']))

    # Ordenar por predição (mais forte primeiro)
    sinais_venda.sort(key=lambda x: x[1])  # Menor predição primeiro (mais negativa)
    sinais_compra.sort(key=lambda x: x[1], reverse=True)  # Maior predição primeiro

    # Mostrar sinais de VENDA primeiro
    if sinais_venda:
        print(f"\n📉 SINAIS DE VENDA ({len(sinais_venda)} ações):")
        print(f"{'Ação':<8} {'Predição':<12} {'Preço Atual':<12} {'Variação Esperada'}")
        print(f"{'-'*60}")
        for ticker, predicao, preco in sinais_venda:
            print(f"{ticker:<8} {predicao:>8.2f}%   R$ {preco:>8.2f}   {predicao:>6.2f}%")

    # Mostrar sinais de COMPRA
    if sinais_compra:
        print(f"\n📈 SINAIS DE COMPRA ({len(sinais_compra)} ações):")
        print(f"{'Ação':<8} {'Predição':<12} {'Preço Atual':<12} {'Variação Esperada'}")
        print(f"{'-'*60}")
        for ticker, predicao, preco in sinais_compra:
            print(f"{ticker:<8} {predicao:>8.2f}%   R$ {preco:>8.2f}   {predicao:>6.2f}%")

    if not sinais_venda and not sinais_compra:
        print(f"\n⏸️ Nenhum sinal de trading gerado hoje.")
        print(f"   Todas as predições estão abaixo do threshold de {config.get('lightgbm.probability_threshold')}%")

    print(f"\n" + "="*80)

def criar_graficos_sinais_individuais(acoes_com_predicoes, max_acoes=6):
    """
    Cria gráficos individuais para ações com sinais de compra/venda
    Mostra preço histórico, valor estimado e sinais
    """
    print(f"\n📈 Criando gráficos individuais de sinais...")

    # Criar diretório de figuras
    figures_dir = 'results/figures/lightgbm_estimador_analysis'
    individual_dir = os.path.join(figures_dir, 'individual_signals')
    os.makedirs(individual_dir, exist_ok=True)

    # Limpar figuras antigas
    if config.get('output.clean_old_files'):
        for arquivo in os.listdir(individual_dir):
            if arquivo.endswith('.png'):
                os.remove(os.path.join(individual_dir, arquivo))

    # Filtrar ações com sinais
    acoes_com_sinais = {ticker: info for ticker, info in acoes_com_predicoes.items()
                       if info['sinal'] in ['COMPRA', 'VENDA']}

    if not acoes_com_sinais:
        print(f"   ⚠️ Nenhuma ação com sinais para plotar")
        return

    # Limitar número de gráficos
    acoes_para_plotar = list(acoes_com_sinais.items())[:max_acoes]

    for ticker, info in acoes_para_plotar:
        dados = info['dados']
        predicao = info['predicao']
        sinal = info['sinal']
        preco_atual = info['preco_atual']

        # Usar últimos 60 dias para o gráfico
        dados_plot = dados.tail(60).copy()

        plt.figure(figsize=(14, 8))
        ax = plt.gca()

        # Plot do preço (média OHLC)
        ax.plot(dados_plot.index, dados_plot['Media_OHLC'],
                linewidth=2, label='Preço (Média OHLC)', color='blue')

        # Adicionar médias móveis
        if 'MM_10' in dados_plot.columns:
            ax.plot(dados_plot.index, dados_plot['MM_10'],
                    linewidth=1, alpha=0.7, label='MM 10', color='orange')
        if 'MM_25' in dados_plot.columns:
            ax.plot(dados_plot.index, dados_plot['MM_25'],
                    linewidth=1, alpha=0.7, label='MM 25', color='green')

        # Marcar o sinal no último dia
        ultimo_dia = dados_plot.index[-1]
        if sinal == 'COMPRA':
            ax.scatter(ultimo_dia, preco_atual, color='green', s=200,
                      marker='^', label=f'COMPRA ({predicao:+.2f}%)', zorder=5)
        elif sinal == 'VENDA':
            ax.scatter(ultimo_dia, preco_atual, color='red', s=200,
                      marker='v', label=f'VENDA ({predicao:+.2f}%)', zorder=5)

        # Configurações do gráfico
        ax.set_title(f'{ticker} - Estimador LightGBM\n'
                    f'Predição: {predicao:+.2f}% | Sinal: {sinal} | Preço: R$ {preco_atual:.2f}',
                    fontsize=14, fontweight='bold')
        ax.set_xlabel('Data', fontsize=12)
        ax.set_ylabel('Preço (R$)', fontsize=12)
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)

        # Rotacionar labels do eixo x
        plt.xticks(rotation=45)
        plt.tight_layout()

        # Salvar gráfico
        plt.savefig(os.path.join(individual_dir, f'{ticker}_sinal.png'),
                    dpi=300, bbox_inches='tight')
        plt.close()

    print(f"   ✅ {len(acoes_para_plotar)} gráficos individuais salvos em: {individual_dir}/")

def salvar_resultados_estimador(acoes_com_predicoes, resultados_modelo, feature_cols):
    """
    Salva os resultados das predições em arquivo CSV
    """
    print(f"\n💾 Salvando resultados das predições...")

    # Criar diretório de resultados
    results_dir = 'results'
    os.makedirs(results_dir, exist_ok=True)

    # Preparar dados para salvar
    resultados_lista = []
    for ticker, info in acoes_com_predicoes.items():
        resultados_lista.append({
            'Ticker': ticker,
            'Data': info['ultimo_dia'].strftime('%Y-%m-%d'),
            'Preco_Atual': info['preco_atual'],
            'Predicao_Variacao': info['predicao'],
            'Sinal': info['sinal']
        })

    # Criar DataFrame e salvar
    df_resultados = pd.DataFrame(resultados_lista)
    df_resultados = df_resultados.sort_values('Predicao_Variacao', ascending=False)

    # Salvar CSV
    csv_path = os.path.join(results_dir, 'lightgbm_estimador_sinais.csv')
    df_resultados.to_csv(csv_path, index=False)

    print(f"   ✅ Resultados salvos em: {csv_path}")
    print(f"   📊 Total de ações analisadas: {len(df_resultados)}")

    # Mostrar resumo
    resumo_sinais = df_resultados['Sinal'].value_counts()
    for sinal, count in resumo_sinais.items():
        print(f"   • {sinal}: {count} ações")

    return csv_path

def main():
    """
    Função principal do estimador LightGBM
    """
    print("🎯 ESTIMADOR LIGHTGBM - PREDIÇÃO DE VALORES DA MÉDIA OHLC")
    print("=" * 80)

    # Configurar ambiente
    setup_environment()

    # Carregar ações diversificadas
    print("\n📋 Carregando ações diversificadas...")
    todas_acoes = carregar_acoes_diversificadas()

    if not todas_acoes:
        print("❌ Nenhuma ação encontrada!")
        return

    # Baixar dados de todas as ações
    print(f"\n📊 Baixando dados de {len(todas_acoes)} ações...")
    # Converter formato de tuplas para compatibilidade com cache unificado
    todas_acoes_com_origem = [(ticker, nome, 'diversificacao') for ticker, nome in todas_acoes]
    acoes_dados = baixar_dados_todas_acoes_unificado(todas_acoes_com_origem)

    if not acoes_dados:
        print("❌ Falha ao baixar dados das ações!")
        return

    # Calcular features para todas as ações
    print(f"\n🔧 Calculando features para {len(acoes_dados)} ações...")
    for ticker, dados in acoes_dados.items():
        if dados is not None:
            acoes_dados[ticker] = calcular_features_e_sinais(dados, ticker)

    # Verificar se modelo estimador já foi treinado hoje (considerando force_training)
    modelo_dir = 'results/models/lightgbm_estimador_analysis'
    modelo_nome = 'modelo_estimador.pkl'
    os.makedirs(modelo_dir, exist_ok=True)

    force_training = config.get('lightgbm', {}).get('cache', {}).get('force_training', False)
    if force_training:
        print(f"🔄 Forçando treinamento do modelo estimador (force_training=True)")
        modelo_treinado_hoje = False
        resultados_modelo = None
        feature_cols = None
    else:
        print(f"\n🔍 Verificando modelo existente...")
        resultados_modelo, feature_cols = carregar_modelo_estimador()
        modelo_treinado_hoje = resultados_modelo is not None

    if not modelo_treinado_hoje:
        # Preparar dataset para regressão
        print(f"\n📈 Preparando dataset de regressão...")
        X, y, feature_cols, dataset_completo = preparar_dataset_regressao(acoes_dados)

        if X is None:
            print("❌ Erro ao preparar dataset")
            return

        # Treinar estimador
        print(f"\n🚀 Treinando estimador LightGBM...")
        resultados_modelo, feature_cols = treinar_estimador_lightgbm(X, y, feature_cols, dataset_completo)

        if resultados_modelo is None:
            print("❌ Erro no treinamento do estimador")
            return

        # Salvar modelo treinado
        print(f"\n💾 Salvando modelo estimador...")
        salvar_modelo_estimador(resultados_modelo, feature_cols)

    # Aplicar predições e gerar sinais
    print(f"\n🎯 Aplicando predições e gerando sinais...")
    acoes_com_predicoes = aplicar_predicoes_estimador(acoes_dados, resultados_modelo, feature_cols)

    # Mostrar recomendações (mesmo formato do XGBoost)
    imprimir_recomendacoes_estimador(acoes_com_predicoes)

    # Criar gráficos de desempenho
    if not modelo_treinado_hoje:
        print(f"\n📊 Criando gráficos de desempenho...")
        criar_graficos_desempenho_estimador(resultados_modelo, feature_cols)

    # Criar gráficos individuais de sinais
    criar_graficos_sinais_individuais(acoes_com_predicoes)

    # Salvar resultados
    print(f"\n💾 Salvando resultados...")
    salvar_resultados_estimador(acoes_com_predicoes, resultados_modelo, feature_cols)

    print(f"\n✅ Análise LightGBM concluída com sucesso!")
    print(f"📁 Resultados salvos em: results/")
    print(f"📊 Gráficos salvos em: results/figures/lightgbm_estimador_analysis/")

if __name__ == "__main__":
    main()
