#!/usr/bin/env python3
"""
Estimador LightGBM para Sinais de Trading
Replica exatamente a funcionalidade do estimador XGBoost usando LightGBM
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import lightgbm as lgb
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import os
import pickle
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Adicionar o diretório src ao path para importar functions e config
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config_loader import config, setup_environment

# Importar cache otimizado se disponível
try:
    from cache_optimized import optimized_cache
    CACHE_OPTIMIZED_AVAILABLE = True
except ImportError:
    CACHE_OPTIMIZED_AVAILABLE = False

# Importar cache unificado se disponível
try:
    from cache_unified import unified_cache
    CACHE_UNIFIED_AVAILABLE = True
except ImportError:
    CACHE_UNIFIED_AVAILABLE = False

# Importar funções de features do módulo compartilhado
from features_xgboost import calcular_features_e_sinais

# Importar funções do classificador para reutilizar lógica de dados
from classificador_xgboost_sinais import (
    carregar_acoes_diversificadas,
    baixar_dados_todas_acoes_unificado,
    baixar_dados_acao_otimizado,
    baixar_dados_acao,
    verificar_status_cache,
    limpar_cache_historico
)

# Configurações globais
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
RANDOM_SEED = 42
np.random.seed(RANDOM_SEED)

def preparar_dataset_regressao(acoes_dados):
    """
    Prepara dataset combinado de todas as ações para treinamento de regressão
    Preserva o índice de data para divisão temporal
    Target: variação percentual da média OHLC em relação ao dia anterior
    """
    datasets = []

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 50:
            # Adicionar coluna do ticker para identificação
            dados_copy = dados.copy()
            dados_copy['Ticker'] = ticker
            # Resetar índice para preservar as datas como coluna
            dados_copy = dados_copy.reset_index()
            datasets.append(dados_copy)

    if not datasets:
        print("❌ Nenhum dataset válido encontrado")
        return None, None, None, None

    # Combinar todos os datasets
    dataset_completo = pd.concat(datasets, ignore_index=True)
    dataset_completo['Date'] = pd.to_datetime(dataset_completo['Date'])
    dataset_completo = dataset_completo.set_index('Date')

    print(f"📊 Dataset combinado criado:")
    print(f"   • Total de registros: {len(dataset_completo)}")
    print(f"   • Ações incluídas: {dataset_completo['Ticker'].nunique()}")
    print(f"   • Período: {dataset_completo.index.min().strftime('%Y-%m-%d')} a {dataset_completo.index.max().strftime('%Y-%m-%d')}")

    # Calcular features básicas (sem usar Close do dia atual)
    basic_features = ['Volume', 'Spread', 'Volatilidade']

    # Calcular features econométricas lagged (versões históricas)
    econometric_features_lagged = []
    econometric_lags = config.get('lightgbm.features.econometric_lags')
    
    # Features econométricas base
    econometric_base = ['MFI', 'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line',
                       'Parkinson_Volatility', 'EMV', 'EMV_MA', 'VO', 'High_Max_50', 'Low_Min_50']
    
    # Adicionar lags das features econométricas
    for feature in econometric_base:
        for lag in range(1, econometric_lags + 1):
            econometric_features_lagged.append(f'{feature}_Lag_{lag}')

    # Combinar apenas features lagged e temporais (SEM features do dia atual)
    feature_cols = basic_features + econometric_features_lagged

    # NOVO TARGET: Variação percentual da média OHLC em relação ao dia anterior
    # Calcular variação percentual: (valor_hoje - valor_ontem) / valor_ontem * 100
    dataset_completo = dataset_completo.sort_values(['Ticker', 'Date'])
    dataset_completo['Media_OHLC_Anterior'] = dataset_completo.groupby('Ticker')['Media_OHLC'].shift(1)

    # Calcular variação percentual
    dataset_completo['Target_PctChange'] = (
        (dataset_completo['Media_OHLC'] - dataset_completo['Media_OHLC_Anterior']) /
        dataset_completo['Media_OHLC_Anterior'] * 100
    )

    # Verificar se todas as features existem no dataset
    features_existentes = [col for col in feature_cols if col in dataset_completo.columns]
    features_faltando = [col for col in feature_cols if col not in dataset_completo.columns]

    if features_faltando:
        print(f"   ⚠️ Features não encontradas: {len(features_faltando)}")
        print(f"   📋 Usando apenas features existentes: {len(features_existentes)}")
        feature_cols = features_existentes

    X = dataset_completo[feature_cols]
    y = dataset_completo['Target_PctChange']

    # Remover registros com NaN no target ou nas features
    mask_validos = ~y.isna() & ~X.isna().any(axis=1)
    X_filtrado = X[mask_validos].copy()
    y_filtrado = y[mask_validos].copy()
    dataset_filtrado = dataset_completo[mask_validos].copy()

    print(f"📊 Dataset de regressão preparado:")
    print(f"   • Total de registros: {len(y_filtrado)}")
    print(f"   • Features utilizadas: {len(feature_cols)}")
    print(f"   • Target: Variação % OHLC (min: {y_filtrado.min():.2f}%, max: {y_filtrado.max():.2f}%, média: {y_filtrado.mean():.2f}%)")

    return X_filtrado, y_filtrado, feature_cols, dataset_filtrado

def treinar_estimador_lightgbm(X, y, feature_cols, dataset_completo=None):
    """
    Treina um estimador LightGBM otimizado para predizer variação percentual da média OHLC
    Inclui seleção de features e otimização de hiperparâmetros se configurado
    """
    # Usar configurações do LightGBM
    use_scaler = config.get('lightgbm.use_standard_scaler')
    lgb_params = config.get('lightgbm.model_params').copy()

    # Configurar parâmetros específicos do LightGBM
    lgb_params.update({
        'objective': 'regression',
        'metric': 'rmse',
        'boosting_type': 'gbdt',
        'verbose': -1,  # Silencioso
        'deterministic': True,  # Para reprodutibilidade
        'force_row_wise': True,  # Para reprodutibilidade
        'feature_fraction': lgb_params.get('colsample_bytree', 1.0),
        'bagging_fraction': lgb_params.get('subsample', 1.0),
        'bagging_freq': 0 if lgb_params.get('subsample', 1.0) == 1.0 else 1,
    })

    # Remover parâmetros que não existem no LightGBM
    lgb_params.pop('colsample_bytree', None)
    lgb_params.pop('subsample', None)

    # Remover early_stopping_rounds dos parâmetros do modelo (será usado no fit)
    early_stopping_rounds = lgb_params.pop('early_stopping_rounds', None)

    # Divisão temporal dos dados
    use_temporal_split = config.get('lightgbm.temporal_split.use_temporal_split')
    test_size = config.get('lightgbm.test_size')

    if use_temporal_split and dataset_completo is not None:
        # Divisão temporal: últimos X% dos dados para teste
        dataset_sorted = dataset_completo.sort_index()
        split_date = dataset_sorted.index[int(len(dataset_sorted) * (1 - test_size))]
        
        train_mask = X.index < split_date
        test_mask = X.index >= split_date
        
        X_train, X_test = X[train_mask], X[test_mask]
        y_train, y_test = y[train_mask], y[test_mask]
        
        print(f"   • Divisão temporal: treino até {split_date.strftime('%Y-%m-%d')}")
    else:
        # Divisão aleatória
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=RANDOM_SEED
        )
        print(f"   • Divisão aleatória: {test_size*100:.0f}% para teste")

    print(f"   • Treino: {len(X_train)} registros")
    print(f"   • Teste: {len(X_test)} registros")

    # Normalizar features se configurado
    if use_scaler:
        # StandardScaler com seed fixa para reprodutibilidade
        scaler = StandardScaler()

        # Remover duplicatas no índice se existirem
        if X_train.index.duplicated().any():
            print(f"   🔧 Removendo {X_train.index.duplicated().sum()} índices duplicados do treino")
            X_train = X_train[~X_train.index.duplicated(keep='last')]
            y_train = y_train[~y_train.index.duplicated(keep='last')]

        if X_test.index.duplicated().any():
            print(f"   🔧 Removendo {X_test.index.duplicated().sum()} índices duplicados do teste")
            X_test = X_test[~X_test.index.duplicated(keep='last')]
            y_test = y_test[~y_test.index.duplicated(keep='last')]

        # Garantir ordem determinística dos dados antes do fit
        X_train_sorted = X_train.sort_index()
        y_train_sorted = y_train.reindex(X_train_sorted.index)

        X_train_scaled = scaler.fit_transform(X_train_sorted)
        X_test_scaled = scaler.transform(X_test)
        X_train_scaled = pd.DataFrame(X_train_scaled, columns=feature_cols, index=X_train_sorted.index)
        X_test_scaled = pd.DataFrame(X_test_scaled, columns=feature_cols, index=X_test.index)

        # Restaurar ordem original
        X_train_scaled = X_train_scaled.reindex(X_train.index)
        y_train = y_train_sorted.reindex(X_train.index)
    else:
        scaler = None
        X_train_scaled = X_train
        X_test_scaled = X_test

    # NOVO: Calcular pesos para o treinamento se configurado
    use_weighted_training = config.get('lightgbm.estimator.use_weighted_training')
    weights = None
    if use_weighted_training:
        print("\n⚖️  Usando pesos no treinamento para amenizar o impacto de grandes variações.")
        # Pesos inversamente proporcionais ao módulo do target para amenizar o efeito de grandes variações
        # Adicionar um pequeno epsilon para evitar divisão por zero
        weights = 1/(np.exp(-(y_train**2)/8)+1e-6)
        
        # Normalizar os pesos para que a soma não altere drasticamente a magnitude do gradiente
        weights = weights/np.mean(weights)

    # Seleção de features se configurado
    use_feature_selection = config.get('lightgbm.estimator.advanced_features.use_feature_selection', False)
    if use_feature_selection:
        print("\n🔍 Realizando seleção de features...")
        from sklearn.feature_selection import SelectFromModel

        # Treinar modelo preliminar para seleção de features
        modelo_preliminar = lgb.LGBMRegressor(n_estimators=50, random_state=42, verbose=-1)
        modelo_preliminar.fit(X_train_scaled, y_train)

        # Selecionar features importantes
        threshold = config.get('lightgbm.estimator.advanced_features.feature_selection_threshold', 0.001)
        selector = SelectFromModel(modelo_preliminar, threshold=threshold)
        X_train_selected = selector.fit_transform(X_train_scaled, y_train)
        X_test_selected = selector.transform(X_test_scaled)

        # Atualizar lista de features
        feature_mask = selector.get_support()
        feature_cols_selected = [feature_cols[i] for i in range(len(feature_cols)) if feature_mask[i]]

        print(f"   • Features selecionadas: {len(feature_cols_selected)}/{len(feature_cols)}")

        X_train_scaled = pd.DataFrame(X_train_selected, columns=feature_cols_selected, index=X_train_scaled.index)
        X_test_scaled = pd.DataFrame(X_test_selected, columns=feature_cols_selected, index=X_test_scaled.index)
        feature_cols = feature_cols_selected

    # Verificar se deve usar otimização de hiperparâmetros
    use_hyperparameter_tuning = config.get('lightgbm.estimator.hyperparameter_tuning.enabled', False)

    if use_hyperparameter_tuning:
        print("\n🎯 Otimizando hiperparâmetros...")
        modelo, best_params = otimizar_hiperparametros_lightgbm(X_train_scaled, y_train, weights)
        print(f"   • Melhores parâmetros encontrados:")
        for param, value in best_params.items():
            print(f"     - {param}: {value}")
    else:
        # Treinar estimador LightGBM com parâmetros configurados
        print("\n🚀 Treinando estimador LightGBM...")
        print(f"   • Target: Variação % OHLC (regressão)")
        print(f"   • Função de perda: RMSE")
        print(f"   • Parâmetros otimizados: n_estimators={lgb_params.get('n_estimators')}, max_depth={lgb_params.get('max_depth')}, learning_rate={lgb_params.get('learning_rate')}")

        modelo = lgb.LGBMRegressor(**lgb_params)

    # Treinar o modelo final
    fit_params = {'sample_weight': weights} if weights is not None else {}

    # Adicionar early stopping se configurado
    if early_stopping_rounds and len(X_train_scaled) > 1000:  # Só usar early stopping com dados suficientes
        # Dividir treino em treino/validação para early stopping
        from sklearn.model_selection import train_test_split
        X_train_fit, X_val_fit, y_train_fit, y_val_fit = train_test_split(
            X_train_scaled, y_train, test_size=0.1, random_state=42
        )

        fit_params.update({
            'eval_set': [(X_val_fit, y_val_fit)],
            'eval_metric': 'rmse',
            'callbacks': [lgb.early_stopping(early_stopping_rounds, verbose=False)]
        })

        if weights is not None:
            weights_train, weights_val = train_test_split(weights, test_size=0.1, random_state=42)
            fit_params['sample_weight'] = weights_train
            fit_params['eval_sample_weight'] = [weights_val]

        modelo.fit(X_train_fit, y_train_fit, **fit_params)
    else:
        modelo.fit(X_train_scaled, y_train, **fit_params)

    # Fazer predições
    y_pred = modelo.predict(X_test_scaled)

    # Calcular métricas de regressão
    mse = mean_squared_error(y_test, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)

    print(f"   • RMSE: {rmse:.4f}")
    print(f"   • MAE: {mae:.4f}")
    print(f"   • R²: {r2:.4f}")

    # Comparar com baseline (predição da média)
    y_baseline = np.full_like(y_test, y_train.mean())
    r2_baseline = r2_score(y_test, y_baseline)
    improvement = ((r2 - r2_baseline) / abs(r2_baseline)) * 100 if r2_baseline != 0 else 0
    print(f"   • R² baseline (média): {r2_baseline:.4f}")
    print(f"   • Melhoria sobre baseline: {improvement:+.1f}%")

    resultados = {
        'modelo': modelo,
        'scaler': scaler,
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'r2_baseline': r2_baseline,
        'improvement': improvement,
        'y_test': y_test,
        'y_pred': y_pred,
        'feature_importance': modelo.feature_importances_,
        'X_test': X_test_scaled,
        'X_train': X_train_scaled,
        'y_train': y_train,
        'best_params': best_params if use_hyperparameter_tuning else lgb_params,
        'feature_cols_used': feature_cols  # Salvar features usadas no treinamento
    }

    return resultados, feature_cols


def otimizar_hiperparametros_lightgbm(X_train, y_train, weights=None):
    """
    Otimiza hiperparâmetros do LightGBM usando Optuna ou Grid Search
    """
    method = config.get('lightgbm.estimator.hyperparameter_tuning.method', 'optuna')

    if method == 'optuna':
        try:
            import optuna
            from optuna.samplers import TPESampler

            def objective(trial):
                # Definir espaço de busca
                search_space = config.get('lightgbm.estimator.hyperparameter_tuning.search_space')

                params = {
                    'n_estimators': trial.suggest_categorical('n_estimators', search_space['n_estimators']),
                    'max_depth': trial.suggest_categorical('max_depth', search_space['max_depth']),
                    'learning_rate': trial.suggest_categorical('learning_rate', search_space['learning_rate']),
                    'num_leaves': trial.suggest_categorical('num_leaves', search_space['num_leaves']),
                    'min_child_samples': trial.suggest_categorical('min_child_samples', search_space['min_child_samples']),
                    'reg_alpha': trial.suggest_categorical('reg_alpha', search_space['reg_alpha']),
                    'reg_lambda': trial.suggest_categorical('reg_lambda', search_space['reg_lambda']),
                    'subsample': trial.suggest_categorical('subsample', search_space['subsample']),
                    'colsample_bytree': trial.suggest_categorical('colsample_bytree', search_space['colsample_bytree']),
                    'objective': 'regression',
                    'metric': 'rmse',
                    'boosting_type': 'gbdt',
                    'random_state': 42,
                    'verbose': -1,
                    'deterministic': True,
                    'force_row_wise': True,
                    'feature_fraction': params.get('colsample_bytree', 1.0),
                    'bagging_fraction': params.get('subsample', 1.0),
                    'bagging_freq': 1 if params.get('subsample', 1.0) < 1.0 else 0,
                }

                # Validação cruzada
                from sklearn.model_selection import cross_val_score
                cv_folds = config.get('lightgbm.estimator.hyperparameter_tuning.cv_folds', 3)

                modelo = lgb.LGBMRegressor(**params)
                scores = cross_val_score(modelo, X_train, y_train, cv=cv_folds,
                                       scoring='r2', fit_params={'sample_weight': weights} if weights is not None else None)

                return scores.mean()

            # Configurar estudo Optuna
            n_trials = config.get('lightgbm.estimator.hyperparameter_tuning.n_trials', 100)
            sampler = TPESampler(seed=42)
            study = optuna.create_study(direction='maximize', sampler=sampler)

            print(f"   • Executando {n_trials} tentativas com Optuna...")
            study.optimize(objective, n_trials=n_trials, show_progress_bar=False)

            best_params = study.best_params
            best_score = study.best_value
            print(f"   • Melhor R²: {best_score:.4f}")

            # Adicionar parâmetros fixos
            best_params.update({
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'random_state': 42,
                'verbose': -1,
                'deterministic': True,
                'force_row_wise': True,
                'feature_fraction': best_params.get('colsample_bytree', 1.0),
                'bagging_fraction': best_params.get('subsample', 1.0),
                'bagging_freq': 1 if best_params.get('subsample', 1.0) < 1.0 else 0,
            })

            # Remover parâmetros duplicados
            best_params.pop('colsample_bytree', None)
            best_params.pop('subsample', None)

            modelo_otimizado = lgb.LGBMRegressor(**best_params)
            return modelo_otimizado, best_params

        except ImportError:
            print("   ⚠️ Optuna não instalado, usando Grid Search...")
            method = 'grid_search'

    if method == 'grid_search':
        from sklearn.model_selection import GridSearchCV

        # Espaço de busca reduzido para Grid Search
        param_grid = {
            'n_estimators': [200, 500],
            'max_depth': [6, 8, 10],
            'learning_rate': [0.05, 0.1],
            'num_leaves': [100, 200],
            'reg_alpha': [0.0, 0.1],
            'reg_lambda': [0.0, 0.1]
        }

        modelo_base = lgb.LGBMRegressor(
            objective='regression',
            metric='rmse',
            boosting_type='gbdt',
            random_state=42,
            verbose=-1
        )

        cv_folds = config.get('lightgbm.estimator.hyperparameter_tuning.cv_folds', 3)
        grid_search = GridSearchCV(
            modelo_base, param_grid, cv=cv_folds, scoring='r2',
            n_jobs=-1, verbose=0
        )

        print(f"   • Executando Grid Search com {cv_folds} folds...")
        fit_params = {'sample_weight': weights} if weights is not None else {}
        grid_search.fit(X_train, y_train, **fit_params)

        best_params = grid_search.best_params_
        best_score = grid_search.best_score_
        print(f"   • Melhor R²: {best_score:.4f}")

        return grid_search.best_estimator_, best_params

    # Fallback: usar parâmetros padrão
    lgb_params = config.get('lightgbm.model_params').copy()
    modelo = lgb.LGBMRegressor(**lgb_params)
    return modelo, lgb_params


def criar_grafico_importancia_features(resultados_modelo, feature_cols):
    """
    Cria gráfico de importância das features do estimador LightGBM
    """
    print(f"📊 Criando gráfico de importância das features...")

    # Criar diretório de figuras
    figures_dir = 'results/figures/lightgbm_estimador_analysis'
    os.makedirs(figures_dir, exist_ok=True)

    # Preparar dados de importância
    feature_importance = resultados_modelo['feature_importance']
    feature_importance_df = pd.DataFrame({
        'feature': feature_cols,
        'importance': feature_importance
    }).sort_values('importance', ascending=True)

    # Mostrar apenas top 20 features
    feature_importance_df = feature_importance_df.tail(20)

    # Criar gráfico
    plt.figure(figsize=(12, 10))
    ax = plt.gca()

    bars = ax.barh(range(len(feature_importance_df)), feature_importance_df['importance'],
                   color='lightcoral', alpha=0.8, edgecolor='darkred', linewidth=0.5)

    # Configurações do gráfico
    ax.set_yticks(range(len(feature_importance_df)))
    ax.set_yticklabels(feature_importance_df['feature'], fontsize=9)
    ax.set_xlabel('Importância', fontsize=12, fontweight='bold')
    ax.set_title('Top 20 Features Mais Importantes - Estimador LightGBM\n' +
                f'Total de Features: {len(feature_cols)}',
                fontsize=14, fontweight='bold', pad=20)

    # Adicionar valores nas barras
    for i, bar in enumerate(bars):
        width = bar.get_width()
        ax.text(width + 0.001, bar.get_y() + bar.get_height()/2,
                f'{width:.3f}', ha='left', va='center', fontsize=8)

    ax.grid(True, alpha=0.3, axis='x')
    plt.tight_layout()

    # Salvar gráfico
    plt.savefig(os.path.join(figures_dir, 'feature_importance_estimador.png'),
                dpi=300, bbox_inches='tight')
    plt.close()

    print(f"📊 Gráfico de importância das features salvo")

def criar_graficos_desempenho_estimador(resultados_modelo, feature_cols):
    """
    Cria gráficos para avaliar o desempenho do estimador LightGBM
    """
    print(f"\n📊 Criando gráficos de desempenho do estimador...")

    # Criar diretório de figuras
    figures_dir = 'results/figures/lightgbm_estimador_analysis'
    os.makedirs(figures_dir, exist_ok=True)

    # Limpar figuras antigas
    if config.get('output.clean_old_files'):
        for arquivo in os.listdir(figures_dir):
            if arquivo.endswith('.png'):
                os.remove(os.path.join(figures_dir, arquivo))
        print(f"🗑️ Figuras antigas removidas de {figures_dir}")

    # 1. Gráfico de importância das features
    criar_grafico_importancia_features(resultados_modelo, feature_cols)

    # 2. Gráfico de predições vs valores reais
    y_test = resultados_modelo['y_test']
    y_pred = resultados_modelo['y_pred']
    rmse = resultados_modelo['rmse']
    mae = resultados_modelo['mae']
    r2 = resultados_modelo['r2']

    plt.figure(figsize=(12, 8))
    ax = plt.gca()

    # Scatter plot
    ax.scatter(y_test, y_pred, alpha=0.6, s=20, color='steelblue', label='Predições')

    # Linha de referência (predição perfeita)
    min_val = min(y_test.min(), y_pred.min())
    max_val = max(y_test.max(), y_pred.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='Predição Perfeita')

    # Configurações do gráfico
    ax.set_xlabel('Variação % Real', fontsize=12)
    ax.set_ylabel('Variação % Estimada', fontsize=12)
    ax.set_title('Estimador LightGBM: Variação % Real vs Estimada\n' +
                f'R² = {r2:.4f} | RMSE = {rmse:.2f}% | MAE = {mae:.2f}%',
                fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)

    # Adicionar estatísticas no gráfico
    ax.text(0.05, 0.95, f'Pontos: {len(y_test)}\nR²: {r2:.4f}\nRMSE: {rmse:.2f}\nMAE: {mae:.2f}',
            transform=ax.transAxes, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'valores_reais_vs_estimados.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 2. Gráfico: Série Temporal dos Últimos 100 Pontos
    fig, ax = plt.subplots(figsize=(14, 8))

    # Pegar últimos 100 pontos para visualização
    n_pontos = min(100, len(y_test))
    indices = range(len(y_test) - n_pontos, len(y_test))

    ax.plot(indices, y_test.iloc[-n_pontos:], 'o-', color='blue', linewidth=2,
            markersize=4, label='Valores Reais', alpha=0.8)
    ax.plot(indices, y_pred[-n_pontos:], 's-', color='red', linewidth=2,
            markersize=4, label='Valores Estimados', alpha=0.8)

    ax.set_xlabel('Índice da Amostra', fontsize=12)
    ax.set_ylabel('Variação % da Média OHLC', fontsize=12)
    ax.set_title(f'Estimador LightGBM: Série Temporal (Últimos {n_pontos} Pontos)\n' +
                f'Comparação entre Variações % Reais e Estimadas', fontsize=14, fontweight='bold')
    ax.legend(loc='upper left')
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'serie_temporal_comparacao.png'), dpi=300, bbox_inches='tight')
    plt.close()

    print(f"📊 Gráfico de predições vs real salvo")
    print(f"📊 Gráfico de série temporal salvo")

    # 3. Histograma dos resíduos
    residuos = y_test - y_pred

    plt.figure(figsize=(10, 6))
    ax = plt.gca()

    ax.hist(residuos, bins=50, alpha=0.7, color='lightgreen', edgecolor='darkgreen')
    ax.axvline(residuos.mean(), color='red', linestyle='--', linewidth=2,
               label=f'Média: {residuos.mean():.3f}')
    ax.axvline(0, color='black', linestyle='-', linewidth=1, alpha=0.5, label='Zero')

    ax.set_xlabel('Resíduos (Real - Predito)', fontsize=12)
    ax.set_ylabel('Frequência', fontsize=12)
    ax.set_title('Distribuição dos Resíduos - Estimador LightGBM', fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'distribuicao_residuos.png'),
                dpi=300, bbox_inches='tight')
    plt.close()

    print(f"📊 Gráfico de distribuição dos resíduos salvo")

def salvar_modelo_estimador(resultados_modelo, feature_cols):
    """
    Salva o modelo treinado e metadados
    """
    print(f"💾 Salvando modelo estimador LightGBM...")

    # Criar diretório de modelos
    models_dir = 'results/models/lightgbm_estimador_analysis'
    os.makedirs(models_dir, exist_ok=True)

    # Salvar modelo
    modelo_path = os.path.join(models_dir, 'modelo_estimador.pkl')
    with open(modelo_path, 'wb') as f:
        pickle.dump({
            'modelo': resultados_modelo['modelo'],
            'scaler': resultados_modelo['scaler'],
            'feature_cols': feature_cols,
            'feature_cols_used': resultados_modelo.get('feature_cols_used', feature_cols),  # Features realmente usadas
            'metricas': {
                'rmse': resultados_modelo['rmse'],
                'mae': resultados_modelo['mae'],
                'r2': resultados_modelo['r2'],
                'r2_baseline': resultados_modelo.get('r2_baseline', 0),
                'improvement': resultados_modelo.get('improvement', 0)
            },
            'best_params': resultados_modelo.get('best_params', {}),
            'data_treinamento': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }, f)

    # Salvar resumo do treinamento
    resumo_path = os.path.join(models_dir, 'resumo_treinamento.txt')
    with open(resumo_path, 'w', encoding='utf-8') as f:
        f.write("RESUMO DO TREINAMENTO LIGHTGBM - ESTIMADOR DE VARIAÇÃO\n")
        f.write("=" * 70 + "\n\n")
        f.write("MODELO ESTIMADOR:\n")
        f.write(f"  • Tipo: LightGBM Regressor\n")
        f.write(f"  • Target: Variação % OHLC (regressão)\n")
        f.write(f"  • Função de perda: RMSE\n")
        f.write(f"  • Features básicas: pct_change da média OHLC (variação percentual), Volume, Spread, Volatilidade\n")
        f.write(f"  • Features econométricas: Parkinson, MFI, EMV, Amihud, Roll Spread,\n")
        f.write(f"    Hurst, Vol/Volume, CMF, A/D Line, Volume Oscillator, High Max 50d, Low Min 50d (13 features)\n")
        f.write(f"  • Features lagged: {config.get('lightgbm.features.econometric_lags')} lags para cada feature econométrica\n")
        f.write(f"  • Total de features: {len(feature_cols)}\n")
        f.write(f"  • RMSE: {resultados_modelo['rmse']:.4f}\n")
        f.write(f"  • MAE: {resultados_modelo['mae']:.4f}\n")
        f.write(f"  • R²: {resultados_modelo['r2']:.4f}\n\n")
        f.write(f"DATA DO TREINAMENTO: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

    print(f"   ✅ Modelo salvo em: {modelo_path}")
    print(f"   ✅ Resumo salvo em: {resumo_path}")

def carregar_modelo_estimador():
    """
    Carrega modelo treinado se existir e estiver atualizado
    """
    models_dir = 'results/models/lightgbm_estimador_analysis'
    modelo_path = os.path.join(models_dir, 'modelo_estimador.pkl')

    if not os.path.exists(modelo_path):
        return None, None

    # Verificar se o modelo foi treinado hoje
    modelo_stat = os.path.getmtime(modelo_path)
    modelo_date = datetime.fromtimestamp(modelo_stat).date()
    hoje = datetime.now().date()

    if modelo_date != hoje:
        print(f"📅 Modelo foi treinado em {modelo_date}, retreinando...")
        return None, None

    try:
        with open(modelo_path, 'rb') as f:
            dados_modelo = pickle.load(f)

        print(f"✅ Modelo LightGBM carregado (treinado em {dados_modelo['data_treinamento']})")
        print(f"   • RMSE: {dados_modelo['metricas']['rmse']:.4f}")
        print(f"   • MAE: {dados_modelo['metricas']['mae']:.4f}")
        print(f"   • R²: {dados_modelo['metricas']['r2']:.4f}")
        if 'r2_baseline' in dados_modelo['metricas']:
            print(f"   • R² baseline: {dados_modelo['metricas']['r2_baseline']:.4f}")
            print(f"   • Melhoria: {dados_modelo['metricas']['improvement']:+.1f}%")

        resultados_modelo = {
            'modelo': dados_modelo['modelo'],
            'scaler': dados_modelo['scaler'],
            'rmse': dados_modelo['metricas']['rmse'],
            'mae': dados_modelo['metricas']['mae'],
            'r2': dados_modelo['metricas']['r2'],
            'r2_baseline': dados_modelo['metricas'].get('r2_baseline', 0),
            'improvement': dados_modelo['metricas'].get('improvement', 0),
            'best_params': dados_modelo.get('best_params', {}),
            'feature_cols_used': dados_modelo.get('feature_cols_used', dados_modelo['feature_cols'])
        }

        # Usar as features que foram realmente usadas no treinamento
        feature_cols_to_use = dados_modelo.get('feature_cols_used', dados_modelo['feature_cols'])
        return resultados_modelo, feature_cols_to_use

    except Exception as e:
        print(f"❌ Erro ao carregar modelo: {e}")
        return None, None

def aplicar_predicoes_estimador(acoes_dados, resultados_modelo, feature_cols):
    """
    Aplica as predições do estimador LightGBM aos dados de cada ação
    Converte predições de variação percentual de volta para valores OHLC
    Gera sinais de compra/venda baseados no valor estimado vs valor anterior
    """
    modelo = resultados_modelo['modelo']
    scaler = resultados_modelo['scaler']
    threshold = config.get('lightgbm.features.pct_threshold')  # Usar mesmo threshold do classificador

    acoes_com_predicoes = {}

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 0:
            dados_copy = dados.copy()

            # Verificar se todas as features existem
            features_disponiveis = [col for col in feature_cols if col in dados_copy.columns]
            if len(features_disponiveis) != len(feature_cols):
                print(f"     ⚠️ Features faltando para {ticker}: {len(features_disponiveis)}/{len(feature_cols)}")
                # Usar apenas as features disponíveis
                feature_cols_disponiveis = features_disponiveis
            else:
                feature_cols_disponiveis = feature_cols

            # Pegar apenas o último dia disponível (sem NaN nas features essenciais)
            X_all = dados_copy[feature_cols_disponiveis].dropna()
            if len(X_all) == 0:
                continue

            # Usar apenas o último dia para predição (dia atual)
            X_ultimo_dia = X_all.tail(1)

            # Aplicar scaler se necessário
            if scaler is not None:
                # Verificar se as features do scaler coincidem com as disponíveis
                try:
                    X_scaled = scaler.transform(X_ultimo_dia)
                    X_scaled = pd.DataFrame(X_scaled, columns=feature_cols_disponiveis, index=X_ultimo_dia.index)
                except ValueError as e:
                    print(f"     ⚠️ Erro no scaler para {ticker}: {e}")
                    # Se houver incompatibilidade, usar dados sem normalização
                    X_scaled = X_ultimo_dia
            else:
                X_scaled = X_ultimo_dia

            # Fazer predição da VARIAÇÃO PERCENTUAL (do dia anterior para o dia estimado)
            pct_change_estimado_modelo = modelo.predict(X_scaled)[0]

            # Obter valor do dia ATUAL (HOJE) para a decisão
            preco_atual = dados_copy['Media_OHLC'].iloc[-1]

            # CORREÇÃO: A base para a predição de amanhã deve ser o preço de HOJE.
            # O modelo aprende a prever a variação do dia seguinte, então aplicamos
            # essa variação ao preço mais recente conhecido.
            valor_base_para_predicao = preco_atual

            # CONVERTER DE VOLTA PARA VALOR OHLC ESTIMADO
            # Se pct_change_estimado_modelo = 5%, e preco_atual = 10, então valor_estimado = 10 * (1 + 0.05) = 10.5
            if valor_base_para_predicao > 0:
                valor_estimado = valor_base_para_predicao * (1 + pct_change_estimado_modelo / 100)
            else:
                valor_estimado = 0

            # [NOVA LÓGICA] Calcular a variação percentual para a DECISÃO e EXIBIÇÃO
            # Compara o valor estimado com o preço ATUAL
            if preco_atual > 0:
                pct_change_final = ((valor_estimado / preco_atual) - 1) * 100
            else:
                pct_change_final = 0.0

            # Gerar sinais baseados no threshold, usando a nova variação percentual
            if pct_change_final > threshold:
                sinal_compra = 1
                sinal_venda = 0
                sinal_tipo = "COMPRA"
            elif pct_change_final < -threshold:
                sinal_compra = 0
                sinal_venda = 1
                sinal_tipo = "VENDA"
            else:
                sinal_compra = 0
                sinal_venda = 0
                sinal_tipo = "SEM_SINAL"

            # Adicionar predições aos dados (apenas para o último dia)
            dados_copy.loc[X_ultimo_dia.index, 'Valor_Estimado'] = valor_estimado
            # Salvar a variação final, que é mais intuitiva para o usuário
            dados_copy.loc[X_ultimo_dia.index, 'Pct_Change_Estimado'] = pct_change_final
            dados_copy.loc[X_ultimo_dia.index, 'Pred_Compra'] = sinal_compra
            dados_copy.loc[X_ultimo_dia.index, 'Pred_Venda'] = sinal_venda
            dados_copy.loc[X_ultimo_dia.index, 'Sinal_Tipo'] = sinal_tipo

            # Preencher NaN com valores padrão
            dados_copy['Valor_Estimado'] = dados_copy['Valor_Estimado'].fillna(0.0)
            dados_copy['Pct_Change_Estimado'] = dados_copy['Pct_Change_Estimado'].fillna(0.0)
            dados_copy['Pred_Compra'] = dados_copy['Pred_Compra'].fillna(0).astype(int)
            dados_copy['Pred_Venda'] = dados_copy['Pred_Venda'].fillna(0).astype(int)
            dados_copy['Sinal_Tipo'] = dados_copy['Sinal_Tipo'].fillna("SEM_SINAL")

            acoes_com_predicoes[ticker] = dados_copy

    print(f"   ✅ Predições aplicadas a {len(acoes_com_predicoes)} ações")
    print(f"   📊 Modelo prediz variação % e converte para valor OHLC")
    print(f"   📊 Threshold utilizado: {threshold}% (mesmo do classificador)")

    return acoes_com_predicoes


def carregar_carteira_atual():
    """
    Carrega a carteira atual do arquivo carteira.csv
    Retorna um dicionário com ticker -> quantidade atual
    """
    try:
        carteira_df = pd.read_csv('carteira.csv')

        # Calcular posição atual de cada ticker
        carteira_atual = {}
        for _, row in carteira_df.iterrows():
            ticker = row['ticker']
            quantidade = row['quantidade']

            if ticker in carteira_atual:
                carteira_atual[ticker] += quantidade
            else:
                carteira_atual[ticker] = quantidade

        # Filtrar apenas tickers com quantidade > 0
        carteira_atual = {ticker: qtd for ticker, qtd in carteira_atual.items() if qtd > 0}

        print(f"📋 Carteira atual carregada: {len(carteira_atual)} posições ativas")
        for ticker, qtd in carteira_atual.items():
            ticker_clean = ticker.replace('.SA', '')
            print(f"   • {ticker_clean}: {qtd} ações")

        return carteira_atual

    except FileNotFoundError:
        print(f"⚠️ Arquivo carteira.csv não encontrado - mostrando todos os sinais de venda")
        return {}
    except Exception as e:
        print(f"⚠️ Erro ao carregar carteira: {e} - mostrando todos os sinais de venda")
        return {}


def imprimir_recomendacoes_estimador(acoes_com_predicoes):
    """
    Imprime recomendações de compra e venda baseadas no estimador
    Filtra sinais de venda apenas para ações na carteira atual
    """
    print(f"\n" + "="*80)
    print(f"🎯 RECOMENDAÇÕES DE TRADING - ESTIMADOR LIGHTGBM")
    print(f"="*80)

    # Obter data mais recente dos dados
    data_mais_recente = None
    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ultima_data = dados.index[-1]
            if data_mais_recente is None or ultima_data > data_mais_recente:
                data_mais_recente = ultima_data

    if data_mais_recente:
        print(f"📅 Análise baseada nos dados até: {data_mais_recente.strftime('%d/%m/%Y (%A)')}")
        print(f"🤖 Sinais baseados nas estimativas do modelo LightGBM (regressão)")
        print(f"🎯 Threshold: {config.get('lightgbm.features.pct_threshold')}%")

    # Carregar carteira atual para filtrar sinais de venda
    carteira_atual = carregar_carteira_atual()

    # Coletar sinais
    sinais_compra = []
    sinais_venda = []

    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ticker_clean = ticker.replace('.SA', '')
            ultimo_dia = dados.tail(1).iloc[0]

            # Verificar se há sinal de compra
            if ultimo_dia.get('Pred_Compra', 0) == 1:
                sinais_compra.append({
                    'ticker_clean': ticker_clean,
                    'nome': ticker_clean,  # Nome simplificado
                    'preco': ultimo_dia.get('Media_OHLC', 0),
                    'valor_estimado': ultimo_dia.get('Valor_Estimado', 0),
                    'pct_change_estimado': ultimo_dia.get('Pct_Change_Estimado', 0),
                    'volume': ultimo_dia.get('Volume', 0),
                    'volatilidade': ultimo_dia.get('Volatilidade', 0),
                    'spread': ultimo_dia.get('Spread', 0),
                    'data': ultimo_dia.name
                })

            elif ultimo_dia.get('Pred_Venda', 0) == 1:
                # FILTRO: Apenas mostrar sinais de venda para ações na carteira
                if ticker in carteira_atual and carteira_atual[ticker] > 0:
                    sinais_venda.append({
                        'ticker_clean': ticker_clean,
                        'nome': ticker_clean,  # Nome simplificado
                        'preco': ultimo_dia.get('Media_OHLC', 0),
                        'valor_estimado': ultimo_dia.get('Valor_Estimado', 0),
                        'pct_change_estimado': ultimo_dia.get('Pct_Change_Estimado', 0),
                        'volume': ultimo_dia.get('Volume', 0),
                        'volatilidade': ultimo_dia.get('Volatilidade', 0),
                        'spread': ultimo_dia.get('Spread', 0),
                        'data': ultimo_dia.name,
                        'quantidade_carteira': carteira_atual[ticker]
                    })

    # Ordenar sinais
    sinais_venda.sort(key=lambda x: x['pct_change_estimado'])  # Menor variação primeiro (mais negativa)
    sinais_compra.sort(key=lambda x: x['pct_change_estimado'], reverse=True)  # Maior variação primeiro

    # Mostrar sinais de VENDA primeiro (apenas ações na carteira)
    if sinais_venda:
        print(f"\n🔴 SINAIS DE VENDA ({len(sinais_venda)} ações na carteira):")
        print(f"{'Ação':<8} {'Preço':<10} {'Est.':<10} {'Var%':<8} {'Qtd':<6} {'Vol':<12} {'Spread':<8}")
        print(f"{'-'*70}")
        for sinal in sinais_venda:
            print(f"{sinal['ticker_clean']:<8} "
                  f"R${sinal['preco']:>7.2f} "
                  f"R${sinal['valor_estimado']:>7.2f} "
                  f"{sinal['pct_change_estimado']:>6.2f}% "
                  f"{sinal['quantidade_carteira']:>4.0f} "
                  f"{sinal['volume']:>10,.0f} "
                  f"{sinal['spread']:>6.3f}")
            print(f"      📉 Estimador prevê queda no preço")
            if sinal['volatilidade'] > 0:
                print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
            if sinal['spread'] > 0:
                print(f"      💹 Spread: {sinal['spread']:.4f}")
            print()

    # Mostrar sinais de COMPRA
    if sinais_compra:
        print(f"\n🟢 SINAIS DE COMPRA ({len(sinais_compra)} ações):")
        print(f"{'Ação':<8} {'Preço':<10} {'Est.':<10} {'Var%':<8} {'Volume':<12} {'Spread':<8}")
        print(f"{'-'*65}")
        for sinal in sinais_compra:
            print(f"{sinal['ticker_clean']:<8} "
                  f"R${sinal['preco']:>7.2f} "
                  f"R${sinal['valor_estimado']:>7.2f} "
                  f"{sinal['pct_change_estimado']:>6.2f}% "
                  f"{sinal['volume']:>10,.0f} "
                  f"{sinal['spread']:>6.3f}")
            print(f"      📈 Estimador prevê alta no preço")
            if sinal['volatilidade'] > 0:
                print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
            if sinal['spread'] > 0:
                print(f"      💹 Spread: {sinal['spread']:.4f}")
            print(f"      🤖 Estimador prevê alta no preço")
            print()

    # Resumo final
    print("📋 RESUMO DOS SINAIS:")
    print(f"   🔴 Venda: {len(sinais_venda)} ações na carteira (ordenadas por maior queda estimada)")
    print(f"   🟢 Compra: {len(sinais_compra)} ações (ordenadas por maior alta estimada)")
    print(f"   📊 Total de sinais: {len(sinais_compra) + len(sinais_venda)} ações")
    print(f"   🎯 Threshold: {config.get('lightgbm.features.pct_threshold')}%")
    if carteira_atual:
        print(f"   📋 Carteira atual: {len(carteira_atual)} posições ativas")

    print(f"\n" + "="*80)

def criar_graficos_sinais_individuais(acoes_com_predicoes, max_acoes=6):
    """
    Cria gráficos individuais para ações com sinais de compra/venda
    Mostra preço histórico, valor estimado e sinais
    """
    print(f"\n📈 Criando gráficos individuais de sinais...")

    # Criar diretório de figuras
    figures_dir = 'results/figures/lightgbm_estimador_analysis'
    individual_dir = os.path.join(figures_dir, 'individual_signals')
    os.makedirs(individual_dir, exist_ok=True)

    # Limpar figuras antigas
    if config.get('output.clean_old_files'):
        for arquivo in os.listdir(individual_dir):
            if arquivo.endswith('.png'):
                os.remove(os.path.join(individual_dir, arquivo))

    # Filtrar ações com sinais
    acoes_com_sinais = {}
    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ultimo_dia = dados.tail(1).iloc[0]
            sinal_tipo = ultimo_dia.get('Sinal_Tipo', 'SEM_SINAL')
            if sinal_tipo in ['COMPRA', 'VENDA']:
                acoes_com_sinais[ticker] = dados

    if not acoes_com_sinais:
        print(f"   ⚠️ Nenhuma ação com sinais para plotar")
        return

    # Limitar número de gráficos
    acoes_para_plotar = list(acoes_com_sinais.items())[:max_acoes]

    for ticker, dados in acoes_para_plotar:
        ticker_clean = ticker.replace('.SA', '')
        ultimo_dia = dados.tail(1).iloc[0]
        sinal = ultimo_dia.get('Sinal_Tipo', 'SEM_SINAL')
        predicao = ultimo_dia.get('Pct_Change_Estimado', 0)
        preco_atual = ultimo_dia.get('Media_OHLC', 0)

        # Usar últimos 60 dias para o gráfico
        dados_plot = dados.tail(60).copy()

        plt.figure(figsize=(14, 8))
        ax = plt.gca()

        # Plot do preço (média OHLC)
        ax.plot(dados_plot.index, dados_plot['Media_OHLC'],
                linewidth=2, label='Preço (Média OHLC)', color='blue')

        # Adicionar médias móveis se existirem
        if 'MM_10' in dados_plot.columns:
            ax.plot(dados_plot.index, dados_plot['MM_10'],
                    linewidth=1, alpha=0.7, label='MM 10', color='orange')
        if 'MM_25' in dados_plot.columns:
            ax.plot(dados_plot.index, dados_plot['MM_25'],
                    linewidth=1, alpha=0.7, label='MM 25', color='green')

        # Marcar o sinal no último dia
        ultimo_dia_index = dados_plot.index[-1]
        if sinal == 'COMPRA':
            ax.scatter(ultimo_dia_index, preco_atual, color='green', s=200,
                      marker='^', label=f'COMPRA ({predicao:+.2f}%)', zorder=5)
        elif sinal == 'VENDA':
            ax.scatter(ultimo_dia_index, preco_atual, color='red', s=200,
                      marker='v', label=f'VENDA ({predicao:+.2f}%)', zorder=5)

        # Configurações do gráfico
        ax.set_title(f'{ticker_clean} - Estimador LightGBM\n'
                    f'Predição: {predicao:+.2f}% | Sinal: {sinal} | Preço: R$ {preco_atual:.2f}',
                    fontsize=14, fontweight='bold')
        ax.set_xlabel('Data', fontsize=12)
        ax.set_ylabel('Preço (R$)', fontsize=12)
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)

        # Rotacionar labels do eixo x
        plt.xticks(rotation=45)
        plt.tight_layout()

        # Salvar gráfico
        plt.savefig(os.path.join(individual_dir, f'{ticker_clean}_sinal.png'),
                    dpi=300, bbox_inches='tight')
        plt.close()

    print(f"   ✅ {len(acoes_para_plotar)} gráficos individuais salvos em: {individual_dir}/")

def salvar_resultados_estimador(acoes_com_predicoes, resultados_modelo, feature_cols):
    """
    Salva os resultados das predições em arquivos CSV
    Filtra sinais de venda apenas para ações na carteira atual
    """
    print(f"\n💾 Salvando resultados das predições...")

    # Criar diretórios de saída
    output_dir = 'results/csv/lightgbm_estimador_analysis'
    individual_dir = os.path.join(output_dir, 'individual_stocks')
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(individual_dir, exist_ok=True)

    # Limpar arquivos antigos
    if config.get('output.clean_old_files'):
        for arquivo in os.listdir(output_dir):
            if arquivo.endswith('.csv'):
                os.remove(os.path.join(output_dir, arquivo))
        for arquivo in os.listdir(individual_dir):
            if arquivo.endswith('.csv'):
                os.remove(os.path.join(individual_dir, arquivo))
        print(f"🗑️ Arquivos antigos removidos de {output_dir}")

    # Carregar carteira atual para filtrar sinais de venda
    carteira_atual = carregar_carteira_atual()

    # Preparar dados para salvar
    dados_completos = []
    sinais_compra = []
    sinais_venda = []

    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ticker_clean = ticker.replace('.SA', '')
            ultimo_dia = dados.tail(1).iloc[0]

            # Dados completos para arquivo principal
            dados_completos.append({
                'ticker': ticker_clean,
                'data': ultimo_dia.name.strftime('%Y-%m-%d'),
                'preco_atual': ultimo_dia.get('Media_OHLC', 0),
                'valor_estimado': ultimo_dia.get('Valor_Estimado', 0),
                'pct_change_estimado': ultimo_dia.get('Pct_Change_Estimado', 0),
                'volume': ultimo_dia.get('Volume', 0),
                'volatilidade': ultimo_dia.get('Volatilidade', 0),
                'sinal_tipo': ultimo_dia.get('Sinal_Tipo', 'SEM_SINAL')
            })

            # Verificar se há sinal de compra
            if ultimo_dia.get('Pred_Compra', 0) == 1:
                sinais_compra.append({
                    'ticker': ticker_clean,
                    'data': ultimo_dia.name.strftime('%Y-%m-%d'),
                    'preco_atual': ultimo_dia.get('Media_OHLC', 0),
                    'valor_estimado': ultimo_dia.get('Valor_Estimado', 0),
                    'pct_change_estimado': ultimo_dia.get('Pct_Change_Estimado', 0),
                    'volume': ultimo_dia.get('Volume', 0),
                    'volatilidade': ultimo_dia.get('Volatilidade', 0)
                })

            elif ultimo_dia.get('Pred_Venda', 0) == 1:
                # FILTRO: Apenas salvar sinais de venda para ações na carteira
                if ticker in carteira_atual and carteira_atual[ticker] > 0:
                    sinais_venda.append({
                        'ticker': ticker_clean,
                        'data': ultimo_dia.name.strftime('%Y-%m-%d'),
                        'preco_atual': ultimo_dia.get('Media_OHLC', 0),
                        'valor_estimado': ultimo_dia.get('Valor_Estimado', 0),
                        'pct_change_estimado': ultimo_dia.get('Pct_Change_Estimado', 0),
                        'volume': ultimo_dia.get('Volume', 0),
                        'volatilidade': ultimo_dia.get('Volatilidade', 0),
                        'quantidade_carteira': carteira_atual[ticker]
                    })

            # Salvar dados individuais de cada ação
            dados_individuais = dados.copy()
            dados_individuais['Ticker'] = ticker_clean
            dados_individuais = dados_individuais.reset_index()
            arquivo_individual = os.path.join(individual_dir, f'estimador_{ticker_clean}.csv')
            dados_individuais.to_csv(arquivo_individual, index=False)

    # Salvar arquivo principal com todos os dados
    if dados_completos:
        df_completo = pd.DataFrame(dados_completos)
        df_completo = df_completo.sort_values('pct_change_estimado', ascending=False)
        arquivo_completo = os.path.join(output_dir, 'estimador_lightgbm_completo.csv')
        df_completo.to_csv(arquivo_completo, index=False)

    # Salvar resumo de sinais de venda (ordenados por maior queda estimada)
    if sinais_venda:
        df_venda = pd.DataFrame(sinais_venda)
        df_venda = df_venda.sort_values('pct_change_estimado')  # Menor variação primeiro (mais negativa)
        arquivo_venda = os.path.join(output_dir, 'sinais_venda_estimador.csv')
        df_venda.to_csv(arquivo_venda, index=False)

    # Salvar resumo de sinais de compra (ordenados por maior alta estimada)
    if sinais_compra:
        df_compra = pd.DataFrame(sinais_compra)
        df_compra = df_compra.sort_values('pct_change_estimado', ascending=False)  # Maior alta primeiro
        arquivo_compra = os.path.join(output_dir, 'sinais_compra_estimador.csv')
        df_compra.to_csv(arquivo_compra, index=False)

    # Salvar importância das features
    feature_importance_df = pd.DataFrame({
        'feature': feature_cols,
        'importance': resultados_modelo['feature_importance']
    }).sort_values('importance', ascending=False)
    arquivo_features = os.path.join(output_dir, 'feature_importance_estimador.csv')
    feature_importance_df.to_csv(arquivo_features, index=False)

    # Salvar métricas do modelo
    metricas_df = pd.DataFrame({
        'metrica': ['RMSE', 'MAE', 'R²'],
        'valor': [resultados_modelo['rmse'], resultados_modelo['mae'], resultados_modelo['r2']]
    })
    arquivo_metricas = os.path.join(output_dir, 'metricas_estimador.csv')
    metricas_df.to_csv(arquivo_metricas, index=False)

    # Salvar arquivo principal simplificado para compatibilidade
    resultados_lista = []
    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ultimo_dia = dados.tail(1).iloc[0]
            resultados_lista.append({
                'Ticker': ticker,
                'Data': ultimo_dia.name.strftime('%Y-%m-%d'),
                'Preco_Atual': ultimo_dia.get('Media_OHLC', 0),
                'Predicao_Variacao': ultimo_dia.get('Pct_Change_Estimado', 0),
                'Sinal': ultimo_dia.get('Sinal_Tipo', 'SEM_SINAL')
            })

    if resultados_lista:
        df_resultados = pd.DataFrame(resultados_lista)
        df_resultados = df_resultados.sort_values('Predicao_Variacao', ascending=False)
        csv_path = os.path.join('results', 'lightgbm_estimador_sinais.csv')
        df_resultados.to_csv(csv_path, index=False)

    print(f"   ✅ Resultados salvos em: {output_dir}/")
    print(f"   📊 Total de ações analisadas: {len(dados_completos)}")
    print(f"   🔴 Sinais de venda (carteira): {len(sinais_venda)} ações")
    print(f"   🟢 Sinais de compra: {len(sinais_compra)} ações")
    print(f"   📁 Arquivos gerados:")
    print(f"   • Dados completos: estimador_lightgbm_completo.csv")
    print(f"   • Sinais venda: sinais_venda_estimador.csv")
    print(f"   • Sinais compra: sinais_compra_estimador.csv")
    print(f"   • Importância features: feature_importance_estimador.csv")
    print(f"   • Métricas modelo: metricas_estimador.csv")
    print(f"   • Dados individuais: individual_stocks/ ({len(dados_completos)} ações)")

    return csv_path

def main():
    """
    Função principal do estimador LightGBM
    """
    print("🎯 ESTIMADOR LIGHTGBM - PREDIÇÃO DE VALORES DA MÉDIA OHLC")
    print("=" * 80)

    # Configurar ambiente
    setup_environment()

    # Carregar ações diversificadas
    print("\n📋 Carregando ações diversificadas...")
    todas_acoes = carregar_acoes_diversificadas()

    if not todas_acoes:
        print("❌ Nenhuma ação encontrada!")
        return

    # Baixar dados de todas as ações
    print(f"\n📊 Baixando dados de {len(todas_acoes)} ações...")
    # Converter formato de tuplas para compatibilidade com cache unificado
    todas_acoes_com_origem = [(ticker, nome, 'diversificacao') for ticker, nome in todas_acoes]
    acoes_dados = baixar_dados_todas_acoes_unificado(todas_acoes_com_origem)

    if not acoes_dados:
        print("❌ Falha ao baixar dados das ações!")
        return

    # Calcular features para todas as ações
    print(f"\n🔧 Calculando features para {len(acoes_dados)} ações...")
    for ticker, dados in acoes_dados.items():
        if dados is not None:
            acoes_dados[ticker] = calcular_features_e_sinais(dados, ticker)

    # Verificar se modelo estimador já foi treinado hoje (considerando force_training)
    modelo_dir = 'results/models/lightgbm_estimador_analysis'
    modelo_nome = 'modelo_estimador.pkl'
    os.makedirs(modelo_dir, exist_ok=True)

    force_training = config.get('lightgbm', {}).get('cache', {}).get('force_training', False)
    if force_training:
        print(f"🔄 Forçando treinamento do modelo estimador (force_training=True)")
        modelo_treinado_hoje = False
        resultados_modelo = None
        feature_cols = None
    else:
        print(f"\n🔍 Verificando modelo existente...")
        resultados_modelo, feature_cols = carregar_modelo_estimador()
        modelo_treinado_hoje = resultados_modelo is not None

    if not modelo_treinado_hoje:
        # Preparar dataset para regressão
        print(f"\n📈 Preparando dataset de regressão...")
        X, y, feature_cols, dataset_completo = preparar_dataset_regressao(acoes_dados)

        if X is None:
            print("❌ Erro ao preparar dataset")
            return

        # Treinar estimador
        print(f"\n🚀 Treinando estimador LightGBM...")
        resultados_modelo, feature_cols = treinar_estimador_lightgbm(X, y, feature_cols, dataset_completo)

        if resultados_modelo is None:
            print("❌ Erro no treinamento do estimador")
            return

        # Salvar modelo treinado
        print(f"\n💾 Salvando modelo estimador...")
        salvar_modelo_estimador(resultados_modelo, feature_cols)

    # Aplicar predições e gerar sinais
    print(f"\n🎯 Aplicando predições e gerando sinais...")
    acoes_com_predicoes = aplicar_predicoes_estimador(acoes_dados, resultados_modelo, feature_cols)

    # Mostrar recomendações (mesmo formato do XGBoost)
    imprimir_recomendacoes_estimador(acoes_com_predicoes)

    # Criar gráficos de desempenho
    if not modelo_treinado_hoje:
        print(f"\n📊 Criando gráficos de desempenho...")
        criar_graficos_desempenho_estimador(resultados_modelo, feature_cols)

    # Criar gráficos individuais de sinais
    criar_graficos_sinais_individuais(acoes_com_predicoes)

    # Salvar resultados
    print(f"\n💾 Salvando resultados...")
    salvar_resultados_estimador(acoes_com_predicoes, resultados_modelo, feature_cols)

    print(f"\n✅ Análise LightGBM concluída com sucesso!")
    print(f"📁 Resultados salvos em: results/")
    print(f"📊 Gráficos salvos em: results/figures/lightgbm_estimador_analysis/")

if __name__ == "__main__":
    main()
