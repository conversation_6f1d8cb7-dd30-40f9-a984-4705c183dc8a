{"last_update": "2025-08-20 16:19:12.481849", "tickers": {"EMBR3.SA": {"nome": "Embraer", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "SYNE3.SA": {"nome": "SYN", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "WEGE3.SA": {"nome": "WEG", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "STBP3.SA": {"nome": "Santos Brasil", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "SLCE3.SA": {"nome": "SLC AgrÃ­cola", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "MRFG3.SA": {"nome": "<PERSON><PERSON><PERSON>", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "SUZB3.SA": {"nome": "<PERSON><PERSON>", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "BRFS3.SA": {"nome": "BRF", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "PORT3.SA": {"nome": "Wilson Sons", "origem": "diversificacao", "total_days": 954, "data_start": "2021-10-25 00:00:00", "data_end": "2025-08-20 00:00:00"}, "ESPA3.SA": {"nome": "EspaÃ§olaser", "origem": "diversificacao", "total_days": 1133, "data_start": "2021-02-04 00:00:00", "data_end": "2025-08-20 00:00:00"}, "PETR3.SA": {"nome": "Petrobras", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "VLID3.SA": {"nome": "<PERSON><PERSON>", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "VALE3.SA": {"nome": "Vale", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "LPSB3.SA": {"nome": "Lopes", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "CSMG3.SA": {"nome": "COPASA", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "NTCO3.SA": {"nome": "<PERSON><PERSON>", "origem": "diversificacao", "total_days": 1385, "data_start": "2019-12-18 00:00:00", "data_end": "2025-07-24 00:00:00"}, "BBSE3.SA": {"nome": "BB Seguridade", "origem": "diversificacao", "total_days": 3063, "data_start": "2013-04-29 00:00:00", "data_end": "2025-08-20 00:00:00"}, "CMIG3.SA": {"nome": "Cemig", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "GGBR4.SA": {"nome": "<PERSON><PERSON><PERSON>", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "RADL3.SA": {"nome": "Raia<PERSON><PERSON><PERSON><PERSON>", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "KLBN11.SA": {"nome": "<PERSON><PERSON><PERSON>", "origem": "diversificacao", "total_days": 2879, "data_start": "2014-01-23 00:00:00", "data_end": "2025-08-20 00:00:00"}, "ABEV3.SA": {"nome": "Ambev", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "ODPV3.SA": {"nome": "Odontoprev", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "PRIO3.SA": {"nome": "PetroRio", "origem": "diversificacao", "total_days": 3682, "data_start": "2010-10-25 00:00:00", "data_end": "2025-08-20 00:00:00"}, "VVEO3.SA": {"nome": "Viveo", "origem": "diversificacao", "total_days": 1007, "data_start": "2021-08-09 00:00:00", "data_end": "2025-08-20 00:00:00"}, "AGRO3.SA": {"nome": "BrasilAgro", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "CXSE3.SA": {"nome": "Caixa Seguridade", "origem": "diversificacao", "total_days": 1076, "data_start": "2021-04-30 00:00:00", "data_end": "2025-08-20 00:00:00"}, "CMIN3.SA": {"nome": "CSN MineraÃ§Ã£o", "origem": "diversificacao", "total_days": 1123, "data_start": "2021-02-22 00:00:00", "data_end": "2025-08-20 00:00:00"}, "TUPY3.SA": {"nome": "<PERSON><PERSON>", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "TTEN3.SA": {"nome": "3tentos", "origem": "diversificacao", "total_days": 1022, "data_start": "2021-07-16 00:00:00", "data_end": "2025-08-20 00:00:00"}, "MDIA3.SA": {"nome": "M<PERSON>ran<PERSON>", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}, "CPFE3.SA": {"nome": "CPFL Energia", "origem": "diversificacao", "total_days": 3728, "data_start": "2010-08-18 00:00:00", "data_end": "2025-08-20 00:00:00"}}, "total_records": 3728, "date_range": {"start": "2010-08-18 00:00:00", "end": "2025-08-20 00:00:00"}, "compression": "snappy", "columns": ["EMBR3.SA_Open", "EMBR3.SA_High", "EMBR3.SA_Low", "EMBR3.SA_Close", "EMBR3.SA_Volume", "EMBR3.SA_Media_OHLC", "EMBR3.SA_MM10", "EMBR3.SA_MM25", "EMBR3.SA_MM100", "EMBR3.SA_Media_OHLC_PctChange", "EMBR3.SA_Volatilidade", "EMBR3.SA_Spread", "EMBR3.SA_Parkinson_Volatility", "EMBR3.SA_MFI", "EMBR3.SA_MFI_Historical", "EMBR3.SA_EMV", "EMBR3.SA_EMV_MA", "EMBR3.SA_Amihud", "EMBR3.SA_Amihud_Historical", "EMBR3.SA_Roll_Spread", "EMBR3.SA_Roll_Spread_Historical", "EMBR3.SA_Hurst", "EMBR3.SA_<PERSON><PERSON>_Historical", "EMBR3.SA_Vol_per_Volume", "EMBR3.SA_Vol_per_Volume_Historical", "EMBR3.SA_CMF", "EMBR3.SA_CMF_Historical", "EMBR3.SA_AD_Line", "EMBR3.SA_AD_Line_Historical", "EMBR3.SA_VO", "EMBR3.SA_High_Max_50", "EMBR3.SA_Low_Min_50", "EMBR3.SA_Segunda", "EMBR3.SA_Terca", "EMBR3.SA_Quarta", "EMBR3.SA_Quinta", "EMBR3.SA_Sexta", "EMBR3.SA_Mes_1", "EMBR3.SA_Mes_2", "EMBR3.SA_Mes_3", "EMBR3.SA_Mes_4", "EMBR3.SA_Mes_5", "EMBR3.SA_Mes_6", "EMBR3.SA_Mes_7", "EMBR3.SA_Mes_8", "EMBR3.SA_Mes_9", "EMBR3.SA_Mes_10", "EMBR3.SA_Mes_11", "EMBR3.SA_Mes_12", "EMBR3.SA_Quarter_1", "EMBR3.SA_Quarter_2", "EMBR3.SA_Quarter_3", "EMBR3.SA_Quarter_4", "EMBR3.SA_Last_Day_Quarter", "EMBR3.SA_Pre_Feriado_Brasil", "EMBR3.SA_Media_OHLC_Anterior", "EMBR3.SA_Sinal_Compra", "EMBR3.SA_Sinal_Venda", "EMBR3.SA_Media_OHLC_Futura", "EMBR3.SA_Media_OHLC_PctChange_Lag_1", "EMBR3.SA_Media_OHLC_PctChange_Lag_2", "EMBR3.SA_Media_OHLC_PctChange_Lag_3", "EMBR3.SA_Media_OHLC_PctChange_Lag_4", "EMBR3.SA_Media_OHLC_PctChange_Lag_5", "EMBR3.SA_Media_OHLC_PctChange_Lag_6", "EMBR3.SA_Media_OHLC_PctChange_Lag_7", "EMBR3.SA_Media_OHLC_PctChange_Lag_8", "EMBR3.SA_Media_OHLC_PctChange_Lag_9", "EMBR3.SA_Media_OHLC_PctChange_Lag_10", "EMBR3.SA_MM10_PctChange", "EMBR3.SA_Diff_OHLC_MM10", "EMBR3.SA_MM25_PctChange", "EMBR3.SA_Diff_OHLC_MM25", "EMBR3.SA_MM100_PctChange", "EMBR3.SA_Diff_OHLC_MM100", "EMBR3.SA_MM10_Menos_MM25", "EMBR3.SA_MM25_Menos_MM100", "EMBR3.SA_MM10_Menos_MM100", "EMBR3.SA_Volume_Lag_1", "EMBR3.SA_Volume_Lag_2", "EMBR3.SA_Volume_Lag_3", "EMBR3.SA_Volume_Lag_4", "EMBR3.SA_Volume_Lag_5", "EMBR3.SA_Spread_Lag_1", "EMBR3.SA_Spread_Lag_2", "EMBR3.SA_Spread_Lag_3", "EMBR3.SA_Spread_Lag_4", "EMBR3.SA_Spread_Lag_5", "EMBR3.SA_Volatilidade_Lag_1", "EMBR3.SA_Volatilidade_Lag_2", "EMBR3.SA_Volatilidade_Lag_3", "EMBR3.SA_Volatilidade_Lag_4", "EMBR3.SA_Volatilidade_Lag_5", "EMBR3.SA_Parkinson_Volatility_Lag_1", "EMBR3.SA_Parkinson_Volatility_Lag_2", "EMBR3.SA_Parkinson_Volatility_Lag_3", "EMBR3.SA_Parkinson_Volatility_Lag_4", "EMBR3.SA_Parkinson_Volatility_Lag_5", "EMBR3.SA_EMV_Lag_1", "EMBR3.SA_EMV_Lag_2", "EMBR3.SA_EMV_Lag_3", "EMBR3.SA_EMV_Lag_4", "EMBR3.SA_EMV_Lag_5", "EMBR3.SA_EMV_MA_Lag_1", "EMBR3.SA_EMV_MA_Lag_2", "EMBR3.SA_EMV_MA_Lag_3", "EMBR3.SA_EMV_MA_Lag_4", "EMBR3.SA_EMV_MA_Lag_5", "EMBR3.SA_VO_Lag_1", "EMBR3.SA_VO_Lag_2", "EMBR3.SA_VO_Lag_3", "EMBR3.SA_VO_Lag_4", "EMBR3.SA_VO_Lag_5", "EMBR3.SA_High_Max_50_Lag_1", "EMBR3.SA_High_Max_50_Lag_2", "EMBR3.SA_High_Max_50_Lag_3", "EMBR3.SA_High_Max_50_Lag_4", "EMBR3.SA_High_Max_50_Lag_5", "EMBR3.SA_Low_Min_50_Lag_1", "EMBR3.SA_Low_Min_50_Lag_2", "EMBR3.SA_Low_Min_50_Lag_3", "EMBR3.SA_Low_Min_50_Lag_4", "EMBR3.SA_Low_Min_50_Lag_5", "EMBR3.SA_MFI_Lag_1", "EMBR3.SA_MFI_Lag_2", "EMBR3.SA_MFI_Lag_3", "EMBR3.SA_MFI_Lag_4", "EMBR3.SA_MFI_Lag_5", "EMBR3.SA_Amihud_Lag_1", "EMBR3.SA_Amihud_Lag_2", "EMBR3.SA_Amihud_Lag_3", "EMBR3.SA_Amihud_Lag_4", "EMBR3.SA_Amihud_Lag_5", "EMBR3.SA_Roll_Spread_Lag_1", "EMBR3.SA_Roll_Spread_Lag_2", "EMBR3.SA_Roll_Spread_Lag_3", "EMBR3.SA_Roll_Spread_Lag_4", "EMBR3.SA_Roll_Spread_Lag_5", "EMBR3.SA_Hurst_Lag_1", "EMBR3.<PERSON>_<PERSON><PERSON>_Lag_2", "EMBR3.<PERSON>_<PERSON><PERSON>_Lag_3", "EMBR3.<PERSON>_<PERSON><PERSON>_Lag_4", "EMBR3.<PERSON>_<PERSON><PERSON>_Lag_5", "EMBR3.SA_Vol_per_Volume_Lag_1", "EMBR3.SA_Vol_per_Volume_Lag_2", "EMBR3.SA_Vol_per_Volume_Lag_3", "EMBR3.SA_Vol_per_Volume_Lag_4", "EMBR3.SA_Vol_per_Volume_Lag_5", "EMBR3.SA_CMF_Lag_1", "EMBR3.SA_CMF_Lag_2", "EMBR3.SA_CMF_Lag_3", "EMBR3.SA_CMF_Lag_4", "EMBR3.SA_CMF_Lag_5", "EMBR3.SA_AD_Line_Lag_1", "EMBR3.SA_AD_Line_Lag_2", "EMBR3.SA_AD_Line_Lag_3", "EMBR3.SA_AD_Line_Lag_4", "EMBR3.SA_AD_Line_Lag_5", "SYNE3.SA_Open", "SYNE3.SA_High", "SYNE3.SA_Low", "SYNE3.SA_Close", "SYNE3.SA_Volume", "SYNE3.SA_Media_OHLC", "SYNE3.SA_MM10", "SYNE3.SA_MM25", "SYNE3.SA_MM100", "SYNE3.SA_Media_OHLC_PctChange", "SYNE3.SA_Volatilidade", "SYNE3.SA_Spread", "SYNE3.SA_Parkinson_Volatility", "SYNE3.SA_MFI", "SYNE3.SA_MFI_Historical", "SYNE3.SA_EMV", "SYNE3.SA_EMV_MA", "SYNE3.SA_Amihud", "SYNE3.SA_Amihud_Historical", "SYNE3.SA_Roll_Spread", "SYNE3.SA_Roll_Spread_Historical", "SYNE3.SA_Hurst", "SYNE3.<PERSON>_<PERSON><PERSON>_Historical", "SYNE3.SA_Vol_per_Volume", "SYNE3.SA_Vol_per_Volume_Historical", "SYNE3.SA_CMF", "SYNE3.SA_CMF_Historical", "SYNE3.SA_AD_Line", "SYNE3.SA_AD_Line_Historical", "SYNE3.SA_VO", "SYNE3.SA_High_Max_50", "SYNE3.SA_Low_Min_50", "SYNE3.SA_Segunda", "SYNE3.SA_Terca", "SYNE3.SA_Quarta", "SYNE3.SA_Quinta", "SYNE3.SA_Sexta", "SYNE3.SA_Mes_1", "SYNE3.SA_Mes_2", "SYNE3.SA_Mes_3", "SYNE3.SA_Mes_4", "SYNE3.SA_Mes_5", "SYNE3.SA_Mes_6", "SYNE3.SA_Mes_7", "SYNE3.SA_Mes_8", "SYNE3.SA_Mes_9", "SYNE3.SA_Mes_10", "SYNE3.SA_Mes_11", "SYNE3.SA_Mes_12", "SYNE3.SA_Quarter_1", "SYNE3.SA_Quarter_2", "SYNE3.SA_Quarter_3", "SYNE3.SA_Quarter_4", "SYNE3.SA_Last_Day_Quarter", "SYNE3.SA_Pre_Feriado_Brasil", "SYNE3.SA_Media_OHLC_Anterior", "SYNE3.SA_Sinal_Compra", "SYNE3.SA_Sinal_Venda", "SYNE3.SA_Media_OHLC_Futura", "SYNE3.SA_Media_OHLC_PctChange_Lag_1", "SYNE3.SA_Media_OHLC_PctChange_Lag_2", "SYNE3.SA_Media_OHLC_PctChange_Lag_3", "SYNE3.SA_Media_OHLC_PctChange_Lag_4", "SYNE3.SA_Media_OHLC_PctChange_Lag_5", "SYNE3.SA_Media_OHLC_PctChange_Lag_6", "SYNE3.SA_Media_OHLC_PctChange_Lag_7", "SYNE3.SA_Media_OHLC_PctChange_Lag_8", "SYNE3.SA_Media_OHLC_PctChange_Lag_9", "SYNE3.SA_Media_OHLC_PctChange_Lag_10", "SYNE3.SA_MM10_PctChange", "SYNE3.SA_Diff_OHLC_MM10", "SYNE3.SA_MM25_PctChange", "SYNE3.SA_Diff_OHLC_MM25", "SYNE3.SA_MM100_PctChange", "SYNE3.SA_Diff_OHLC_MM100", "SYNE3.SA_MM10_Menos_MM25", "SYNE3.SA_MM25_Menos_MM100", "SYNE3.SA_MM10_Menos_MM100", "SYNE3.SA_Volume_Lag_1", "SYNE3.SA_Volume_Lag_2", "SYNE3.SA_Volume_Lag_3", "SYNE3.SA_Volume_Lag_4", "SYNE3.SA_Volume_Lag_5", "SYNE3.SA_Spread_Lag_1", "SYNE3.SA_Spread_Lag_2", "SYNE3.SA_Spread_Lag_3", "SYNE3.SA_Spread_Lag_4", "SYNE3.SA_Spread_Lag_5", "SYNE3.SA_Volatilidade_Lag_1", "SYNE3.SA_Volatilidade_Lag_2", "SYNE3.SA_Volatilidade_Lag_3", "SYNE3.SA_Volatilidade_Lag_4", "SYNE3.SA_Volatilidade_Lag_5", "SYNE3.<PERSON>_Parkinson_Volatility_Lag_1", "SYNE3.<PERSON>_Parkinson_Volatility_Lag_2", "SYNE3.<PERSON>_Parkinson_Volatility_Lag_3", "SYNE3.<PERSON>_Parkinson_Volatility_Lag_4", "SYNE3.<PERSON>_Parkinson_Volatility_Lag_5", "SYNE3.SA_EMV_Lag_1", "SYNE3.SA_EMV_Lag_2", "SYNE3.SA_EMV_Lag_3", "SYNE3.SA_EMV_Lag_4", "SYNE3.SA_EMV_Lag_5", "SYNE3.SA_EMV_MA_Lag_1", "SYNE3.SA_EMV_MA_Lag_2", "SYNE3.SA_EMV_MA_Lag_3", "SYNE3.SA_EMV_MA_Lag_4", "SYNE3.SA_EMV_MA_Lag_5", "SYNE3.SA_VO_Lag_1", "SYNE3.SA_VO_Lag_2", "SYNE3.SA_VO_Lag_3", "SYNE3.SA_VO_Lag_4", "SYNE3.SA_VO_Lag_5", "SYNE3.SA_High_Max_50_Lag_1", "SYNE3.SA_High_Max_50_Lag_2", "SYNE3.SA_High_Max_50_Lag_3", "SYNE3.SA_High_Max_50_Lag_4", "SYNE3.SA_High_Max_50_Lag_5", "SYNE3.SA_Low_Min_50_Lag_1", "SYNE3.SA_Low_Min_50_Lag_2", "SYNE3.SA_Low_Min_50_Lag_3", "SYNE3.SA_Low_Min_50_Lag_4", "SYNE3.SA_Low_Min_50_Lag_5", "SYNE3.SA_MFI_Lag_1", "SYNE3.SA_MFI_Lag_2", "SYNE3.SA_MFI_Lag_3", "SYNE3.SA_MFI_Lag_4", "SYNE3.SA_MFI_Lag_5", "SYNE3.SA_Amihud_Lag_1", "SYNE3.SA_Amihud_Lag_2", "SYNE3.SA_Amihud_Lag_3", "SYNE3.SA_Amihud_Lag_4", "SYNE3.SA_Amihud_Lag_5", "SYNE3.SA_Roll_Spread_Lag_1", "SYNE3.SA_Roll_Spread_Lag_2", "SYNE3.SA_Roll_Spread_Lag_3", "SYNE3.SA_Roll_Spread_Lag_4", "SYNE3.SA_Roll_Spread_Lag_5", "SYNE3.<PERSON>_Hurst_Lag_1", "SYNE3.<PERSON>_<PERSON><PERSON>_Lag_2", "SYNE3.<PERSON>_<PERSON><PERSON>_Lag_3", "SYNE3.<PERSON>_<PERSON><PERSON>_Lag_4", "SYNE3.<PERSON>_<PERSON><PERSON>_Lag_5", "SYNE3.SA_Vol_per_Volume_Lag_1", "SYNE3.SA_Vol_per_Volume_Lag_2", "SYNE3.SA_Vol_per_Volume_Lag_3", "SYNE3.SA_Vol_per_Volume_Lag_4", "SYNE3.SA_Vol_per_Volume_Lag_5", "SYNE3.SA_CMF_Lag_1", "SYNE3.SA_CMF_Lag_2", "SYNE3.SA_CMF_Lag_3", "SYNE3.SA_CMF_Lag_4", "SYNE3.SA_CMF_Lag_5", "SYNE3.SA_AD_Line_Lag_1", "SYNE3.SA_AD_Line_Lag_2", "SYNE3.SA_AD_Line_Lag_3", "SYNE3.SA_AD_Line_Lag_4", "SYNE3.SA_AD_Line_Lag_5", "WEGE3.SA_Open", "WEGE3.SA_High", "WEGE3.SA_Low", "WEGE3.SA_Close", "WEGE3.SA_Volume", "WEGE3.SA_Media_OHLC", "WEGE3.SA_MM10", "WEGE3.SA_MM25", "WEGE3.SA_MM100", "WEGE3.SA_Media_OHLC_PctChange", "WEGE3.SA_Volatilidade", "WEGE3.SA_Spread", "WEGE3.SA_Parkinson_Volatility", "WEGE3.SA_MFI", "WEGE3.SA_MFI_Historical", "WEGE3.SA_EMV", "WEGE3.SA_EMV_MA", "WEGE3.SA_Amihud", "WEGE3.SA_Amihud_Historical", "WEGE3.SA_Roll_Spread", "WEGE3.SA_Roll_Spread_Historical", "WEGE3.SA_Hurst", "WEGE3.<PERSON>_<PERSON><PERSON>_Historical", "WEGE3.SA_Vol_per_Volume", "WEGE3.SA_Vol_per_Volume_Historical", "WEGE3.SA_CMF", "WEGE3.SA_CMF_Historical", "WEGE3.SA_AD_Line", "WEGE3.SA_AD_Line_Historical", "WEGE3.SA_VO", "WEGE3.SA_High_Max_50", "WEGE3.SA_Low_Min_50", "WEGE3.SA_Segunda", "WEGE3.SA_Terca", "WEGE3.SA_Quarta", "WEGE3.SA_Quinta", "WEGE3.SA_Sexta", "WEGE3.SA_Mes_1", "WEGE3.SA_Mes_2", "WEGE3.SA_Mes_3", "WEGE3.SA_Mes_4", "WEGE3.SA_Mes_5", "WEGE3.SA_Mes_6", "WEGE3.SA_Mes_7", "WEGE3.SA_Mes_8", "WEGE3.SA_Mes_9", "WEGE3.SA_Mes_10", "WEGE3.SA_Mes_11", "WEGE3.SA_Mes_12", "WEGE3.SA_Quarter_1", "WEGE3.SA_Quarter_2", "WEGE3.SA_Quarter_3", "WEGE3.SA_Quarter_4", "WEGE3.SA_Last_Day_Quarter", "WEGE3.SA_Pre_Feriado_Brasil", "WEGE3.SA_Media_OHLC_Anterior", "WEGE3.SA_Sinal_Compra", "WEGE3.SA_Sinal_Venda", "WEGE3.SA_Media_OHLC_Futura", "WEGE3.SA_Media_OHLC_PctChange_Lag_1", "WEGE3.SA_Media_OHLC_PctChange_Lag_2", "WEGE3.SA_Media_OHLC_PctChange_Lag_3", "WEGE3.SA_Media_OHLC_PctChange_Lag_4", "WEGE3.SA_Media_OHLC_PctChange_Lag_5", "WEGE3.SA_Media_OHLC_PctChange_Lag_6", "WEGE3.SA_Media_OHLC_PctChange_Lag_7", "WEGE3.SA_Media_OHLC_PctChange_Lag_8", "WEGE3.SA_Media_OHLC_PctChange_Lag_9", "WEGE3.SA_Media_OHLC_PctChange_Lag_10", "WEGE3.SA_MM10_PctChange", "WEGE3.SA_Diff_OHLC_MM10", "WEGE3.SA_MM25_PctChange", "WEGE3.SA_Diff_OHLC_MM25", "WEGE3.SA_MM100_PctChange", "WEGE3.SA_Diff_OHLC_MM100", "WEGE3.SA_MM10_Menos_MM25", "WEGE3.SA_MM25_Menos_MM100", "WEGE3.SA_MM10_Menos_MM100", "WEGE3.SA_Volume_Lag_1", "WEGE3.SA_Volume_Lag_2", "WEGE3.SA_Volume_Lag_3", "WEGE3.SA_Volume_Lag_4", "WEGE3.SA_Volume_Lag_5", "WEGE3.SA_Spread_Lag_1", "WEGE3.SA_Spread_Lag_2", "WEGE3.SA_Spread_Lag_3", "WEGE3.SA_Spread_Lag_4", "WEGE3.SA_Spread_Lag_5", "WEGE3.SA_Volatilidade_Lag_1", "WEGE3.SA_Volatilidade_Lag_2", "WEGE3.SA_Volatilidade_Lag_3", "WEGE3.SA_Volatilidade_Lag_4", "WEGE3.SA_Volatilidade_Lag_5", "WEGE3.SA_Parkinson_Volatility_Lag_1", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_2", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_3", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_4", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_5", "WEGE3.SA_EMV_Lag_1", "WEGE3.SA_EMV_Lag_2", "WEGE3.SA_EMV_Lag_3", "WEGE3.SA_EMV_Lag_4", "WEGE3.SA_EMV_Lag_5", "WEGE3.SA_EMV_MA_Lag_1", "WEGE3.SA_EMV_MA_Lag_2", "WEGE3.SA_EMV_MA_Lag_3", "WEGE3.SA_EMV_MA_Lag_4", "WEGE3.SA_EMV_MA_Lag_5", "WEGE3.SA_VO_Lag_1", "WEGE3.SA_VO_Lag_2", "WEGE3.SA_VO_Lag_3", "WEGE3.SA_VO_Lag_4", "WEGE3.SA_VO_Lag_5", "WEGE3.SA_High_Max_50_Lag_1", "WEGE3.SA_High_Max_50_Lag_2", "WEGE3.SA_High_Max_50_Lag_3", "WEGE3.SA_High_Max_50_Lag_4", "WEGE3.SA_High_Max_50_Lag_5", "WEGE3.SA_Low_Min_50_Lag_1", "WEGE3.SA_Low_Min_50_Lag_2", "WEGE3.SA_Low_Min_50_Lag_3", "WEGE3.SA_Low_Min_50_Lag_4", "WEGE3.SA_Low_Min_50_Lag_5", "WEGE3.SA_MFI_Lag_1", "WEGE3.SA_MFI_Lag_2", "WEGE3.SA_MFI_Lag_3", "WEGE3.SA_MFI_Lag_4", "WEGE3.SA_MFI_Lag_5", "WEGE3.SA_Amihud_Lag_1", "WEGE3.SA_Amihud_Lag_2", "WEGE3.SA_Amihud_Lag_3", "WEGE3.SA_Amihud_Lag_4", "WEGE3.SA_Amihud_Lag_5", "WEGE3.SA_Roll_Spread_Lag_1", "WEGE3.SA_Roll_Spread_Lag_2", "WEGE3.SA_Roll_Spread_Lag_3", "WEGE3.SA_Roll_Spread_Lag_4", "WEGE3.SA_Roll_Spread_Lag_5", "WEGE3.<PERSON>_Hurst_Lag_1", "WEGE3.<PERSON>_Hu<PERSON>_Lag_2", "WEGE3.<PERSON>_Hu<PERSON>_Lag_3", "WEGE3.<PERSON>_<PERSON><PERSON>_Lag_4", "WEGE3.<PERSON>_<PERSON><PERSON>_Lag_5", "WEGE3.SA_Vol_per_Volume_Lag_1", "WEGE3.SA_Vol_per_Volume_Lag_2", "WEGE3.SA_Vol_per_Volume_Lag_3", "WEGE3.SA_Vol_per_Volume_Lag_4", "WEGE3.SA_Vol_per_Volume_Lag_5", "WEGE3.SA_CMF_Lag_1", "WEGE3.SA_CMF_Lag_2", "WEGE3.SA_CMF_Lag_3", "WEGE3.SA_CMF_Lag_4", "WEGE3.SA_CMF_Lag_5", "WEGE3.SA_AD_Line_Lag_1", "WEGE3.SA_AD_Line_Lag_2", "WEGE3.SA_AD_Line_Lag_3", "WEGE3.SA_AD_Line_Lag_4", "WEGE3.SA_AD_Line_Lag_5", "STBP3.SA_Open", "STBP3.SA_High", "STBP3.SA_Low", "STBP3.SA_Close", "STBP3.SA_Volume", "STBP3.SA_Media_OHLC", "STBP3.SA_MM10", "STBP3.SA_MM25", "STBP3.SA_MM100", "STBP3.SA_Media_OHLC_PctChange", "STBP3.SA_Volatilidade", "STBP3.SA_Spread", "STBP3.SA_Parkinson_Volatility", "STBP3.SA_MFI", "STBP3.SA_MFI_Historical", "STBP3.SA_EMV", "STBP3.SA_EMV_MA", "STBP3.SA_Amihud", "STBP3.SA_Amihud_Historical", "STBP3.SA_Roll_Spread", "STBP3.SA_Roll_Spread_Historical", "STBP3.SA_Hurst", "STBP3.SA_<PERSON><PERSON>_Historical", "STBP3.SA_Vol_per_Volume", "STBP3.SA_Vol_per_Volume_Historical", "STBP3.SA_CMF", "STBP3.SA_CMF_Historical", "STBP3.SA_AD_Line", "STBP3.SA_AD_Line_Historical", "STBP3.SA_VO", "STBP3.SA_High_Max_50", "STBP3.SA_Low_Min_50", "STBP3.SA_Segunda", "STBP3.SA_Terca", "STBP3.SA_Quarta", "STBP3.SA_Quinta", "STBP3.SA_Sexta", "STBP3.SA_Mes_1", "STBP3.SA_Mes_2", "STBP3.SA_Mes_3", "STBP3.SA_Mes_4", "STBP3.SA_Mes_5", "STBP3.SA_Mes_6", "STBP3.SA_Mes_7", "STBP3.SA_Mes_8", "STBP3.SA_Mes_9", "STBP3.SA_Mes_10", "STBP3.SA_Mes_11", "STBP3.SA_Mes_12", "STBP3.SA_Quarter_1", "STBP3.SA_Quarter_2", "STBP3.SA_Quarter_3", "STBP3.SA_Quarter_4", "STBP3.SA_Last_Day_Quarter", "STBP3.SA_Pre_Feriado_Brasil", "STBP3.SA_Media_OHLC_Anterior", "STBP3.SA_Sinal_Compra", "STBP3.SA_Sinal_Venda", "STBP3.SA_Media_OHLC_Futura", "STBP3.SA_Media_OHLC_PctChange_Lag_1", "STBP3.SA_Media_OHLC_PctChange_Lag_2", "STBP3.SA_Media_OHLC_PctChange_Lag_3", "STBP3.SA_Media_OHLC_PctChange_Lag_4", "STBP3.SA_Media_OHLC_PctChange_Lag_5", "STBP3.SA_Media_OHLC_PctChange_Lag_6", "STBP3.SA_Media_OHLC_PctChange_Lag_7", "STBP3.SA_Media_OHLC_PctChange_Lag_8", "STBP3.SA_Media_OHLC_PctChange_Lag_9", "STBP3.SA_Media_OHLC_PctChange_Lag_10", "STBP3.SA_MM10_PctChange", "STBP3.SA_Diff_OHLC_MM10", "STBP3.SA_MM25_PctChange", "STBP3.SA_Diff_OHLC_MM25", "STBP3.SA_MM100_PctChange", "STBP3.SA_Diff_OHLC_MM100", "STBP3.SA_MM10_Menos_MM25", "STBP3.SA_MM25_Menos_MM100", "STBP3.SA_MM10_Menos_MM100", "STBP3.SA_Volume_Lag_1", "STBP3.SA_Volume_Lag_2", "STBP3.SA_Volume_Lag_3", "STBP3.SA_Volume_Lag_4", "STBP3.SA_Volume_Lag_5", "STBP3.SA_Spread_Lag_1", "STBP3.SA_Spread_Lag_2", "STBP3.SA_Spread_Lag_3", "STBP3.SA_Spread_Lag_4", "STBP3.SA_Spread_Lag_5", "STBP3.SA_Volatilidade_Lag_1", "STBP3.SA_Volatilidade_Lag_2", "STBP3.SA_Volatilidade_Lag_3", "STBP3.SA_Volatilidade_Lag_4", "STBP3.SA_Volatilidade_Lag_5", "STBP3.SA_Parkinson_Volatility_Lag_1", "STBP3.SA_Parkinson_Volatility_Lag_2", "STBP3.SA_Parkinson_Volatility_Lag_3", "STBP3.SA_Parkinson_Volatility_Lag_4", "STBP3.<PERSON>_Parkinson_Volatility_Lag_5", "STBP3.SA_EMV_Lag_1", "STBP3.SA_EMV_Lag_2", "STBP3.SA_EMV_Lag_3", "STBP3.SA_EMV_Lag_4", "STBP3.SA_EMV_Lag_5", "STBP3.SA_EMV_MA_Lag_1", "STBP3.SA_EMV_MA_Lag_2", "STBP3.SA_EMV_MA_Lag_3", "STBP3.SA_EMV_MA_Lag_4", "STBP3.SA_EMV_MA_Lag_5", "STBP3.SA_VO_Lag_1", "STBP3.SA_VO_Lag_2", "STBP3.SA_VO_Lag_3", "STBP3.SA_VO_Lag_4", "STBP3.SA_VO_Lag_5", "STBP3.SA_High_Max_50_Lag_1", "STBP3.SA_High_Max_50_Lag_2", "STBP3.SA_High_Max_50_Lag_3", "STBP3.SA_High_Max_50_Lag_4", "STBP3.SA_High_Max_50_Lag_5", "STBP3.SA_Low_Min_50_Lag_1", "STBP3.SA_Low_Min_50_Lag_2", "STBP3.SA_Low_Min_50_Lag_3", "STBP3.SA_Low_Min_50_Lag_4", "STBP3.SA_Low_Min_50_Lag_5", "STBP3.SA_MFI_Lag_1", "STBP3.SA_MFI_Lag_2", "STBP3.SA_MFI_Lag_3", "STBP3.SA_MFI_Lag_4", "STBP3.SA_MFI_Lag_5", "STBP3.SA_Amihud_Lag_1", "STBP3.SA_Amihud_Lag_2", "STBP3.SA_Amihud_Lag_3", "STBP3.SA_Amihud_Lag_4", "STBP3.SA_Amihud_Lag_5", "STBP3.SA_Roll_Spread_Lag_1", "STBP3.SA_Roll_Spread_Lag_2", "STBP3.SA_Roll_Spread_Lag_3", "STBP3.SA_Roll_Spread_Lag_4", "STBP3.SA_Roll_Spread_Lag_5", "STBP3.SA_Hurst_Lag_1", "STBP3.SA_Hurst_Lag_2", "STBP3.SA_Hurst_Lag_3", "STBP3.SA_Hurst_Lag_4", "STBP3.<PERSON>_<PERSON><PERSON>_Lag_5", "STBP3.SA_Vol_per_Volume_Lag_1", "STBP3.SA_Vol_per_Volume_Lag_2", "STBP3.SA_Vol_per_Volume_Lag_3", "STBP3.SA_Vol_per_Volume_Lag_4", "STBP3.SA_Vol_per_Volume_Lag_5", "STBP3.SA_CMF_Lag_1", "STBP3.SA_CMF_Lag_2", "STBP3.SA_CMF_Lag_3", "STBP3.SA_CMF_Lag_4", "STBP3.SA_CMF_Lag_5", "STBP3.SA_AD_Line_Lag_1", "STBP3.SA_AD_Line_Lag_2", "STBP3.SA_AD_Line_Lag_3", "STBP3.SA_AD_Line_Lag_4", "STBP3.SA_AD_Line_Lag_5", "SLCE3.SA_Open", "SLCE3.SA_High", "SLCE3.SA_Low", "SLCE3.SA_Close", "SLCE3.SA_Volume", "SLCE3.SA_Media_OHLC", "SLCE3.SA_MM10", "SLCE3.SA_MM25", "SLCE3.SA_MM100", "SLCE3.SA_Media_OHLC_PctChange", "SLCE3.SA_Volatilidade", "SLCE3.SA_Spread", "SLCE3.SA_Parkinson_Volatility", "SLCE3.SA_MFI", "SLCE3.SA_MFI_Historical", "SLCE3.SA_EMV", "SLCE3.SA_EMV_MA", "SLCE3.SA_Amihud", "SLCE3.SA_Amihud_Historical", "SLCE3.SA_Roll_Spread", "SLCE3.SA_Roll_Spread_Historical", "SLCE3.SA_Hurst", "SLCE3.SA_<PERSON><PERSON>_Historical", "SLCE3.SA_Vol_per_Volume", "SLCE3.SA_Vol_per_Volume_Historical", "SLCE3.SA_CMF", "SLCE3.SA_CMF_Historical", "SLCE3.SA_AD_Line", "SLCE3.SA_AD_Line_Historical", "SLCE3.SA_VO", "SLCE3.SA_High_Max_50", "SLCE3.SA_Low_Min_50", "SLCE3.SA_Segunda", "SLCE3.SA_Terca", "SLCE3.SA_Quarta", "SLCE3.SA_Quinta", "SLCE3.SA_Sexta", "SLCE3.SA_Mes_1", "SLCE3.SA_Mes_2", "SLCE3.SA_Mes_3", "SLCE3.SA_Mes_4", "SLCE3.SA_Mes_5", "SLCE3.SA_Mes_6", "SLCE3.SA_Mes_7", "SLCE3.SA_Mes_8", "SLCE3.SA_Mes_9", "SLCE3.SA_Mes_10", "SLCE3.SA_Mes_11", "SLCE3.SA_Mes_12", "SLCE3.SA_Quarter_1", "SLCE3.SA_Quarter_2", "SLCE3.SA_Quarter_3", "SLCE3.SA_Quarter_4", "SLCE3.SA_Last_Day_Quarter", "SLCE3.SA_Pre_Feriado_Brasil", "SLCE3.SA_Media_OHLC_Anterior", "SLCE3.SA_Sinal_Compra", "SLCE3.SA_Sinal_Venda", "SLCE3.SA_Media_OHLC_Futura", "SLCE3.SA_Media_OHLC_PctChange_Lag_1", "SLCE3.SA_Media_OHLC_PctChange_Lag_2", "SLCE3.SA_Media_OHLC_PctChange_Lag_3", "SLCE3.SA_Media_OHLC_PctChange_Lag_4", "SLCE3.SA_Media_OHLC_PctChange_Lag_5", "SLCE3.SA_Media_OHLC_PctChange_Lag_6", "SLCE3.SA_Media_OHLC_PctChange_Lag_7", "SLCE3.SA_Media_OHLC_PctChange_Lag_8", "SLCE3.SA_Media_OHLC_PctChange_Lag_9", "SLCE3.SA_Media_OHLC_PctChange_Lag_10", "SLCE3.SA_MM10_PctChange", "SLCE3.SA_Diff_OHLC_MM10", "SLCE3.SA_MM25_PctChange", "SLCE3.SA_Diff_OHLC_MM25", "SLCE3.SA_MM100_PctChange", "SLCE3.SA_Diff_OHLC_MM100", "SLCE3.SA_MM10_Menos_MM25", "SLCE3.SA_MM25_Menos_MM100", "SLCE3.SA_MM10_Menos_MM100", "SLCE3.SA_Volume_Lag_1", "SLCE3.SA_Volume_Lag_2", "SLCE3.SA_Volume_Lag_3", "SLCE3.SA_Volume_Lag_4", "SLCE3.SA_Volume_Lag_5", "SLCE3.SA_Spread_Lag_1", "SLCE3.SA_Spread_Lag_2", "SLCE3.SA_Spread_Lag_3", "SLCE3.SA_Spread_Lag_4", "SLCE3.SA_Spread_Lag_5", "SLCE3.SA_Volatilidade_Lag_1", "SLCE3.SA_Volatilidade_Lag_2", "SLCE3.SA_Volatilidade_Lag_3", "SLCE3.SA_Volatilidade_Lag_4", "SLCE3.SA_Volatilidade_Lag_5", "SLCE3.SA_Parkinson_Volatility_Lag_1", "SLCE3.SA_Parkinson_Volatility_Lag_2", "SLCE3.SA_Parkinson_Volatility_Lag_3", "SLCE3.SA_Parkinson_Volatility_Lag_4", "SLCE3.SA_Parkinson_Volatility_Lag_5", "SLCE3.SA_EMV_Lag_1", "SLCE3.SA_EMV_Lag_2", "SLCE3.SA_EMV_Lag_3", "SLCE3.SA_EMV_Lag_4", "SLCE3.SA_EMV_Lag_5", "SLCE3.SA_EMV_MA_Lag_1", "SLCE3.SA_EMV_MA_Lag_2", "SLCE3.SA_EMV_MA_Lag_3", "SLCE3.SA_EMV_MA_Lag_4", "SLCE3.SA_EMV_MA_Lag_5", "SLCE3.SA_VO_Lag_1", "SLCE3.SA_VO_Lag_2", "SLCE3.SA_VO_Lag_3", "SLCE3.SA_VO_Lag_4", "SLCE3.SA_VO_Lag_5", "SLCE3.SA_High_Max_50_Lag_1", "SLCE3.SA_High_Max_50_Lag_2", "SLCE3.SA_High_Max_50_Lag_3", "SLCE3.SA_High_Max_50_Lag_4", "SLCE3.SA_High_Max_50_Lag_5", "SLCE3.SA_Low_Min_50_Lag_1", "SLCE3.SA_Low_Min_50_Lag_2", "SLCE3.SA_Low_Min_50_Lag_3", "SLCE3.SA_Low_Min_50_Lag_4", "SLCE3.SA_Low_Min_50_Lag_5", "SLCE3.SA_MFI_Lag_1", "SLCE3.SA_MFI_Lag_2", "SLCE3.SA_MFI_Lag_3", "SLCE3.SA_MFI_Lag_4", "SLCE3.SA_MFI_Lag_5", "SLCE3.SA_Amihud_Lag_1", "SLCE3.SA_Amihud_Lag_2", "SLCE3.SA_Amihud_Lag_3", "SLCE3.SA_Amihud_Lag_4", "SLCE3.SA_Amihud_Lag_5", "SLCE3.SA_Roll_Spread_Lag_1", "SLCE3.SA_Roll_Spread_Lag_2", "SLCE3.SA_Roll_Spread_Lag_3", "SLCE3.SA_Roll_Spread_Lag_4", "SLCE3.SA_Roll_Spread_Lag_5", "SLCE3.SA_Hurst_Lag_1", "SLCE3.SA_Hurst_Lag_2", "SLCE3.SA_<PERSON>rst_Lag_3", "SLCE3.SA_<PERSON>rst_Lag_4", "SLCE3.<PERSON>_<PERSON><PERSON>_Lag_5", "SLCE3.SA_Vol_per_Volume_Lag_1", "SLCE3.SA_Vol_per_Volume_Lag_2", "SLCE3.SA_Vol_per_Volume_Lag_3", "SLCE3.SA_Vol_per_Volume_Lag_4", "SLCE3.SA_Vol_per_Volume_Lag_5", "SLCE3.SA_CMF_Lag_1", "SLCE3.SA_CMF_Lag_2", "SLCE3.SA_CMF_Lag_3", "SLCE3.SA_CMF_Lag_4", "SLCE3.SA_CMF_Lag_5", "SLCE3.SA_AD_Line_Lag_1", "SLCE3.SA_AD_Line_Lag_2", "SLCE3.SA_AD_Line_Lag_3", "SLCE3.SA_AD_Line_Lag_4", "SLCE3.SA_AD_Line_Lag_5", "MRFG3.SA_Open", "MRFG3.SA_High", "MRFG3.SA_Low", "MRFG3.SA_Close", "MRFG3.SA_Volume", "MRFG3.SA_Media_OHLC", "MRFG3.SA_MM10", "MRFG3.SA_MM25", "MRFG3.SA_MM100", "MRFG3.SA_Media_OHLC_PctChange", "MRFG3.SA_Volatilidade", "MRFG3.SA_Spread", "MRFG3.SA_Parkinson_Volatility", "MRFG3.SA_MFI", "MRFG3.SA_MFI_Historical", "MRFG3.SA_EMV", "MRFG3.SA_EMV_MA", "MRFG3.SA_Amihud", "MRFG3.SA_Amihud_Historical", "MRFG3.SA_Roll_Spread", "MRFG3.SA_Roll_Spread_Historical", "MRFG3.SA_Hurst", "MRFG3.SA_<PERSON><PERSON>_Historical", "MRFG3.SA_Vol_per_Volume", "MRFG3.SA_Vol_per_Volume_Historical", "MRFG3.SA_CMF", "MRFG3.SA_CMF_Historical", "MRFG3.SA_AD_Line", "MRFG3.SA_AD_Line_Historical", "MRFG3.SA_VO", "MRFG3.SA_High_Max_50", "MRFG3.SA_Low_Min_50", "MRFG3.SA_Segunda", "MRFG3.SA_Terca", "MRFG3.SA_Quarta", "MRFG3.SA_Quinta", "MRFG3.SA_Sexta", "MRFG3.SA_Mes_1", "MRFG3.SA_Mes_2", "MRFG3.SA_Mes_3", "MRFG3.SA_Mes_4", "MRFG3.SA_Mes_5", "MRFG3.SA_Mes_6", "MRFG3.SA_Mes_7", "MRFG3.SA_Mes_8", "MRFG3.SA_Mes_9", "MRFG3.SA_Mes_10", "MRFG3.SA_Mes_11", "MRFG3.SA_Mes_12", "MRFG3.SA_Quarter_1", "MRFG3.SA_Quarter_2", "MRFG3.SA_Quarter_3", "MRFG3.SA_Quarter_4", "MRFG3.SA_Last_Day_Quarter", "MRFG3.SA_Pre_Feriado_Brasil", "MRFG3.SA_Media_OHLC_Anterior", "MRFG3.SA_Sinal_Compra", "MRFG3.SA_Sinal_Venda", "MRFG3.SA_Media_OHLC_Futura", "MRFG3.SA_Media_OHLC_PctChange_Lag_1", "MRFG3.SA_Media_OHLC_PctChange_Lag_2", "MRFG3.SA_Media_OHLC_PctChange_Lag_3", "MRFG3.SA_Media_OHLC_PctChange_Lag_4", "MRFG3.SA_Media_OHLC_PctChange_Lag_5", "MRFG3.SA_Media_OHLC_PctChange_Lag_6", "MRFG3.SA_Media_OHLC_PctChange_Lag_7", "MRFG3.SA_Media_OHLC_PctChange_Lag_8", "MRFG3.SA_Media_OHLC_PctChange_Lag_9", "MRFG3.SA_Media_OHLC_PctChange_Lag_10", "MRFG3.SA_MM10_PctChange", "MRFG3.SA_Diff_OHLC_MM10", "MRFG3.SA_MM25_PctChange", "MRFG3.SA_Diff_OHLC_MM25", "MRFG3.SA_MM100_PctChange", "MRFG3.SA_Diff_OHLC_MM100", "MRFG3.SA_MM10_Menos_MM25", "MRFG3.SA_MM25_Menos_MM100", "MRFG3.SA_MM10_Menos_MM100", "MRFG3.SA_Volume_Lag_1", "MRFG3.SA_Volume_Lag_2", "MRFG3.SA_Volume_Lag_3", "MRFG3.SA_Volume_Lag_4", "MRFG3.SA_Volume_Lag_5", "MRFG3.SA_Spread_Lag_1", "MRFG3.SA_Spread_Lag_2", "MRFG3.SA_Spread_Lag_3", "MRFG3.SA_Spread_Lag_4", "MRFG3.SA_Spread_Lag_5", "MRFG3.SA_Volatilidade_Lag_1", "MRFG3.SA_Volatilidade_Lag_2", "MRFG3.SA_Volatilidade_Lag_3", "MRFG3.SA_Volatilidade_Lag_4", "MRFG3.SA_Volatilidade_Lag_5", "MRFG3.SA_Parkinson_Volatility_Lag_1", "MRFG3.SA_Parkinson_Volatility_Lag_2", "MRFG3.SA_Parkinson_Volatility_Lag_3", "MRFG3.SA_Parkinson_Volatility_Lag_4", "MRFG3.SA_Parkinson_Volatility_Lag_5", "MRFG3.SA_EMV_Lag_1", "MRFG3.SA_EMV_Lag_2", "MRFG3.SA_EMV_Lag_3", "MRFG3.SA_EMV_Lag_4", "MRFG3.SA_EMV_Lag_5", "MRFG3.SA_EMV_MA_Lag_1", "MRFG3.SA_EMV_MA_Lag_2", "MRFG3.SA_EMV_MA_Lag_3", "MRFG3.SA_EMV_MA_Lag_4", "MRFG3.SA_EMV_MA_Lag_5", "MRFG3.SA_VO_Lag_1", "MRFG3.SA_VO_Lag_2", "MRFG3.SA_VO_Lag_3", "MRFG3.SA_VO_Lag_4", "MRFG3.SA_VO_Lag_5", "MRFG3.SA_High_Max_50_Lag_1", "MRFG3.SA_High_Max_50_Lag_2", "MRFG3.SA_High_Max_50_Lag_3", "MRFG3.SA_High_Max_50_Lag_4", "MRFG3.SA_High_Max_50_Lag_5", "MRFG3.SA_Low_Min_50_Lag_1", "MRFG3.SA_Low_Min_50_Lag_2", "MRFG3.SA_Low_Min_50_Lag_3", "MRFG3.SA_Low_Min_50_Lag_4", "MRFG3.SA_Low_Min_50_Lag_5", "MRFG3.SA_MFI_Lag_1", "MRFG3.SA_MFI_Lag_2", "MRFG3.SA_MFI_Lag_3", "MRFG3.SA_MFI_Lag_4", "MRFG3.SA_MFI_Lag_5", "MRFG3.SA_Amihud_Lag_1", "MRFG3.SA_Amihud_Lag_2", "MRFG3.SA_Amihud_Lag_3", "MRFG3.SA_Amihud_Lag_4", "MRFG3.SA_Amihud_Lag_5", "MRFG3.SA_Roll_Spread_Lag_1", "MRFG3.SA_Roll_Spread_Lag_2", "MRFG3.SA_Roll_Spread_Lag_3", "MRFG3.SA_Roll_Spread_Lag_4", "MRFG3.SA_Roll_Spread_Lag_5", "MRFG3.SA_Hurst_Lag_1", "MRFG3.<PERSON>_Hurst_Lag_2", "MRFG3.SA_<PERSON>rst_Lag_3", "MRFG3.<PERSON>_<PERSON><PERSON>_Lag_4", "MRFG3.<PERSON>_<PERSON><PERSON>_Lag_5", "MRFG3.SA_Vol_per_Volume_Lag_1", "MRFG3.SA_Vol_per_Volume_Lag_2", "MRFG3.SA_Vol_per_Volume_Lag_3", "MRFG3.SA_Vol_per_Volume_Lag_4", "MRFG3.SA_Vol_per_Volume_Lag_5", "MRFG3.SA_CMF_Lag_1", "MRFG3.SA_CMF_Lag_2", "MRFG3.SA_CMF_Lag_3", "MRFG3.SA_CMF_Lag_4", "MRFG3.SA_CMF_Lag_5", "MRFG3.SA_AD_Line_Lag_1", "MRFG3.SA_AD_Line_Lag_2", "MRFG3.SA_AD_Line_Lag_3", "MRFG3.SA_AD_Line_Lag_4", "MRFG3.SA_AD_Line_Lag_5", "SUZB3.SA_Open", "SUZB3.SA_High", "SUZB3.SA_Low", "SUZB3.SA_Close", "SUZB3.SA_Volume", "SUZB3.SA_Media_OHLC", "SUZB3.SA_MM10", "SUZB3.SA_MM25", "SUZB3.SA_MM100", "SUZB3.SA_Media_OHLC_PctChange", "SUZB3.SA_Volatilidade", "SUZB3.SA_Spread", "SUZB3.SA_Parkinson_Volatility", "SUZB3.SA_MFI", "SUZB3.SA_MFI_Historical", "SUZB3.SA_EMV", "SUZB3.SA_EMV_MA", "SUZB3.SA_Amihud", "SUZB3.SA_Amihud_Historical", "SUZB3.SA_Roll_Spread", "SUZB3.SA_Roll_Spread_Historical", "SUZB3.SA_Hurst", "SUZB3.SA_Hurst_Historical", "SUZB3.SA_Vol_per_Volume", "SUZB3.SA_Vol_per_Volume_Historical", "SUZB3.SA_CMF", "SUZB3.SA_CMF_Historical", "SUZB3.SA_AD_Line", "SUZB3.SA_AD_Line_Historical", "SUZB3.SA_VO", "SUZB3.SA_High_Max_50", "SUZB3.SA_Low_Min_50", "SUZB3.SA_Segunda", "SUZB3.SA_Terca", "SUZB3.SA_Quarta", "SUZB3.SA_Quinta", "SUZB3.SA_Sexta", "SUZB3.SA_Mes_1", "SUZB3.SA_Mes_2", "SUZB3.SA_Mes_3", "SUZB3.SA_Mes_4", "SUZB3.SA_Mes_5", "SUZB3.SA_Mes_6", "SUZB3.SA_Mes_7", "SUZB3.SA_Mes_8", "SUZB3.SA_Mes_9", "SUZB3.SA_Mes_10", "SUZB3.SA_Mes_11", "SUZB3.SA_Mes_12", "SUZB3.SA_Quarter_1", "SUZB3.SA_Quarter_2", "SUZB3.SA_Quarter_3", "SUZB3.SA_Quarter_4", "SUZB3.SA_Last_Day_Quarter", "SUZB3.SA_Pre_Feriado_Brasil", "SUZB3.SA_Media_OHLC_Anterior", "SUZB3.SA_Sinal_Compra", "SUZB3.SA_Sinal_Venda", "SUZB3.SA_Media_OHLC_Futura", "SUZB3.SA_Media_OHLC_PctChange_Lag_1", "SUZB3.SA_Media_OHLC_PctChange_Lag_2", "SUZB3.SA_Media_OHLC_PctChange_Lag_3", "SUZB3.SA_Media_OHLC_PctChange_Lag_4", "SUZB3.SA_Media_OHLC_PctChange_Lag_5", "SUZB3.SA_Media_OHLC_PctChange_Lag_6", "SUZB3.SA_Media_OHLC_PctChange_Lag_7", "SUZB3.SA_Media_OHLC_PctChange_Lag_8", "SUZB3.SA_Media_OHLC_PctChange_Lag_9", "SUZB3.SA_Media_OHLC_PctChange_Lag_10", "SUZB3.SA_MM10_PctChange", "SUZB3.SA_Diff_OHLC_MM10", "SUZB3.SA_MM25_PctChange", "SUZB3.SA_Diff_OHLC_MM25", "SUZB3.SA_MM100_PctChange", "SUZB3.SA_Diff_OHLC_MM100", "SUZB3.SA_MM10_Menos_MM25", "SUZB3.SA_MM25_Menos_MM100", "SUZB3.SA_MM10_Menos_MM100", "SUZB3.SA_Volume_Lag_1", "SUZB3.SA_Volume_Lag_2", "SUZB3.SA_Volume_Lag_3", "SUZB3.SA_Volume_Lag_4", "SUZB3.SA_Volume_Lag_5", "SUZB3.SA_Spread_Lag_1", "SUZB3.SA_Spread_Lag_2", "SUZB3.SA_Spread_Lag_3", "SUZB3.SA_Spread_Lag_4", "SUZB3.SA_Spread_Lag_5", "SUZB3.SA_Volatilidade_Lag_1", "SUZB3.SA_Volatilidade_Lag_2", "SUZB3.SA_Volatilidade_Lag_3", "SUZB3.SA_Volatilidade_Lag_4", "SUZB3.SA_Volatilidade_Lag_5", "SUZB3.SA_Parkinson_Volatility_Lag_1", "SUZB3.SA_Parkinson_Volatility_Lag_2", "SUZB3.SA_Parkinson_Volatility_Lag_3", "SUZB3.SA_Parkinson_Volatility_Lag_4", "SUZB3.<PERSON>_Parkinson_Volatility_Lag_5", "SUZB3.SA_EMV_Lag_1", "SUZB3.SA_EMV_Lag_2", "SUZB3.SA_EMV_Lag_3", "SUZB3.SA_EMV_Lag_4", "SUZB3.SA_EMV_Lag_5", "SUZB3.SA_EMV_MA_Lag_1", "SUZB3.SA_EMV_MA_Lag_2", "SUZB3.SA_EMV_MA_Lag_3", "SUZB3.SA_EMV_MA_Lag_4", "SUZB3.SA_EMV_MA_Lag_5", "SUZB3.SA_VO_Lag_1", "SUZB3.SA_VO_Lag_2", "SUZB3.SA_VO_Lag_3", "SUZB3.SA_VO_Lag_4", "SUZB3.SA_VO_Lag_5", "SUZB3.SA_High_Max_50_Lag_1", "SUZB3.SA_High_Max_50_Lag_2", "SUZB3.SA_High_Max_50_Lag_3", "SUZB3.SA_High_Max_50_Lag_4", "SUZB3.SA_High_Max_50_Lag_5", "SUZB3.SA_Low_Min_50_Lag_1", "SUZB3.SA_Low_Min_50_Lag_2", "SUZB3.SA_Low_Min_50_Lag_3", "SUZB3.SA_Low_Min_50_Lag_4", "SUZB3.SA_Low_Min_50_Lag_5", "SUZB3.SA_MFI_Lag_1", "SUZB3.SA_MFI_Lag_2", "SUZB3.SA_MFI_Lag_3", "SUZB3.SA_MFI_Lag_4", "SUZB3.SA_MFI_Lag_5", "SUZB3.SA_Amihud_Lag_1", "SUZB3.SA_Amihud_Lag_2", "SUZB3.SA_Amihud_Lag_3", "SUZB3.SA_Amihud_Lag_4", "SUZB3.SA_Amihud_Lag_5", "SUZB3.SA_Roll_Spread_Lag_1", "SUZB3.SA_Roll_Spread_Lag_2", "SUZB3.SA_Roll_Spread_Lag_3", "SUZB3.SA_Roll_Spread_Lag_4", "SUZB3.SA_Roll_Spread_Lag_5", "SUZB3.SA_Hurst_Lag_1", "SUZB3.SA_Hurst_Lag_2", "SUZB3.SA_Hurst_Lag_3", "SUZB3.SA_Hurst_Lag_4", "SUZB3.SA_<PERSON>rst_Lag_5", "SUZB3.SA_Vol_per_Volume_Lag_1", "SUZB3.SA_Vol_per_Volume_Lag_2", "SUZB3.SA_Vol_per_Volume_Lag_3", "SUZB3.SA_Vol_per_Volume_Lag_4", "SUZB3.SA_Vol_per_Volume_Lag_5", "SUZB3.SA_CMF_Lag_1", "SUZB3.SA_CMF_Lag_2", "SUZB3.SA_CMF_Lag_3", "SUZB3.SA_CMF_Lag_4", "SUZB3.SA_CMF_Lag_5", "SUZB3.SA_AD_Line_Lag_1", "SUZB3.SA_AD_Line_Lag_2", "SUZB3.SA_AD_Line_Lag_3", "SUZB3.SA_AD_Line_Lag_4", "SUZB3.SA_AD_Line_Lag_5", "BRFS3.SA_Open", "BRFS3.SA_High", "BRFS3.SA_Low", "BRFS3.SA_Close", "BRFS3.SA_Volume", "BRFS3.SA_Media_OHLC", "BRFS3.SA_MM10", "BRFS3.SA_MM25", "BRFS3.SA_MM100", "BRFS3.SA_Media_OHLC_PctChange", "BRFS3.SA_Volatilidade", "BRFS3.SA_Spread", "BRFS3.SA_Parkinson_Volatility", "BRFS3.SA_MFI", "BRFS3.SA_MFI_Historical", "BRFS3.SA_EMV", "BRFS3.SA_EMV_MA", "BRFS3.SA_Amihud", "BRFS3.SA_Amihud_Historical", "BRFS3.SA_Roll_Spread", "BRFS3.SA_Roll_Spread_Historical", "BRFS3.SA_Hurst", "BRFS3.SA_Hurst_Historical", "BRFS3.SA_Vol_per_Volume", "BRFS3.SA_Vol_per_Volume_Historical", "BRFS3.SA_CMF", "BRFS3.SA_CMF_Historical", "BRFS3.SA_AD_Line", "BRFS3.SA_AD_Line_Historical", "BRFS3.SA_VO", "BRFS3.SA_High_Max_50", "BRFS3.SA_Low_Min_50", "BRFS3.SA_Segunda", "BRFS3.SA_Terca", "BRFS3.SA_Quarta", "BRFS3.SA_Quinta", "BRFS3.SA_Sexta", "BRFS3.SA_Mes_1", "BRFS3.SA_Mes_2", "BRFS3.SA_Mes_3", "BRFS3.SA_Mes_4", "BRFS3.SA_Mes_5", "BRFS3.SA_Mes_6", "BRFS3.SA_Mes_7", "BRFS3.SA_Mes_8", "BRFS3.SA_Mes_9", "BRFS3.SA_Mes_10", "BRFS3.SA_Mes_11", "BRFS3.SA_Mes_12", "BRFS3.SA_Quarter_1", "BRFS3.SA_Quarter_2", "BRFS3.SA_Quarter_3", "BRFS3.SA_Quarter_4", "BRFS3.SA_Last_Day_Quarter", "BRFS3.SA_Pre_Feriado_Brasil", "BRFS3.SA_Media_OHLC_Anterior", "BRFS3.SA_Sinal_Compra", "BRFS3.SA_Sinal_Venda", "BRFS3.SA_Media_OHLC_Futura", "BRFS3.SA_Media_OHLC_PctChange_Lag_1", "BRFS3.SA_Media_OHLC_PctChange_Lag_2", "BRFS3.SA_Media_OHLC_PctChange_Lag_3", "BRFS3.SA_Media_OHLC_PctChange_Lag_4", "BRFS3.SA_Media_OHLC_PctChange_Lag_5", "BRFS3.SA_Media_OHLC_PctChange_Lag_6", "BRFS3.SA_Media_OHLC_PctChange_Lag_7", "BRFS3.SA_Media_OHLC_PctChange_Lag_8", "BRFS3.SA_Media_OHLC_PctChange_Lag_9", "BRFS3.SA_Media_OHLC_PctChange_Lag_10", "BRFS3.SA_MM10_PctChange", "BRFS3.SA_Diff_OHLC_MM10", "BRFS3.SA_MM25_PctChange", "BRFS3.SA_Diff_OHLC_MM25", "BRFS3.SA_MM100_PctChange", "BRFS3.SA_Diff_OHLC_MM100", "BRFS3.SA_MM10_Menos_MM25", "BRFS3.SA_MM25_Menos_MM100", "BRFS3.SA_MM10_Menos_MM100", "BRFS3.SA_Volume_Lag_1", "BRFS3.SA_Volume_Lag_2", "BRFS3.SA_Volume_Lag_3", "BRFS3.SA_Volume_Lag_4", "BRFS3.SA_Volume_Lag_5", "BRFS3.SA_Spread_Lag_1", "BRFS3.SA_Spread_Lag_2", "BRFS3.SA_Spread_Lag_3", "BRFS3.SA_Spread_Lag_4", "BRFS3.SA_Spread_Lag_5", "BRFS3.SA_Volatilidade_Lag_1", "BRFS3.SA_Volatilidade_Lag_2", "BRFS3.SA_Volatilidade_Lag_3", "BRFS3.SA_Volatilidade_Lag_4", "BRFS3.SA_Volatilidade_Lag_5", "BRFS3.SA_Parkinson_Volatility_Lag_1", "BRFS3.SA_Parkinson_Volatility_Lag_2", "BRFS3.SA_Parkinson_Volatility_Lag_3", "BRFS3.SA_Parkinson_Volatility_Lag_4", "BRFS3.SA_Parkinson_Volatility_Lag_5", "BRFS3.SA_EMV_Lag_1", "BRFS3.SA_EMV_Lag_2", "BRFS3.SA_EMV_Lag_3", "BRFS3.SA_EMV_Lag_4", "BRFS3.SA_EMV_Lag_5", "BRFS3.SA_EMV_MA_Lag_1", "BRFS3.SA_EMV_MA_Lag_2", "BRFS3.SA_EMV_MA_Lag_3", "BRFS3.SA_EMV_MA_Lag_4", "BRFS3.SA_EMV_MA_Lag_5", "BRFS3.SA_VO_Lag_1", "BRFS3.SA_VO_Lag_2", "BRFS3.SA_VO_Lag_3", "BRFS3.SA_VO_Lag_4", "BRFS3.SA_VO_Lag_5", "BRFS3.SA_High_Max_50_Lag_1", "BRFS3.SA_High_Max_50_Lag_2", "BRFS3.SA_High_Max_50_Lag_3", "BRFS3.SA_High_Max_50_Lag_4", "BRFS3.SA_High_Max_50_Lag_5", "BRFS3.SA_Low_Min_50_Lag_1", "BRFS3.SA_Low_Min_50_Lag_2", "BRFS3.SA_Low_Min_50_Lag_3", "BRFS3.SA_Low_Min_50_Lag_4", "BRFS3.SA_Low_Min_50_Lag_5", "BRFS3.SA_MFI_Lag_1", "BRFS3.SA_MFI_Lag_2", "BRFS3.SA_MFI_Lag_3", "BRFS3.SA_MFI_Lag_4", "BRFS3.SA_MFI_Lag_5", "BRFS3.SA_Amihud_Lag_1", "BRFS3.SA_Amihud_Lag_2", "BRFS3.SA_Amihud_Lag_3", "BRFS3.SA_Amihud_Lag_4", "BRFS3.SA_Amihud_Lag_5", "BRFS3.SA_Roll_Spread_Lag_1", "BRFS3.SA_Roll_Spread_Lag_2", "BRFS3.SA_Roll_Spread_Lag_3", "BRFS3.SA_Roll_Spread_Lag_4", "BRFS3.SA_Roll_Spread_Lag_5", "BRFS3.SA_Hurst_Lag_1", "BRFS3.SA_Hurst_Lag_2", "BRFS3.SA_Hurst_Lag_3", "BRFS3.SA_Hurst_Lag_4", "BRFS3.SA_<PERSON>rst_Lag_5", "BRFS3.SA_Vol_per_Volume_Lag_1", "BRFS3.SA_Vol_per_Volume_Lag_2", "BRFS3.SA_Vol_per_Volume_Lag_3", "BRFS3.SA_Vol_per_Volume_Lag_4", "BRFS3.SA_Vol_per_Volume_Lag_5", "BRFS3.SA_CMF_Lag_1", "BRFS3.SA_CMF_Lag_2", "BRFS3.SA_CMF_Lag_3", "BRFS3.SA_CMF_Lag_4", "BRFS3.SA_CMF_Lag_5", "BRFS3.SA_AD_Line_Lag_1", "BRFS3.SA_AD_Line_Lag_2", "BRFS3.SA_AD_Line_Lag_3", "BRFS3.SA_AD_Line_Lag_4", "BRFS3.SA_AD_Line_Lag_5", "PORT3.SA_Close", "PORT3.SA_High", "PORT3.SA_Low", "PORT3.SA_Open", "PORT3.SA_Volume", "PORT3.SA_Media_OHLC", "PORT3.SA_MM10", "PORT3.SA_MM25", "PORT3.SA_MM100", "PORT3.SA_Media_OHLC_PctChange", "PORT3.SA_Volatilidade", "PORT3.SA_Spread", "PORT3.SA_Parkinson_Volatility", "PORT3.SA_MFI", "PORT3.SA_MFI_Historical", "PORT3.SA_EMV", "PORT3.SA_EMV_MA", "PORT3.SA_Amihud", "PORT3.SA_Amihud_Historical", "PORT3.SA_Roll_Spread", "PORT3.SA_Roll_Spread_Historical", "PORT3.SA_Hurst", "PORT3.<PERSON>_<PERSON><PERSON>_Historical", "PORT3.SA_Vol_per_Volume", "PORT3.SA_Vol_per_Volume_Historical", "PORT3.SA_CMF", "PORT3.SA_CMF_Historical", "PORT3.SA_AD_Line", "PORT3.SA_AD_Line_Historical", "PORT3.SA_VO", "PORT3.SA_High_Max_50", "PORT3.SA_Low_Min_50", "PORT3.SA_Segunda", "PORT3.SA_Terca", "PORT3.SA_Quarta", "PORT3.SA_Quinta", "PORT3.SA_Sexta", "PORT3.SA_Mes_1", "PORT3.SA_Mes_2", "PORT3.SA_Mes_3", "PORT3.SA_Mes_4", "PORT3.SA_Mes_5", "PORT3.SA_Mes_6", "PORT3.SA_Mes_7", "PORT3.SA_Mes_8", "PORT3.SA_Mes_9", "PORT3.SA_Mes_10", "PORT3.SA_Mes_11", "PORT3.SA_Mes_12", "PORT3.SA_Quarter_1", "PORT3.SA_Quarter_2", "PORT3.SA_Quarter_3", "PORT3.SA_Quarter_4", "PORT3.SA_Last_Day_Quarter", "PORT3.SA_Pre_Feriado_Brasil", "PORT3.SA_Media_OHLC_Anterior", "PORT3.SA_Sinal_Compra", "PORT3.SA_Sinal_Venda", "PORT3.SA_Media_OHLC_Futura", "PORT3.SA_Media_OHLC_PctChange_Lag_1", "PORT3.SA_Media_OHLC_PctChange_Lag_2", "PORT3.SA_Media_OHLC_PctChange_Lag_3", "PORT3.SA_Media_OHLC_PctChange_Lag_4", "PORT3.SA_Media_OHLC_PctChange_Lag_5", "PORT3.SA_Media_OHLC_PctChange_Lag_6", "PORT3.SA_Media_OHLC_PctChange_Lag_7", "PORT3.SA_Media_OHLC_PctChange_Lag_8", "PORT3.SA_Media_OHLC_PctChange_Lag_9", "PORT3.SA_Media_OHLC_PctChange_Lag_10", "PORT3.SA_MM10_PctChange", "PORT3.SA_Diff_OHLC_MM10", "PORT3.SA_MM25_PctChange", "PORT3.SA_Diff_OHLC_MM25", "PORT3.SA_MM100_PctChange", "PORT3.SA_Diff_OHLC_MM100", "PORT3.SA_MM10_Menos_MM25", "PORT3.SA_MM25_Menos_MM100", "PORT3.SA_MM10_Menos_MM100", "PORT3.SA_Volume_Lag_1", "PORT3.SA_Volume_Lag_2", "PORT3.SA_Volume_Lag_3", "PORT3.SA_Volume_Lag_4", "PORT3.SA_Volume_Lag_5", "PORT3.SA_Spread_Lag_1", "PORT3.SA_Spread_Lag_2", "PORT3.SA_Spread_Lag_3", "PORT3.SA_Spread_Lag_4", "PORT3.SA_Spread_Lag_5", "PORT3.SA_Volatilidade_Lag_1", "PORT3.SA_Volatilidade_Lag_2", "PORT3.SA_Volatilidade_Lag_3", "PORT3.SA_Volatilidade_Lag_4", "PORT3.SA_Volatilidade_Lag_5", "PORT3.SA_Parkinson_Volatility_Lag_1", "PORT3.<PERSON>_Parkinson_Volatility_Lag_2", "PORT3.SA_Parkinson_Volatility_Lag_3", "PORT3.SA_Parkinson_Volatility_Lag_4", "PORT3.<PERSON>_Parkinson_Volatility_Lag_5", "PORT3.SA_EMV_Lag_1", "PORT3.SA_EMV_Lag_2", "PORT3.SA_EMV_Lag_3", "PORT3.SA_EMV_Lag_4", "PORT3.SA_EMV_Lag_5", "PORT3.SA_EMV_MA_Lag_1", "PORT3.SA_EMV_MA_Lag_2", "PORT3.SA_EMV_MA_Lag_3", "PORT3.SA_EMV_MA_Lag_4", "PORT3.SA_EMV_MA_Lag_5", "PORT3.SA_VO_Lag_1", "PORT3.SA_VO_Lag_2", "PORT3.SA_VO_Lag_3", "PORT3.SA_VO_Lag_4", "PORT3.SA_VO_Lag_5", "PORT3.SA_High_Max_50_Lag_1", "PORT3.SA_High_Max_50_Lag_2", "PORT3.SA_High_Max_50_Lag_3", "PORT3.SA_High_Max_50_Lag_4", "PORT3.SA_High_Max_50_Lag_5", "PORT3.SA_Low_Min_50_Lag_1", "PORT3.SA_Low_Min_50_Lag_2", "PORT3.SA_Low_Min_50_Lag_3", "PORT3.SA_Low_Min_50_Lag_4", "PORT3.SA_Low_Min_50_Lag_5", "PORT3.SA_MFI_Lag_1", "PORT3.SA_MFI_Lag_2", "PORT3.SA_MFI_Lag_3", "PORT3.SA_MFI_Lag_4", "PORT3.SA_MFI_Lag_5", "PORT3.SA_Amihud_Lag_1", "PORT3.SA_Amihud_Lag_2", "PORT3.SA_Amihud_Lag_3", "PORT3.SA_Amihud_Lag_4", "PORT3.SA_Amihud_Lag_5", "PORT3.SA_Roll_Spread_Lag_1", "PORT3.SA_Roll_Spread_Lag_2", "PORT3.SA_Roll_Spread_Lag_3", "PORT3.SA_Roll_Spread_Lag_4", "PORT3.SA_Roll_Spread_Lag_5", "PORT3.<PERSON>_Hu<PERSON>_Lag_1", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_2", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_3", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_4", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_5", "PORT3.SA_Vol_per_Volume_Lag_1", "PORT3.SA_Vol_per_Volume_Lag_2", "PORT3.SA_Vol_per_Volume_Lag_3", "PORT3.SA_Vol_per_Volume_Lag_4", "PORT3.SA_Vol_per_Volume_Lag_5", "PORT3.SA_CMF_Lag_1", "PORT3.SA_CMF_Lag_2", "PORT3.SA_CMF_Lag_3", "PORT3.SA_CMF_Lag_4", "PORT3.SA_CMF_Lag_5", "PORT3.SA_AD_Line_Lag_1", "PORT3.SA_AD_Line_Lag_2", "PORT3.SA_AD_Line_Lag_3", "PORT3.SA_AD_Line_Lag_4", "PORT3.SA_AD_Line_Lag_5", "ESPA3.SA_Close", "ESPA3.SA_High", "ESPA3.SA_Low", "ESPA3.SA_Open", "ESPA3.SA_Volume", "ESPA3.SA_Media_OHLC", "ESPA3.SA_MM10", "ESPA3.SA_MM25", "ESPA3.SA_MM100", "ESPA3.SA_Media_OHLC_PctChange", "ESPA3.SA_Volatilidade", "ESPA3.SA_Spread", "ESPA3.SA_Parkinson_Volatility", "ESPA3.SA_MFI", "ESPA3.SA_MFI_Historical", "ESPA3.SA_EMV", "ESPA3.SA_EMV_MA", "ESPA3.SA_Amihud", "ESPA3.SA_Amihud_Historical", "ESPA3.SA_Roll_Spread", "ESPA3.SA_Roll_Spread_Historical", "ESPA3.SA_Hurst", "ESPA3.<PERSON>_<PERSON><PERSON>_Historical", "ESPA3.SA_Vol_per_Volume", "ESPA3.SA_Vol_per_Volume_Historical", "ESPA3.SA_CMF", "ESPA3.SA_CMF_Historical", "ESPA3.SA_AD_Line", "ESPA3.SA_AD_Line_Historical", "ESPA3.SA_VO", "ESPA3.SA_High_Max_50", "ESPA3.SA_Low_Min_50", "ESPA3.SA_Segunda", "ESPA3.SA_Terca", "ESPA3.SA_Quarta", "ESPA3.SA_Quinta", "ESPA3.SA_Sexta", "ESPA3.SA_Mes_1", "ESPA3.SA_Mes_2", "ESPA3.SA_Mes_3", "ESPA3.SA_Mes_4", "ESPA3.SA_Mes_5", "ESPA3.SA_Mes_6", "ESPA3.SA_Mes_7", "ESPA3.SA_Mes_8", "ESPA3.SA_Mes_9", "ESPA3.SA_Mes_10", "ESPA3.SA_Mes_11", "ESPA3.SA_Mes_12", "ESPA3.SA_Quarter_1", "ESPA3.SA_Quarter_2", "ESPA3.SA_Quarter_3", "ESPA3.SA_Quarter_4", "ESPA3.SA_Last_Day_Quarter", "ESPA3.SA_Pre_Feriado_Brasil", "ESPA3.SA_Media_OHLC_Anterior", "ESPA3.SA_Sinal_Compra", "ESPA3.SA_Sinal_Venda", "ESPA3.SA_Media_OHLC_Futura", "ESPA3.SA_Media_OHLC_PctChange_Lag_1", "ESPA3.SA_Media_OHLC_PctChange_Lag_2", "ESPA3.SA_Media_OHLC_PctChange_Lag_3", "ESPA3.SA_Media_OHLC_PctChange_Lag_4", "ESPA3.SA_Media_OHLC_PctChange_Lag_5", "ESPA3.SA_Media_OHLC_PctChange_Lag_6", "ESPA3.SA_Media_OHLC_PctChange_Lag_7", "ESPA3.SA_Media_OHLC_PctChange_Lag_8", "ESPA3.SA_Media_OHLC_PctChange_Lag_9", "ESPA3.SA_Media_OHLC_PctChange_Lag_10", "ESPA3.SA_MM10_PctChange", "ESPA3.SA_Diff_OHLC_MM10", "ESPA3.SA_MM25_PctChange", "ESPA3.SA_Diff_OHLC_MM25", "ESPA3.SA_MM100_PctChange", "ESPA3.SA_Diff_OHLC_MM100", "ESPA3.SA_MM10_Menos_MM25", "ESPA3.SA_MM25_Menos_MM100", "ESPA3.SA_MM10_Menos_MM100", "ESPA3.SA_Volume_Lag_1", "ESPA3.SA_Volume_Lag_2", "ESPA3.SA_Volume_Lag_3", "ESPA3.SA_Volume_Lag_4", "ESPA3.SA_Volume_Lag_5", "ESPA3.SA_Spread_Lag_1", "ESPA3.SA_Spread_Lag_2", "ESPA3.SA_Spread_Lag_3", "ESPA3.SA_Spread_Lag_4", "ESPA3.SA_Spread_Lag_5", "ESPA3.SA_Volatilidade_Lag_1", "ESPA3.SA_Volatilidade_Lag_2", "ESPA3.SA_Volatilidade_Lag_3", "ESPA3.SA_Volatilidade_Lag_4", "ESPA3.SA_Volatilidade_Lag_5", "ESPA3.SA_Parkinson_Volatility_Lag_1", "ESPA3.SA_Parkinson_Volatility_Lag_2", "ESPA3.SA_Parkinson_Volatility_Lag_3", "ESPA3.SA_Parkinson_Volatility_Lag_4", "ESPA3.SA_Parkinson_Volatility_Lag_5", "ESPA3.SA_EMV_Lag_1", "ESPA3.SA_EMV_Lag_2", "ESPA3.SA_EMV_Lag_3", "ESPA3.SA_EMV_Lag_4", "ESPA3.SA_EMV_Lag_5", "ESPA3.SA_EMV_MA_Lag_1", "ESPA3.SA_EMV_MA_Lag_2", "ESPA3.SA_EMV_MA_Lag_3", "ESPA3.SA_EMV_MA_Lag_4", "ESPA3.SA_EMV_MA_Lag_5", "ESPA3.SA_VO_Lag_1", "ESPA3.SA_VO_Lag_2", "ESPA3.SA_VO_Lag_3", "ESPA3.SA_VO_Lag_4", "ESPA3.SA_VO_Lag_5", "ESPA3.SA_High_Max_50_Lag_1", "ESPA3.SA_High_Max_50_Lag_2", "ESPA3.SA_High_Max_50_Lag_3", "ESPA3.SA_High_Max_50_Lag_4", "ESPA3.SA_High_Max_50_Lag_5", "ESPA3.SA_Low_Min_50_Lag_1", "ESPA3.SA_Low_Min_50_Lag_2", "ESPA3.SA_Low_Min_50_Lag_3", "ESPA3.SA_Low_Min_50_Lag_4", "ESPA3.SA_Low_Min_50_Lag_5", "ESPA3.SA_MFI_Lag_1", "ESPA3.SA_MFI_Lag_2", "ESPA3.SA_MFI_Lag_3", "ESPA3.SA_MFI_Lag_4", "ESPA3.SA_MFI_Lag_5", "ESPA3.SA_Amihud_Lag_1", "ESPA3.SA_Amihud_Lag_2", "ESPA3.SA_Amihud_Lag_3", "ESPA3.SA_Amihud_Lag_4", "ESPA3.SA_Amihud_Lag_5", "ESPA3.SA_Roll_Spread_Lag_1", "ESPA3.SA_Roll_Spread_Lag_2", "ESPA3.SA_Roll_Spread_Lag_3", "ESPA3.SA_Roll_Spread_Lag_4", "ESPA3.SA_Roll_Spread_Lag_5", "ESPA3.SA_Hurst_Lag_1", "ESPA3.<PERSON>_<PERSON><PERSON>_Lag_2", "ESPA3.<PERSON>_<PERSON><PERSON>_Lag_3", "ESPA3.<PERSON>_<PERSON><PERSON>_Lag_4", "ESPA3.<PERSON>_<PERSON><PERSON>_Lag_5", "ESPA3.SA_Vol_per_Volume_Lag_1", "ESPA3.SA_Vol_per_Volume_Lag_2", "ESPA3.SA_Vol_per_Volume_Lag_3", "ESPA3.SA_Vol_per_Volume_Lag_4", "ESPA3.SA_Vol_per_Volume_Lag_5", "ESPA3.SA_CMF_Lag_1", "ESPA3.SA_CMF_Lag_2", "ESPA3.SA_CMF_Lag_3", "ESPA3.SA_CMF_Lag_4", "ESPA3.SA_CMF_Lag_5", "ESPA3.SA_AD_Line_Lag_1", "ESPA3.SA_AD_Line_Lag_2", "ESPA3.SA_AD_Line_Lag_3", "ESPA3.SA_AD_Line_Lag_4", "ESPA3.SA_AD_Line_Lag_5", "PETR3.SA_Open", "PETR3.SA_High", "PETR3.SA_Low", "PETR3.SA_Close", "PETR3.SA_Volume", "PETR3.SA_Media_OHLC", "PETR3.SA_MM10", "PETR3.SA_MM25", "PETR3.SA_MM100", "PETR3.SA_Media_OHLC_PctChange", "PETR3.SA_Volatilidade", "PETR3.SA_Spread", "PETR3.SA_Parkinson_Volatility", "PETR3.SA_MFI", "PETR3.SA_MFI_Historical", "PETR3.SA_EMV", "PETR3.SA_EMV_MA", "PETR3.SA_Amihud", "PETR3.SA_Amihud_Historical", "PETR3.SA_Roll_Spread", "PETR3.SA_Roll_Spread_Historical", "PETR3.SA_Hurst", "PETR3.SA_<PERSON><PERSON>_Historical", "PETR3.SA_Vol_per_Volume", "PETR3.SA_Vol_per_Volume_Historical", "PETR3.SA_CMF", "PETR3.SA_CMF_Historical", "PETR3.SA_AD_Line", "PETR3.SA_AD_Line_Historical", "PETR3.SA_VO", "PETR3.SA_High_Max_50", "PETR3.SA_Low_Min_50", "PETR3.SA_Segunda", "PETR3.SA_Terca", "PETR3.SA_Quarta", "PETR3.SA_Quinta", "PETR3.SA_Sexta", "PETR3.SA_Mes_1", "PETR3.SA_Mes_2", "PETR3.SA_Mes_3", "PETR3.SA_Mes_4", "PETR3.SA_Mes_5", "PETR3.SA_Mes_6", "PETR3.SA_Mes_7", "PETR3.SA_Mes_8", "PETR3.SA_Mes_9", "PETR3.SA_Mes_10", "PETR3.SA_Mes_11", "PETR3.SA_Mes_12", "PETR3.SA_Quarter_1", "PETR3.SA_Quarter_2", "PETR3.SA_Quarter_3", "PETR3.SA_Quarter_4", "PETR3.SA_Last_Day_Quarter", "PETR3.SA_Pre_Feriado_Brasil", "PETR3.SA_Media_OHLC_Anterior", "PETR3.SA_Sinal_Compra", "PETR3.SA_Sinal_Venda", "PETR3.SA_Media_OHLC_Futura", "PETR3.SA_Media_OHLC_PctChange_Lag_1", "PETR3.SA_Media_OHLC_PctChange_Lag_2", "PETR3.SA_Media_OHLC_PctChange_Lag_3", "PETR3.SA_Media_OHLC_PctChange_Lag_4", "PETR3.SA_Media_OHLC_PctChange_Lag_5", "PETR3.SA_Media_OHLC_PctChange_Lag_6", "PETR3.SA_Media_OHLC_PctChange_Lag_7", "PETR3.SA_Media_OHLC_PctChange_Lag_8", "PETR3.SA_Media_OHLC_PctChange_Lag_9", "PETR3.SA_Media_OHLC_PctChange_Lag_10", "PETR3.SA_MM10_PctChange", "PETR3.SA_Diff_OHLC_MM10", "PETR3.SA_MM25_PctChange", "PETR3.SA_Diff_OHLC_MM25", "PETR3.SA_MM100_PctChange", "PETR3.SA_Diff_OHLC_MM100", "PETR3.SA_MM10_Menos_MM25", "PETR3.SA_MM25_Menos_MM100", "PETR3.SA_MM10_Menos_MM100", "PETR3.SA_Volume_Lag_1", "PETR3.SA_Volume_Lag_2", "PETR3.SA_Volume_Lag_3", "PETR3.SA_Volume_Lag_4", "PETR3.SA_Volume_Lag_5", "PETR3.SA_Spread_Lag_1", "PETR3.SA_Spread_Lag_2", "PETR3.SA_Spread_Lag_3", "PETR3.SA_Spread_Lag_4", "PETR3.SA_Spread_Lag_5", "PETR3.SA_Volatilidade_Lag_1", "PETR3.SA_Volatilidade_Lag_2", "PETR3.SA_Volatilidade_Lag_3", "PETR3.SA_Volatilidade_Lag_4", "PETR3.SA_Volatilidade_Lag_5", "PETR3.SA_Parkinson_Volatility_Lag_1", "PETR3.<PERSON>_Parkinson_Volatility_Lag_2", "PETR3.SA_Parkinson_Volatility_Lag_3", "PETR3.SA_Parkinson_Volatility_Lag_4", "PETR3.<PERSON>_Parkinson_Volatility_Lag_5", "PETR3.SA_EMV_Lag_1", "PETR3.SA_EMV_Lag_2", "PETR3.SA_EMV_Lag_3", "PETR3.SA_EMV_Lag_4", "PETR3.SA_EMV_Lag_5", "PETR3.SA_EMV_MA_Lag_1", "PETR3.SA_EMV_MA_Lag_2", "PETR3.SA_EMV_MA_Lag_3", "PETR3.SA_EMV_MA_Lag_4", "PETR3.SA_EMV_MA_Lag_5", "PETR3.SA_VO_Lag_1", "PETR3.SA_VO_Lag_2", "PETR3.SA_VO_Lag_3", "PETR3.SA_VO_Lag_4", "PETR3.SA_VO_Lag_5", "PETR3.SA_High_Max_50_Lag_1", "PETR3.SA_High_Max_50_Lag_2", "PETR3.SA_High_Max_50_Lag_3", "PETR3.SA_High_Max_50_Lag_4", "PETR3.SA_High_Max_50_Lag_5", "PETR3.SA_Low_Min_50_Lag_1", "PETR3.SA_Low_Min_50_Lag_2", "PETR3.SA_Low_Min_50_Lag_3", "PETR3.SA_Low_Min_50_Lag_4", "PETR3.SA_Low_Min_50_Lag_5", "PETR3.SA_MFI_Lag_1", "PETR3.SA_MFI_Lag_2", "PETR3.SA_MFI_Lag_3", "PETR3.SA_MFI_Lag_4", "PETR3.SA_MFI_Lag_5", "PETR3.SA_Amihud_Lag_1", "PETR3.SA_Amihud_Lag_2", "PETR3.SA_Amihud_Lag_3", "PETR3.SA_Amihud_Lag_4", "PETR3.SA_Amihud_Lag_5", "PETR3.SA_Roll_Spread_Lag_1", "PETR3.SA_Roll_Spread_Lag_2", "PETR3.SA_Roll_Spread_Lag_3", "PETR3.SA_Roll_Spread_Lag_4", "PETR3.SA_Roll_Spread_Lag_5", "PETR3.<PERSON>_Hurst_Lag_1", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_2", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_3", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_4", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_5", "PETR3.SA_Vol_per_Volume_Lag_1", "PETR3.SA_Vol_per_Volume_Lag_2", "PETR3.SA_Vol_per_Volume_Lag_3", "PETR3.SA_Vol_per_Volume_Lag_4", "PETR3.SA_Vol_per_Volume_Lag_5", "PETR3.SA_CMF_Lag_1", "PETR3.SA_CMF_Lag_2", "PETR3.SA_CMF_Lag_3", "PETR3.SA_CMF_Lag_4", "PETR3.SA_CMF_Lag_5", "PETR3.SA_AD_Line_Lag_1", "PETR3.SA_AD_Line_Lag_2", "PETR3.SA_AD_Line_Lag_3", "PETR3.SA_AD_Line_Lag_4", "PETR3.SA_AD_Line_Lag_5", "VLID3.SA_Open", "VLID3.SA_High", "VLID3.SA_Low", "VLID3.SA_Close", "VLID3.SA_Volume", "VLID3.SA_Media_OHLC", "VLID3.SA_MM10", "VLID3.SA_MM25", "VLID3.SA_MM100", "VLID3.SA_Media_OHLC_PctChange", "VLID3.SA_Volatilidade", "VLID3.SA_Spread", "VLID3.SA_Parkinson_Volatility", "VLID3.SA_MFI", "VLID3.SA_MFI_Historical", "VLID3.SA_EMV", "VLID3.SA_EMV_MA", "VLID3.SA_Amihud", "VLID3.SA_Amihud_Historical", "VLID3.SA_Roll_Spread", "VLID3.SA_Roll_Spread_Historical", "VLID3.SA_Hurst", "VLID3.SA_Hurst_Historical", "VLID3.SA_Vol_per_Volume", "VLID3.SA_Vol_per_Volume_Historical", "VLID3.SA_CMF", "VLID3.SA_CMF_Historical", "VLID3.SA_AD_Line", "VLID3.SA_AD_Line_Historical", "VLID3.SA_VO", "VLID3.SA_High_Max_50", "VLID3.SA_Low_Min_50", "VLID3.SA_Segunda", "VLID3.SA_Terca", "VLID3.SA_Quarta", "VLID3.SA_Quinta", "VLID3.SA_Sexta", "VLID3.SA_Mes_1", "VLID3.SA_Mes_2", "VLID3.SA_Mes_3", "VLID3.SA_Mes_4", "VLID3.SA_Mes_5", "VLID3.SA_Mes_6", "VLID3.SA_Mes_7", "VLID3.SA_Mes_8", "VLID3.SA_Mes_9", "VLID3.SA_Mes_10", "VLID3.SA_Mes_11", "VLID3.SA_Mes_12", "VLID3.SA_Quarter_1", "VLID3.SA_Quarter_2", "VLID3.SA_Quarter_3", "VLID3.SA_Quarter_4", "VLID3.SA_Last_Day_Quarter", "VLID3.SA_Pre_Feriado_Brasil", "VLID3.SA_Media_OHLC_Anterior", "VLID3.SA_Sinal_Compra", "VLID3.SA_Sinal_Venda", "VLID3.SA_Media_OHLC_Futura", "VLID3.SA_Media_OHLC_PctChange_Lag_1", "VLID3.SA_Media_OHLC_PctChange_Lag_2", "VLID3.SA_Media_OHLC_PctChange_Lag_3", "VLID3.SA_Media_OHLC_PctChange_Lag_4", "VLID3.SA_Media_OHLC_PctChange_Lag_5", "VLID3.SA_Media_OHLC_PctChange_Lag_6", "VLID3.SA_Media_OHLC_PctChange_Lag_7", "VLID3.SA_Media_OHLC_PctChange_Lag_8", "VLID3.SA_Media_OHLC_PctChange_Lag_9", "VLID3.SA_Media_OHLC_PctChange_Lag_10", "VLID3.SA_MM10_PctChange", "VLID3.SA_Diff_OHLC_MM10", "VLID3.SA_MM25_PctChange", "VLID3.SA_Diff_OHLC_MM25", "VLID3.SA_MM100_PctChange", "VLID3.SA_Diff_OHLC_MM100", "VLID3.SA_MM10_Menos_MM25", "VLID3.SA_MM25_Menos_MM100", "VLID3.SA_MM10_Menos_MM100", "VLID3.SA_Volume_Lag_1", "VLID3.SA_Volume_Lag_2", "VLID3.SA_Volume_Lag_3", "VLID3.SA_Volume_Lag_4", "VLID3.SA_Volume_Lag_5", "VLID3.SA_Spread_Lag_1", "VLID3.SA_Spread_Lag_2", "VLID3.SA_Spread_Lag_3", "VLID3.SA_Spread_Lag_4", "VLID3.SA_Spread_Lag_5", "VLID3.SA_Volatilidade_Lag_1", "VLID3.SA_Volatilidade_Lag_2", "VLID3.SA_Volatilidade_Lag_3", "VLID3.SA_Volatilidade_Lag_4", "VLID3.SA_Volatilidade_Lag_5", "VLID3.SA_Parkinson_Volatility_Lag_1", "VLID3.SA_Parkinson_Volatility_Lag_2", "VLID3.SA_Parkinson_Volatility_Lag_3", "VLID3.SA_Parkinson_Volatility_Lag_4", "VLID3.SA_Parkinson_Volatility_Lag_5", "VLID3.SA_EMV_Lag_1", "VLID3.SA_EMV_Lag_2", "VLID3.SA_EMV_Lag_3", "VLID3.SA_EMV_Lag_4", "VLID3.SA_EMV_Lag_5", "VLID3.SA_EMV_MA_Lag_1", "VLID3.SA_EMV_MA_Lag_2", "VLID3.SA_EMV_MA_Lag_3", "VLID3.SA_EMV_MA_Lag_4", "VLID3.SA_EMV_MA_Lag_5", "VLID3.SA_VO_Lag_1", "VLID3.SA_VO_Lag_2", "VLID3.SA_VO_Lag_3", "VLID3.SA_VO_Lag_4", "VLID3.SA_VO_Lag_5", "VLID3.SA_High_Max_50_Lag_1", "VLID3.SA_High_Max_50_Lag_2", "VLID3.SA_High_Max_50_Lag_3", "VLID3.SA_High_Max_50_Lag_4", "VLID3.SA_High_Max_50_Lag_5", "VLID3.SA_Low_Min_50_Lag_1", "VLID3.SA_Low_Min_50_Lag_2", "VLID3.SA_Low_Min_50_Lag_3", "VLID3.SA_Low_Min_50_Lag_4", "VLID3.SA_Low_Min_50_Lag_5", "VLID3.SA_MFI_Lag_1", "VLID3.SA_MFI_Lag_2", "VLID3.SA_MFI_Lag_3", "VLID3.SA_MFI_Lag_4", "VLID3.SA_MFI_Lag_5", "VLID3.SA_Amihud_Lag_1", "VLID3.SA_Amihud_Lag_2", "VLID3.SA_Amihud_Lag_3", "VLID3.SA_Amihud_Lag_4", "VLID3.SA_Amihud_Lag_5", "VLID3.SA_Roll_Spread_Lag_1", "VLID3.SA_Roll_Spread_Lag_2", "VLID3.SA_Roll_Spread_Lag_3", "VLID3.SA_Roll_Spread_Lag_4", "VLID3.SA_Roll_Spread_Lag_5", "VLID3.SA_Hurst_Lag_1", "VLID3.SA_Hurst_Lag_2", "VLID3.SA_Hurst_Lag_3", "VLID3.SA_Hurst_Lag_4", "VLID3.SA_Hurst_Lag_5", "VLID3.SA_Vol_per_Volume_Lag_1", "VLID3.SA_Vol_per_Volume_Lag_2", "VLID3.SA_Vol_per_Volume_Lag_3", "VLID3.SA_Vol_per_Volume_Lag_4", "VLID3.SA_Vol_per_Volume_Lag_5", "VLID3.SA_CMF_Lag_1", "VLID3.SA_CMF_Lag_2", "VLID3.SA_CMF_Lag_3", "VLID3.SA_CMF_Lag_4", "VLID3.SA_CMF_Lag_5", "VLID3.SA_AD_Line_Lag_1", "VLID3.SA_AD_Line_Lag_2", "VLID3.SA_AD_Line_Lag_3", "VLID3.SA_AD_Line_Lag_4", "VLID3.SA_AD_Line_Lag_5", "VALE3.SA_Open", "VALE3.SA_High", "VALE3.SA_Low", "VALE3.SA_Close", "VALE3.SA_Volume", "VALE3.SA_Media_OHLC", "VALE3.SA_MM10", "VALE3.SA_MM25", "VALE3.SA_MM100", "VALE3.SA_Media_OHLC_PctChange", "VALE3.SA_Volatilidade", "VALE3.SA_Spread", "VALE3.SA_Parkinson_Volatility", "VALE3.SA_MFI", "VALE3.SA_MFI_Historical", "VALE3.SA_EMV", "VALE3.SA_EMV_MA", "VALE3.SA_Amihud", "VALE3.SA_Amihud_Historical", "VALE3.SA_Roll_Spread", "VALE3.SA_Roll_Spread_Historical", "VALE3.SA_Hurst", "VALE3.SA_<PERSON><PERSON>_Historical", "VALE3.SA_Vol_per_Volume", "VALE3.SA_Vol_per_Volume_Historical", "VALE3.SA_CMF", "VALE3.SA_CMF_Historical", "VALE3.SA_AD_Line", "VALE3.SA_AD_Line_Historical", "VALE3.SA_VO", "VALE3.SA_High_Max_50", "VALE3.SA_Low_Min_50", "VALE3.SA_Segunda", "VALE3.SA_Terca", "VALE3.SA_Quarta", "VALE3.SA_Quinta", "VALE3.SA_Sexta", "VALE3.SA_Mes_1", "VALE3.SA_Mes_2", "VALE3.SA_Mes_3", "VALE3.SA_Mes_4", "VALE3.SA_Mes_5", "VALE3.SA_Mes_6", "VALE3.SA_Mes_7", "VALE3.SA_Mes_8", "VALE3.SA_Mes_9", "VALE3.SA_Mes_10", "VALE3.SA_Mes_11", "VALE3.SA_Mes_12", "VALE3.SA_Quarter_1", "VALE3.SA_Quarter_2", "VALE3.SA_Quarter_3", "VALE3.SA_Quarter_4", "VALE3.SA_Last_Day_Quarter", "VALE3.SA_Pre_Feriado_Brasil", "VALE3.SA_Media_OHLC_Anterior", "VALE3.SA_Sinal_Compra", "VALE3.SA_Sinal_Venda", "VALE3.SA_Media_OHLC_Futura", "VALE3.SA_Media_OHLC_PctChange_Lag_1", "VALE3.SA_Media_OHLC_PctChange_Lag_2", "VALE3.SA_Media_OHLC_PctChange_Lag_3", "VALE3.SA_Media_OHLC_PctChange_Lag_4", "VALE3.SA_Media_OHLC_PctChange_Lag_5", "VALE3.SA_Media_OHLC_PctChange_Lag_6", "VALE3.SA_Media_OHLC_PctChange_Lag_7", "VALE3.SA_Media_OHLC_PctChange_Lag_8", "VALE3.SA_Media_OHLC_PctChange_Lag_9", "VALE3.SA_Media_OHLC_PctChange_Lag_10", "VALE3.SA_MM10_PctChange", "VALE3.SA_Diff_OHLC_MM10", "VALE3.SA_MM25_PctChange", "VALE3.SA_Diff_OHLC_MM25", "VALE3.SA_MM100_PctChange", "VALE3.SA_Diff_OHLC_MM100", "VALE3.SA_MM10_Menos_MM25", "VALE3.SA_MM25_Menos_MM100", "VALE3.SA_MM10_Menos_MM100", "VALE3.SA_Volume_Lag_1", "VALE3.SA_Volume_Lag_2", "VALE3.SA_Volume_Lag_3", "VALE3.SA_Volume_Lag_4", "VALE3.SA_Volume_Lag_5", "VALE3.SA_Spread_Lag_1", "VALE3.SA_Spread_Lag_2", "VALE3.SA_Spread_Lag_3", "VALE3.SA_Spread_Lag_4", "VALE3.SA_Spread_Lag_5", "VALE3.SA_Volatilidade_Lag_1", "VALE3.SA_Volatilidade_Lag_2", "VALE3.SA_Volatilidade_Lag_3", "VALE3.SA_Volatilidade_Lag_4", "VALE3.SA_Volatilidade_Lag_5", "VALE3.SA_Parkinson_Volatility_Lag_1", "VALE3.SA_Parkinson_Volatility_Lag_2", "VALE3.SA_Parkinson_Volatility_Lag_3", "VALE3.SA_Parkinson_Volatility_Lag_4", "VALE3.SA_Parkinson_Volatility_Lag_5", "VALE3.SA_EMV_Lag_1", "VALE3.SA_EMV_Lag_2", "VALE3.SA_EMV_Lag_3", "VALE3.SA_EMV_Lag_4", "VALE3.SA_EMV_Lag_5", "VALE3.SA_EMV_MA_Lag_1", "VALE3.SA_EMV_MA_Lag_2", "VALE3.SA_EMV_MA_Lag_3", "VALE3.SA_EMV_MA_Lag_4", "VALE3.SA_EMV_MA_Lag_5", "VALE3.SA_VO_Lag_1", "VALE3.SA_VO_Lag_2", "VALE3.SA_VO_Lag_3", "VALE3.SA_VO_Lag_4", "VALE3.SA_VO_Lag_5", "VALE3.SA_High_Max_50_Lag_1", "VALE3.SA_High_Max_50_Lag_2", "VALE3.SA_High_Max_50_Lag_3", "VALE3.SA_High_Max_50_Lag_4", "VALE3.SA_High_Max_50_Lag_5", "VALE3.SA_Low_Min_50_Lag_1", "VALE3.SA_Low_Min_50_Lag_2", "VALE3.SA_Low_Min_50_Lag_3", "VALE3.SA_Low_Min_50_Lag_4", "VALE3.SA_Low_Min_50_Lag_5", "VALE3.SA_MFI_Lag_1", "VALE3.SA_MFI_Lag_2", "VALE3.SA_MFI_Lag_3", "VALE3.SA_MFI_Lag_4", "VALE3.SA_MFI_Lag_5", "VALE3.SA_Amihud_Lag_1", "VALE3.SA_Amihud_Lag_2", "VALE3.SA_Amihud_Lag_3", "VALE3.SA_Amihud_Lag_4", "VALE3.SA_Amihud_Lag_5", "VALE3.SA_Roll_Spread_Lag_1", "VALE3.SA_Roll_Spread_Lag_2", "VALE3.SA_Roll_Spread_Lag_3", "VALE3.SA_Roll_Spread_Lag_4", "VALE3.SA_Roll_Spread_Lag_5", "VALE3.SA_Hurst_Lag_1", "VALE3.SA_Hurst_Lag_2", "VALE3.SA_Hurst_Lag_3", "VALE3.SA_<PERSON>rst_Lag_4", "VALE3.<PERSON>_<PERSON><PERSON>_Lag_5", "VALE3.SA_Vol_per_Volume_Lag_1", "VALE3.SA_Vol_per_Volume_Lag_2", "VALE3.SA_Vol_per_Volume_Lag_3", "VALE3.SA_Vol_per_Volume_Lag_4", "VALE3.SA_Vol_per_Volume_Lag_5", "VALE3.SA_CMF_Lag_1", "VALE3.SA_CMF_Lag_2", "VALE3.SA_CMF_Lag_3", "VALE3.SA_CMF_Lag_4", "VALE3.SA_CMF_Lag_5", "VALE3.SA_AD_Line_Lag_1", "VALE3.SA_AD_Line_Lag_2", "VALE3.SA_AD_Line_Lag_3", "VALE3.SA_AD_Line_Lag_4", "VALE3.SA_AD_Line_Lag_5", "LPSB3.SA_Open", "LPSB3.SA_High", "LPSB3.SA_Low", "LPSB3.SA_Close", "LPSB3.SA_Volume", "LPSB3.SA_Media_OHLC", "LPSB3.SA_MM10", "LPSB3.SA_MM25", "LPSB3.SA_MM100", "LPSB3.SA_Media_OHLC_PctChange", "LPSB3.SA_Volatilidade", "LPSB3.SA_Spread", "LPSB3.SA_Parkinson_Volatility", "LPSB3.SA_MFI", "LPSB3.SA_MFI_Historical", "LPSB3.SA_EMV", "LPSB3.SA_EMV_MA", "LPSB3.SA_Amihud", "LPSB3.SA_Amihud_Historical", "LPSB3.SA_Roll_Spread", "LPSB3.SA_Roll_Spread_Historical", "LPSB3.SA_Hurst", "LPSB3.SA_Hurst_Historical", "LPSB3.SA_Vol_per_Volume", "LPSB3.SA_Vol_per_Volume_Historical", "LPSB3.SA_CMF", "LPSB3.SA_CMF_Historical", "LPSB3.SA_AD_Line", "LPSB3.SA_AD_Line_Historical", "LPSB3.SA_VO", "LPSB3.SA_High_Max_50", "LPSB3.SA_Low_Min_50", "LPSB3.SA_Segunda", "LPSB3.SA_Terca", "LPSB3.SA_Quarta", "LPSB3.SA_Quinta", "LPSB3.SA_Sexta", "LPSB3.SA_Mes_1", "LPSB3.SA_Mes_2", "LPSB3.SA_Mes_3", "LPSB3.SA_Mes_4", "LPSB3.SA_Mes_5", "LPSB3.SA_Mes_6", "LPSB3.SA_Mes_7", "LPSB3.SA_Mes_8", "LPSB3.SA_Mes_9", "LPSB3.SA_Mes_10", "LPSB3.SA_Mes_11", "LPSB3.SA_Mes_12", "LPSB3.SA_Quarter_1", "LPSB3.SA_Quarter_2", "LPSB3.SA_Quarter_3", "LPSB3.SA_Quarter_4", "LPSB3.SA_Last_Day_Quarter", "LPSB3.SA_Pre_Feriado_Brasil", "LPSB3.SA_Media_OHLC_Anterior", "LPSB3.SA_Sinal_Compra", "LPSB3.SA_Sinal_Venda", "LPSB3.SA_Media_OHLC_Futura", "LPSB3.SA_Media_OHLC_PctChange_Lag_1", "LPSB3.SA_Media_OHLC_PctChange_Lag_2", "LPSB3.SA_Media_OHLC_PctChange_Lag_3", "LPSB3.SA_Media_OHLC_PctChange_Lag_4", "LPSB3.SA_Media_OHLC_PctChange_Lag_5", "LPSB3.SA_Media_OHLC_PctChange_Lag_6", "LPSB3.SA_Media_OHLC_PctChange_Lag_7", "LPSB3.SA_Media_OHLC_PctChange_Lag_8", "LPSB3.SA_Media_OHLC_PctChange_Lag_9", "LPSB3.SA_Media_OHLC_PctChange_Lag_10", "LPSB3.SA_MM10_PctChange", "LPSB3.SA_Diff_OHLC_MM10", "LPSB3.SA_MM25_PctChange", "LPSB3.SA_Diff_OHLC_MM25", "LPSB3.SA_MM100_PctChange", "LPSB3.SA_Diff_OHLC_MM100", "LPSB3.SA_MM10_Menos_MM25", "LPSB3.SA_MM25_Menos_MM100", "LPSB3.SA_MM10_Menos_MM100", "LPSB3.SA_Volume_Lag_1", "LPSB3.SA_Volume_Lag_2", "LPSB3.SA_Volume_Lag_3", "LPSB3.SA_Volume_Lag_4", "LPSB3.SA_Volume_Lag_5", "LPSB3.SA_Spread_Lag_1", "LPSB3.SA_Spread_Lag_2", "LPSB3.SA_Spread_Lag_3", "LPSB3.SA_Spread_Lag_4", "LPSB3.SA_Spread_Lag_5", "LPSB3.SA_Volatilidade_Lag_1", "LPSB3.SA_Volatilidade_Lag_2", "LPSB3.SA_Volatilidade_Lag_3", "LPSB3.SA_Volatilidade_Lag_4", "LPSB3.SA_Volatilidade_Lag_5", "LPSB3.SA_Parkinson_Volatility_Lag_1", "LPSB3.SA_Parkinson_Volatility_Lag_2", "LPSB3.SA_Parkinson_Volatility_Lag_3", "LPSB3.SA_Parkinson_Volatility_Lag_4", "LPSB3.SA_Parkinson_Volatility_Lag_5", "LPSB3.SA_EMV_Lag_1", "LPSB3.SA_EMV_Lag_2", "LPSB3.SA_EMV_Lag_3", "LPSB3.SA_EMV_Lag_4", "LPSB3.SA_EMV_Lag_5", "LPSB3.SA_EMV_MA_Lag_1", "LPSB3.SA_EMV_MA_Lag_2", "LPSB3.SA_EMV_MA_Lag_3", "LPSB3.SA_EMV_MA_Lag_4", "LPSB3.SA_EMV_MA_Lag_5", "LPSB3.SA_VO_Lag_1", "LPSB3.SA_VO_Lag_2", "LPSB3.SA_VO_Lag_3", "LPSB3.SA_VO_Lag_4", "LPSB3.SA_VO_Lag_5", "LPSB3.SA_High_Max_50_Lag_1", "LPSB3.SA_High_Max_50_Lag_2", "LPSB3.SA_High_Max_50_Lag_3", "LPSB3.SA_High_Max_50_Lag_4", "LPSB3.SA_High_Max_50_Lag_5", "LPSB3.SA_Low_Min_50_Lag_1", "LPSB3.SA_Low_Min_50_Lag_2", "LPSB3.SA_Low_Min_50_Lag_3", "LPSB3.SA_Low_Min_50_Lag_4", "LPSB3.SA_Low_Min_50_Lag_5", "LPSB3.SA_MFI_Lag_1", "LPSB3.SA_MFI_Lag_2", "LPSB3.SA_MFI_Lag_3", "LPSB3.SA_MFI_Lag_4", "LPSB3.SA_MFI_Lag_5", "LPSB3.SA_Amihud_Lag_1", "LPSB3.SA_Amihud_Lag_2", "LPSB3.SA_Amihud_Lag_3", "LPSB3.SA_Amihud_Lag_4", "LPSB3.SA_Amihud_Lag_5", "LPSB3.SA_Roll_Spread_Lag_1", "LPSB3.SA_Roll_Spread_Lag_2", "LPSB3.SA_Roll_Spread_Lag_3", "LPSB3.SA_Roll_Spread_Lag_4", "LPSB3.SA_Roll_Spread_Lag_5", "LPSB3.SA_Hurst_Lag_1", "LPSB3.SA_Hurst_Lag_2", "LPSB3.SA_Hurst_Lag_3", "LPSB3.SA_Hurst_Lag_4", "LPSB3.SA_<PERSON>rst_Lag_5", "LPSB3.SA_Vol_per_Volume_Lag_1", "LPSB3.SA_Vol_per_Volume_Lag_2", "LPSB3.SA_Vol_per_Volume_Lag_3", "LPSB3.SA_Vol_per_Volume_Lag_4", "LPSB3.SA_Vol_per_Volume_Lag_5", "LPSB3.SA_CMF_Lag_1", "LPSB3.SA_CMF_Lag_2", "LPSB3.SA_CMF_Lag_3", "LPSB3.SA_CMF_Lag_4", "LPSB3.SA_CMF_Lag_5", "LPSB3.SA_AD_Line_Lag_1", "LPSB3.SA_AD_Line_Lag_2", "LPSB3.SA_AD_Line_Lag_3", "LPSB3.SA_AD_Line_Lag_4", "LPSB3.SA_AD_Line_Lag_5", "CSMG3.SA_Open", "CSMG3.SA_High", "CSMG3.SA_Low", "CSMG3.SA_Close", "CSMG3.SA_Volume", "CSMG3.SA_Media_OHLC", "CSMG3.SA_MM10", "CSMG3.SA_MM25", "CSMG3.SA_MM100", "CSMG3.SA_Media_OHLC_PctChange", "CSMG3.SA_Volatilidade", "CSMG3.SA_Spread", "CSMG3.SA_Parkinson_Volatility", "CSMG3.SA_MFI", "CSMG3.SA_MFI_Historical", "CSMG3.SA_EMV", "CSMG3.SA_EMV_MA", "CSMG3.SA_Amihud", "CSMG3.SA_Amihud_Historical", "CSMG3.SA_Roll_Spread", "CSMG3.SA_Roll_Spread_Historical", "CSMG3.SA_Hurst", "CSMG3.SA_<PERSON><PERSON>_Historical", "CSMG3.SA_Vol_per_Volume", "CSMG3.SA_Vol_per_Volume_Historical", "CSMG3.SA_CMF", "CSMG3.SA_CMF_Historical", "CSMG3.SA_AD_Line", "CSMG3.SA_AD_Line_Historical", "CSMG3.SA_VO", "CSMG3.SA_High_Max_50", "CSMG3.SA_Low_Min_50", "CSMG3.SA_Segunda", "CSMG3.SA_Terca", "CSMG3.SA_Quarta", "CSMG3.SA_Quinta", "CSMG3.SA_Sexta", "CSMG3.SA_Mes_1", "CSMG3.SA_Mes_2", "CSMG3.SA_Mes_3", "CSMG3.SA_Mes_4", "CSMG3.SA_Mes_5", "CSMG3.SA_Mes_6", "CSMG3.SA_Mes_7", "CSMG3.SA_Mes_8", "CSMG3.SA_Mes_9", "CSMG3.SA_Mes_10", "CSMG3.SA_Mes_11", "CSMG3.SA_Mes_12", "CSMG3.SA_Quarter_1", "CSMG3.SA_Quarter_2", "CSMG3.SA_Quarter_3", "CSMG3.SA_Quarter_4", "CSMG3.SA_Last_Day_Quarter", "CSMG3.SA_Pre_Feriado_Brasil", "CSMG3.SA_Media_OHLC_Anterior", "CSMG3.SA_Sinal_Compra", "CSMG3.SA_Sinal_Venda", "CSMG3.SA_Media_OHLC_Futura", "CSMG3.SA_Media_OHLC_PctChange_Lag_1", "CSMG3.SA_Media_OHLC_PctChange_Lag_2", "CSMG3.SA_Media_OHLC_PctChange_Lag_3", "CSMG3.SA_Media_OHLC_PctChange_Lag_4", "CSMG3.SA_Media_OHLC_PctChange_Lag_5", "CSMG3.SA_Media_OHLC_PctChange_Lag_6", "CSMG3.SA_Media_OHLC_PctChange_Lag_7", "CSMG3.SA_Media_OHLC_PctChange_Lag_8", "CSMG3.SA_Media_OHLC_PctChange_Lag_9", "CSMG3.SA_Media_OHLC_PctChange_Lag_10", "CSMG3.SA_MM10_PctChange", "CSMG3.SA_Diff_OHLC_MM10", "CSMG3.SA_MM25_PctChange", "CSMG3.SA_Diff_OHLC_MM25", "CSMG3.SA_MM100_PctChange", "CSMG3.SA_Diff_OHLC_MM100", "CSMG3.SA_MM10_Menos_MM25", "CSMG3.SA_MM25_Menos_MM100", "CSMG3.SA_MM10_Menos_MM100", "CSMG3.SA_Volume_Lag_1", "CSMG3.SA_Volume_Lag_2", "CSMG3.SA_Volume_Lag_3", "CSMG3.SA_Volume_Lag_4", "CSMG3.SA_Volume_Lag_5", "CSMG3.SA_Spread_Lag_1", "CSMG3.SA_Spread_Lag_2", "CSMG3.SA_Spread_Lag_3", "CSMG3.SA_Spread_Lag_4", "CSMG3.SA_Spread_Lag_5", "CSMG3.SA_Volatilidade_Lag_1", "CSMG3.SA_Volatilidade_Lag_2", "CSMG3.SA_Volatilidade_Lag_3", "CSMG3.SA_Volatilidade_Lag_4", "CSMG3.SA_Volatilidade_Lag_5", "CSMG3.SA_Parkinson_Volatility_Lag_1", "CSMG3.SA_Parkinson_Volatility_Lag_2", "CSMG3.SA_Parkinson_Volatility_Lag_3", "CSMG3.SA_Parkinson_Volatility_Lag_4", "CSMG3.SA_Parkinson_Volatility_Lag_5", "CSMG3.SA_EMV_Lag_1", "CSMG3.SA_EMV_Lag_2", "CSMG3.SA_EMV_Lag_3", "CSMG3.SA_EMV_Lag_4", "CSMG3.SA_EMV_Lag_5", "CSMG3.SA_EMV_MA_Lag_1", "CSMG3.SA_EMV_MA_Lag_2", "CSMG3.SA_EMV_MA_Lag_3", "CSMG3.SA_EMV_MA_Lag_4", "CSMG3.SA_EMV_MA_Lag_5", "CSMG3.SA_VO_Lag_1", "CSMG3.SA_VO_Lag_2", "CSMG3.SA_VO_Lag_3", "CSMG3.SA_VO_Lag_4", "CSMG3.SA_VO_Lag_5", "CSMG3.SA_High_Max_50_Lag_1", "CSMG3.SA_High_Max_50_Lag_2", "CSMG3.SA_High_Max_50_Lag_3", "CSMG3.SA_High_Max_50_Lag_4", "CSMG3.SA_High_Max_50_Lag_5", "CSMG3.SA_Low_Min_50_Lag_1", "CSMG3.SA_Low_Min_50_Lag_2", "CSMG3.SA_Low_Min_50_Lag_3", "CSMG3.SA_Low_Min_50_Lag_4", "CSMG3.SA_Low_Min_50_Lag_5", "CSMG3.SA_MFI_Lag_1", "CSMG3.SA_MFI_Lag_2", "CSMG3.SA_MFI_Lag_3", "CSMG3.SA_MFI_Lag_4", "CSMG3.SA_MFI_Lag_5", "CSMG3.SA_Amihud_Lag_1", "CSMG3.SA_Amihud_Lag_2", "CSMG3.SA_Amihud_Lag_3", "CSMG3.SA_Amihud_Lag_4", "CSMG3.SA_Amihud_Lag_5", "CSMG3.SA_Roll_Spread_Lag_1", "CSMG3.SA_Roll_Spread_Lag_2", "CSMG3.SA_Roll_Spread_Lag_3", "CSMG3.SA_Roll_Spread_Lag_4", "CSMG3.SA_Roll_Spread_Lag_5", "CSMG3.SA_Hurst_Lag_1", "CSMG3.SA_Hurst_Lag_2", "CSMG3.SA_Hurst_Lag_3", "CSMG3.<PERSON>_<PERSON>rst_Lag_4", "CSMG3.<PERSON>_<PERSON><PERSON>_Lag_5", "CSMG3.SA_Vol_per_Volume_Lag_1", "CSMG3.SA_Vol_per_Volume_Lag_2", "CSMG3.SA_Vol_per_Volume_Lag_3", "CSMG3.SA_Vol_per_Volume_Lag_4", "CSMG3.SA_Vol_per_Volume_Lag_5", "CSMG3.SA_CMF_Lag_1", "CSMG3.SA_CMF_Lag_2", "CSMG3.SA_CMF_Lag_3", "CSMG3.SA_CMF_Lag_4", "CSMG3.SA_CMF_Lag_5", "CSMG3.SA_AD_Line_Lag_1", "CSMG3.SA_AD_Line_Lag_2", "CSMG3.SA_AD_Line_Lag_3", "CSMG3.SA_AD_Line_Lag_4", "CSMG3.SA_AD_Line_Lag_5", "NTCO3.SA_Close", "NTCO3.SA_High", "NTCO3.SA_Low", "NTCO3.SA_Open", "NTCO3.SA_Volume", "NTCO3.SA_Media_OHLC", "NTCO3.SA_MM10", "NTCO3.SA_MM25", "NTCO3.SA_MM100", "NTCO3.SA_Media_OHLC_PctChange", "NTCO3.SA_Volatilidade", "NTCO3.SA_Spread", "NTCO3.SA_Parkinson_Volatility", "NTCO3.SA_MFI", "NTCO3.SA_MFI_Historical", "NTCO3.SA_EMV", "NTCO3.SA_EMV_MA", "NTCO3.SA_Amihud", "NTCO3.SA_Amihud_Historical", "NTCO3.SA_Roll_Spread", "NTCO3.SA_Roll_Spread_Historical", "NTCO3.SA_Hurst", "NTCO3.SA_<PERSON>rst_Historical", "NTCO3.SA_Vol_per_Volume", "NTCO3.SA_Vol_per_Volume_Historical", "NTCO3.SA_CMF", "NTCO3.SA_CMF_Historical", "NTCO3.SA_AD_Line", "NTCO3.SA_AD_Line_Historical", "NTCO3.SA_VO", "NTCO3.SA_High_Max_50", "NTCO3.SA_Low_Min_50", "NTCO3.SA_Segunda", "NTCO3.SA_Terca", "NTCO3.SA_Quarta", "NTCO3.SA_Quinta", "NTCO3.SA_Sexta", "NTCO3.SA_Mes_1", "NTCO3.SA_Mes_2", "NTCO3.SA_Mes_3", "NTCO3.SA_Mes_4", "NTCO3.SA_Mes_5", "NTCO3.SA_Mes_6", "NTCO3.SA_Mes_7", "NTCO3.SA_Mes_8", "NTCO3.SA_Mes_9", "NTCO3.SA_Mes_10", "NTCO3.SA_Mes_11", "NTCO3.SA_Mes_12", "NTCO3.SA_Quarter_1", "NTCO3.SA_Quarter_2", "NTCO3.SA_Quarter_3", "NTCO3.SA_Quarter_4", "NTCO3.SA_Last_Day_Quarter", "NTCO3.SA_Pre_Feriado_Brasil", "NTCO3.SA_Media_OHLC_Anterior", "NTCO3.SA_Sinal_Compra", "NTCO3.SA_Sinal_Venda", "NTCO3.SA_Media_OHLC_Futura", "NTCO3.SA_Media_OHLC_PctChange_Lag_1", "NTCO3.SA_Media_OHLC_PctChange_Lag_2", "NTCO3.SA_Media_OHLC_PctChange_Lag_3", "NTCO3.SA_Media_OHLC_PctChange_Lag_4", "NTCO3.SA_Media_OHLC_PctChange_Lag_5", "NTCO3.SA_Media_OHLC_PctChange_Lag_6", "NTCO3.SA_Media_OHLC_PctChange_Lag_7", "NTCO3.SA_Media_OHLC_PctChange_Lag_8", "NTCO3.SA_Media_OHLC_PctChange_Lag_9", "NTCO3.SA_Media_OHLC_PctChange_Lag_10", "NTCO3.SA_MM10_PctChange", "NTCO3.SA_Diff_OHLC_MM10", "NTCO3.SA_MM25_PctChange", "NTCO3.SA_Diff_OHLC_MM25", "NTCO3.SA_MM100_PctChange", "NTCO3.SA_Diff_OHLC_MM100", "NTCO3.SA_MM10_Menos_MM25", "NTCO3.SA_MM25_Menos_MM100", "NTCO3.SA_MM10_Menos_MM100", "NTCO3.SA_Volume_Lag_1", "NTCO3.SA_Volume_Lag_2", "NTCO3.SA_Volume_Lag_3", "NTCO3.SA_Volume_Lag_4", "NTCO3.SA_Volume_Lag_5", "NTCO3.SA_Spread_Lag_1", "NTCO3.SA_Spread_Lag_2", "NTCO3.SA_Spread_Lag_3", "NTCO3.SA_Spread_Lag_4", "NTCO3.SA_Spread_Lag_5", "NTCO3.SA_Volatilidade_Lag_1", "NTCO3.SA_Volatilidade_Lag_2", "NTCO3.SA_Volatilidade_Lag_3", "NTCO3.SA_Volatilidade_Lag_4", "NTCO3.SA_Volatilidade_Lag_5", "NTCO3.SA_Parkinson_Volatility_Lag_1", "NTCO3.SA_Parkinson_Volatility_Lag_2", "NTCO3.SA_Parkinson_Volatility_Lag_3", "NTCO3.SA_Parkinson_Volatility_Lag_4", "NTCO3.SA_Parkinson_Volatility_Lag_5", "NTCO3.SA_EMV_Lag_1", "NTCO3.SA_EMV_Lag_2", "NTCO3.SA_EMV_Lag_3", "NTCO3.SA_EMV_Lag_4", "NTCO3.SA_EMV_Lag_5", "NTCO3.SA_EMV_MA_Lag_1", "NTCO3.SA_EMV_MA_Lag_2", "NTCO3.SA_EMV_MA_Lag_3", "NTCO3.SA_EMV_MA_Lag_4", "NTCO3.SA_EMV_MA_Lag_5", "NTCO3.SA_VO_Lag_1", "NTCO3.SA_VO_Lag_2", "NTCO3.SA_VO_Lag_3", "NTCO3.SA_VO_Lag_4", "NTCO3.SA_VO_Lag_5", "NTCO3.SA_High_Max_50_Lag_1", "NTCO3.SA_High_Max_50_Lag_2", "NTCO3.SA_High_Max_50_Lag_3", "NTCO3.SA_High_Max_50_Lag_4", "NTCO3.SA_High_Max_50_Lag_5", "NTCO3.SA_Low_Min_50_Lag_1", "NTCO3.SA_Low_Min_50_Lag_2", "NTCO3.SA_Low_Min_50_Lag_3", "NTCO3.SA_Low_Min_50_Lag_4", "NTCO3.SA_Low_Min_50_Lag_5", "NTCO3.SA_MFI_Lag_1", "NTCO3.SA_MFI_Lag_2", "NTCO3.SA_MFI_Lag_3", "NTCO3.SA_MFI_Lag_4", "NTCO3.SA_MFI_Lag_5", "NTCO3.SA_Amihud_Lag_1", "NTCO3.SA_Amihud_Lag_2", "NTCO3.SA_Amihud_Lag_3", "NTCO3.SA_Amihud_Lag_4", "NTCO3.SA_Amihud_Lag_5", "NTCO3.SA_Roll_Spread_Lag_1", "NTCO3.SA_Roll_Spread_Lag_2", "NTCO3.SA_Roll_Spread_Lag_3", "NTCO3.SA_Roll_Spread_Lag_4", "NTCO3.SA_Roll_Spread_Lag_5", "NTCO3.SA_Hurst_Lag_1", "NTCO3.SA_Hurst_Lag_2", "NTCO3.SA_Hurst_Lag_3", "NTCO3.SA_<PERSON>rst_Lag_4", "NTCO3.<PERSON>_<PERSON><PERSON>_Lag_5", "NTCO3.SA_Vol_per_Volume_Lag_1", "NTCO3.SA_Vol_per_Volume_Lag_2", "NTCO3.SA_Vol_per_Volume_Lag_3", "NTCO3.SA_Vol_per_Volume_Lag_4", "NTCO3.SA_Vol_per_Volume_Lag_5", "NTCO3.SA_CMF_Lag_1", "NTCO3.SA_CMF_Lag_2", "NTCO3.SA_CMF_Lag_3", "NTCO3.SA_CMF_Lag_4", "NTCO3.SA_CMF_Lag_5", "NTCO3.SA_AD_Line_Lag_1", "NTCO3.SA_AD_Line_Lag_2", "NTCO3.SA_AD_Line_Lag_3", "NTCO3.SA_AD_Line_Lag_4", "NTCO3.SA_AD_Line_Lag_5", "BBSE3.SA_Close", "BBSE3.SA_High", "BBSE3.SA_Low", "BBSE3.SA_Open", "BBSE3.SA_Volume", "BBSE3.SA_Media_OHLC", "BBSE3.SA_MM10", "BBSE3.SA_MM25", "BBSE3.SA_MM100", "BBSE3.SA_Media_OHLC_PctChange", "BBSE3.SA_Volatilidade", "BBSE3.SA_Spread", "BBSE3.SA_Parkinson_Volatility", "BBSE3.SA_MFI", "BBSE3.SA_MFI_Historical", "BBSE3.SA_EMV", "BBSE3.SA_EMV_MA", "BBSE3.SA_Amihud", "BBSE3.SA_Amihud_Historical", "BBSE3.SA_Roll_Spread", "BBSE3.SA_Roll_Spread_Historical", "BBSE3.SA_Hurst", "BBSE3.SA_<PERSON><PERSON>_Historical", "BBSE3.SA_Vol_per_Volume", "BBSE3.SA_Vol_per_Volume_Historical", "BBSE3.SA_CMF", "BBSE3.SA_CMF_Historical", "BBSE3.SA_AD_Line", "BBSE3.SA_AD_Line_Historical", "BBSE3.SA_VO", "BBSE3.SA_High_Max_50", "BBSE3.SA_Low_Min_50", "BBSE3.SA_Segunda", "BBSE3.SA_Terca", "BBSE3.SA_Quarta", "BBSE3.SA_Quinta", "BBSE3.SA_Sexta", "BBSE3.SA_Mes_1", "BBSE3.SA_Mes_2", "BBSE3.SA_Mes_3", "BBSE3.SA_Mes_4", "BBSE3.SA_Mes_5", "BBSE3.SA_Mes_6", "BBSE3.SA_Mes_7", "BBSE3.SA_Mes_8", "BBSE3.SA_Mes_9", "BBSE3.SA_Mes_10", "BBSE3.SA_Mes_11", "BBSE3.SA_Mes_12", "BBSE3.SA_Quarter_1", "BBSE3.SA_Quarter_2", "BBSE3.SA_Quarter_3", "BBSE3.SA_Quarter_4", "BBSE3.SA_Last_Day_Quarter", "BBSE3.SA_Pre_Feriado_Brasil", "BBSE3.SA_Media_OHLC_Anterior", "BBSE3.SA_Sinal_Compra", "BBSE3.SA_Sinal_Venda", "BBSE3.SA_Media_OHLC_Futura", "BBSE3.SA_Media_OHLC_PctChange_Lag_1", "BBSE3.SA_Media_OHLC_PctChange_Lag_2", "BBSE3.SA_Media_OHLC_PctChange_Lag_3", "BBSE3.SA_Media_OHLC_PctChange_Lag_4", "BBSE3.SA_Media_OHLC_PctChange_Lag_5", "BBSE3.SA_Media_OHLC_PctChange_Lag_6", "BBSE3.SA_Media_OHLC_PctChange_Lag_7", "BBSE3.SA_Media_OHLC_PctChange_Lag_8", "BBSE3.SA_Media_OHLC_PctChange_Lag_9", "BBSE3.SA_Media_OHLC_PctChange_Lag_10", "BBSE3.SA_MM10_PctChange", "BBSE3.SA_Diff_OHLC_MM10", "BBSE3.SA_MM25_PctChange", "BBSE3.SA_Diff_OHLC_MM25", "BBSE3.SA_MM100_PctChange", "BBSE3.SA_Diff_OHLC_MM100", "BBSE3.SA_MM10_Menos_MM25", "BBSE3.SA_MM25_Menos_MM100", "BBSE3.SA_MM10_Menos_MM100", "BBSE3.SA_Volume_Lag_1", "BBSE3.SA_Volume_Lag_2", "BBSE3.SA_Volume_Lag_3", "BBSE3.SA_Volume_Lag_4", "BBSE3.SA_Volume_Lag_5", "BBSE3.SA_Spread_Lag_1", "BBSE3.SA_Spread_Lag_2", "BBSE3.SA_Spread_Lag_3", "BBSE3.SA_Spread_Lag_4", "BBSE3.SA_Spread_Lag_5", "BBSE3.SA_Volatilidade_Lag_1", "BBSE3.SA_Volatilidade_Lag_2", "BBSE3.SA_Volatilidade_Lag_3", "BBSE3.SA_Volatilidade_Lag_4", "BBSE3.SA_Volatilidade_Lag_5", "BBSE3.SA_Parkinson_Volatility_Lag_1", "BBSE3.SA_Parkinson_Volatility_Lag_2", "BBSE3.SA_Parkinson_Volatility_Lag_3", "BBSE3.SA_Parkinson_Volatility_Lag_4", "BBSE3.SA_Parkinson_Volatility_Lag_5", "BBSE3.SA_EMV_Lag_1", "BBSE3.SA_EMV_Lag_2", "BBSE3.SA_EMV_Lag_3", "BBSE3.SA_EMV_Lag_4", "BBSE3.SA_EMV_Lag_5", "BBSE3.SA_EMV_MA_Lag_1", "BBSE3.SA_EMV_MA_Lag_2", "BBSE3.SA_EMV_MA_Lag_3", "BBSE3.SA_EMV_MA_Lag_4", "BBSE3.SA_EMV_MA_Lag_5", "BBSE3.SA_VO_Lag_1", "BBSE3.SA_VO_Lag_2", "BBSE3.SA_VO_Lag_3", "BBSE3.SA_VO_Lag_4", "BBSE3.SA_VO_Lag_5", "BBSE3.SA_High_Max_50_Lag_1", "BBSE3.SA_High_Max_50_Lag_2", "BBSE3.SA_High_Max_50_Lag_3", "BBSE3.SA_High_Max_50_Lag_4", "BBSE3.SA_High_Max_50_Lag_5", "BBSE3.SA_Low_Min_50_Lag_1", "BBSE3.SA_Low_Min_50_Lag_2", "BBSE3.SA_Low_Min_50_Lag_3", "BBSE3.SA_Low_Min_50_Lag_4", "BBSE3.SA_Low_Min_50_Lag_5", "BBSE3.SA_MFI_Lag_1", "BBSE3.SA_MFI_Lag_2", "BBSE3.SA_MFI_Lag_3", "BBSE3.SA_MFI_Lag_4", "BBSE3.SA_MFI_Lag_5", "BBSE3.SA_Amihud_Lag_1", "BBSE3.SA_Amihud_Lag_2", "BBSE3.SA_Amihud_Lag_3", "BBSE3.SA_Amihud_Lag_4", "BBSE3.SA_Amihud_Lag_5", "BBSE3.SA_Roll_Spread_Lag_1", "BBSE3.SA_Roll_Spread_Lag_2", "BBSE3.SA_Roll_Spread_Lag_3", "BBSE3.SA_Roll_Spread_Lag_4", "BBSE3.SA_Roll_Spread_Lag_5", "BBSE3.SA_Hurst_Lag_1", "BBSE3.SA_Hu<PERSON>_Lag_2", "BBSE3.SA_Hurst_Lag_3", "BBSE3.<PERSON>_<PERSON><PERSON>_Lag_4", "BBSE3.<PERSON>_<PERSON><PERSON>_Lag_5", "BBSE3.SA_Vol_per_Volume_Lag_1", "BBSE3.SA_Vol_per_Volume_Lag_2", "BBSE3.SA_Vol_per_Volume_Lag_3", "BBSE3.SA_Vol_per_Volume_Lag_4", "BBSE3.SA_Vol_per_Volume_Lag_5", "BBSE3.SA_CMF_Lag_1", "BBSE3.SA_CMF_Lag_2", "BBSE3.SA_CMF_Lag_3", "BBSE3.SA_CMF_Lag_4", "BBSE3.SA_CMF_Lag_5", "BBSE3.SA_AD_Line_Lag_1", "BBSE3.SA_AD_Line_Lag_2", "BBSE3.SA_AD_Line_Lag_3", "BBSE3.SA_AD_Line_Lag_4", "BBSE3.SA_AD_Line_Lag_5", "CMIG3.SA_Open", "CMIG3.SA_High", "CMIG3.SA_Low", "CMIG3.SA_Close", "CMIG3.SA_Volume", "CMIG3.SA_Media_OHLC", "CMIG3.SA_MM10", "CMIG3.SA_MM25", "CMIG3.SA_MM100", "CMIG3.SA_Media_OHLC_PctChange", "CMIG3.SA_Volatilidade", "CMIG3.SA_Spread", "CMIG3.SA_Parkinson_Volatility", "CMIG3.SA_MFI", "CMIG3.SA_MFI_Historical", "CMIG3.SA_EMV", "CMIG3.SA_EMV_MA", "CMIG3.SA_Amihud", "CMIG3.SA_Amihud_Historical", "CMIG3.SA_Roll_Spread", "CMIG3.SA_Roll_Spread_Historical", "CMIG3.SA_Hurst", "CMIG3.<PERSON>_<PERSON><PERSON>_Historical", "CMIG3.SA_Vol_per_Volume", "CMIG3.SA_Vol_per_Volume_Historical", "CMIG3.SA_CMF", "CMIG3.SA_CMF_Historical", "CMIG3.SA_AD_Line", "CMIG3.SA_AD_Line_Historical", "CMIG3.SA_VO", "CMIG3.SA_High_Max_50", "CMIG3.SA_Low_Min_50", "CMIG3.SA_Segunda", "CMIG3.SA_Terca", "CMIG3.SA_Quarta", "CMIG3.SA_Quinta", "CMIG3.SA_Sexta", "CMIG3.SA_Mes_1", "CMIG3.SA_Mes_2", "CMIG3.SA_Mes_3", "CMIG3.SA_Mes_4", "CMIG3.SA_Mes_5", "CMIG3.SA_Mes_6", "CMIG3.SA_Mes_7", "CMIG3.SA_Mes_8", "CMIG3.SA_Mes_9", "CMIG3.SA_Mes_10", "CMIG3.SA_Mes_11", "CMIG3.SA_Mes_12", "CMIG3.SA_Quarter_1", "CMIG3.SA_Quarter_2", "CMIG3.SA_Quarter_3", "CMIG3.SA_Quarter_4", "CMIG3.SA_Last_Day_Quarter", "CMIG3.SA_Pre_Feriado_Brasil", "CMIG3.SA_Media_OHLC_Anterior", "CMIG3.SA_Sinal_Compra", "CMIG3.SA_Sinal_Venda", "CMIG3.SA_Media_OHLC_Futura", "CMIG3.SA_Media_OHLC_PctChange_Lag_1", "CMIG3.SA_Media_OHLC_PctChange_Lag_2", "CMIG3.SA_Media_OHLC_PctChange_Lag_3", "CMIG3.SA_Media_OHLC_PctChange_Lag_4", "CMIG3.SA_Media_OHLC_PctChange_Lag_5", "CMIG3.SA_Media_OHLC_PctChange_Lag_6", "CMIG3.SA_Media_OHLC_PctChange_Lag_7", "CMIG3.SA_Media_OHLC_PctChange_Lag_8", "CMIG3.SA_Media_OHLC_PctChange_Lag_9", "CMIG3.SA_Media_OHLC_PctChange_Lag_10", "CMIG3.SA_MM10_PctChange", "CMIG3.SA_Diff_OHLC_MM10", "CMIG3.SA_MM25_PctChange", "CMIG3.SA_Diff_OHLC_MM25", "CMIG3.SA_MM100_PctChange", "CMIG3.SA_Diff_OHLC_MM100", "CMIG3.SA_MM10_Menos_MM25", "CMIG3.SA_MM25_Menos_MM100", "CMIG3.SA_MM10_Menos_MM100", "CMIG3.SA_Volume_Lag_1", "CMIG3.SA_Volume_Lag_2", "CMIG3.SA_Volume_Lag_3", "CMIG3.SA_Volume_Lag_4", "CMIG3.SA_Volume_Lag_5", "CMIG3.SA_Spread_Lag_1", "CMIG3.SA_Spread_Lag_2", "CMIG3.SA_Spread_Lag_3", "CMIG3.SA_Spread_Lag_4", "CMIG3.SA_Spread_Lag_5", "CMIG3.SA_Volatilidade_Lag_1", "CMIG3.SA_Volatilidade_Lag_2", "CMIG3.SA_Volatilidade_Lag_3", "CMIG3.SA_Volatilidade_Lag_4", "CMIG3.SA_Volatilidade_Lag_5", "CMIG3.SA_Parkinson_Volatility_Lag_1", "CMIG3.<PERSON>_Parkinson_Volatility_Lag_2", "CMIG3.SA_Parkinson_Volatility_Lag_3", "CMIG3.SA_Parkinson_Volatility_Lag_4", "CMIG3.<PERSON>_Parkinson_Volatility_Lag_5", "CMIG3.SA_EMV_Lag_1", "CMIG3.SA_EMV_Lag_2", "CMIG3.SA_EMV_Lag_3", "CMIG3.SA_EMV_Lag_4", "CMIG3.SA_EMV_Lag_5", "CMIG3.SA_EMV_MA_Lag_1", "CMIG3.SA_EMV_MA_Lag_2", "CMIG3.SA_EMV_MA_Lag_3", "CMIG3.SA_EMV_MA_Lag_4", "CMIG3.SA_EMV_MA_Lag_5", "CMIG3.SA_VO_Lag_1", "CMIG3.SA_VO_Lag_2", "CMIG3.SA_VO_Lag_3", "CMIG3.SA_VO_Lag_4", "CMIG3.SA_VO_Lag_5", "CMIG3.SA_High_Max_50_Lag_1", "CMIG3.SA_High_Max_50_Lag_2", "CMIG3.SA_High_Max_50_Lag_3", "CMIG3.SA_High_Max_50_Lag_4", "CMIG3.SA_High_Max_50_Lag_5", "CMIG3.SA_Low_Min_50_Lag_1", "CMIG3.SA_Low_Min_50_Lag_2", "CMIG3.SA_Low_Min_50_Lag_3", "CMIG3.SA_Low_Min_50_Lag_4", "CMIG3.SA_Low_Min_50_Lag_5", "CMIG3.SA_MFI_Lag_1", "CMIG3.SA_MFI_Lag_2", "CMIG3.SA_MFI_Lag_3", "CMIG3.SA_MFI_Lag_4", "CMIG3.SA_MFI_Lag_5", "CMIG3.SA_Amihud_Lag_1", "CMIG3.SA_Amihud_Lag_2", "CMIG3.SA_Amihud_Lag_3", "CMIG3.SA_Amihud_Lag_4", "CMIG3.SA_Amihud_Lag_5", "CMIG3.SA_Roll_Spread_Lag_1", "CMIG3.SA_Roll_Spread_Lag_2", "CMIG3.SA_Roll_Spread_Lag_3", "CMIG3.SA_Roll_Spread_Lag_4", "CMIG3.SA_Roll_Spread_Lag_5", "CMIG3.SA_Hurst_Lag_1", "CMIG3.<PERSON>_Hu<PERSON>_Lag_2", "CMIG3.<PERSON>_Hurst_Lag_3", "CMIG3.<PERSON>_<PERSON><PERSON>_Lag_4", "CMIG3.<PERSON>_<PERSON><PERSON>_Lag_5", "CMIG3.SA_Vol_per_Volume_Lag_1", "CMIG3.SA_Vol_per_Volume_Lag_2", "CMIG3.SA_Vol_per_Volume_Lag_3", "CMIG3.SA_Vol_per_Volume_Lag_4", "CMIG3.SA_Vol_per_Volume_Lag_5", "CMIG3.SA_CMF_Lag_1", "CMIG3.SA_CMF_Lag_2", "CMIG3.SA_CMF_Lag_3", "CMIG3.SA_CMF_Lag_4", "CMIG3.SA_CMF_Lag_5", "CMIG3.SA_AD_Line_Lag_1", "CMIG3.SA_AD_Line_Lag_2", "CMIG3.SA_AD_Line_Lag_3", "CMIG3.SA_AD_Line_Lag_4", "CMIG3.SA_AD_Line_Lag_5", "GGBR4.SA_Open", "GGBR4.SA_High", "GGBR4.SA_Low", "GGBR4.SA_Close", "GGBR4.SA_Volume", "GGBR4.SA_Media_OHLC", "GGBR4.SA_MM10", "GGBR4.SA_MM25", "GGBR4.SA_MM100", "GGBR4.SA_Media_OHLC_PctChange", "GGBR4.SA_Volatilidade", "GGBR4.SA_Spread", "GGBR4.SA_Parkinson_Volatility", "GGBR4.SA_MFI", "GGBR4.SA_MFI_Historical", "GGBR4.SA_EMV", "GGBR4.SA_EMV_MA", "GGBR4.SA_Amihud", "GGBR4.SA_Amihud_Historical", "GGBR4.SA_Roll_Spread", "GGBR4.SA_Roll_Spread_Historical", "GGBR4.SA_Hurst", "GGBR4.SA_Hurst_Historical", "GGBR4.SA_Vol_per_Volume", "GGBR4.SA_Vol_per_Volume_Historical", "GGBR4.SA_CMF", "GGBR4.SA_CMF_Historical", "GGBR4.SA_AD_Line", "GGBR4.SA_AD_Line_Historical", "GGBR4.SA_VO", "GGBR4.SA_High_Max_50", "GGBR4.SA_Low_Min_50", "GGBR4.SA_Segunda", "GGBR4.SA_Terca", "GGBR4.SA_Quarta", "GGBR4.SA_Quinta", "GGBR4.SA_Sexta", "GGBR4.SA_Mes_1", "GGBR4.SA_Mes_2", "GGBR4.SA_Mes_3", "GGBR4.SA_Mes_4", "GGBR4.SA_Mes_5", "GGBR4.SA_Mes_6", "GGBR4.SA_Mes_7", "GGBR4.SA_Mes_8", "GGBR4.SA_Mes_9", "GGBR4.SA_Mes_10", "GGBR4.SA_Mes_11", "GGBR4.SA_Mes_12", "GGBR4.SA_Quarter_1", "GGBR4.SA_Quarter_2", "GGBR4.SA_Quarter_3", "GGBR4.SA_Quarter_4", "GGBR4.SA_Last_Day_Quarter", "GGBR4.SA_Pre_Feriado_Brasil", "GGBR4.SA_Media_OHLC_Anterior", "GGBR4.SA_Sinal_Compra", "GGBR4.SA_Sinal_Venda", "GGBR4.SA_Media_OHLC_Futura", "GGBR4.SA_Media_OHLC_PctChange_Lag_1", "GGBR4.SA_Media_OHLC_PctChange_Lag_2", "GGBR4.SA_Media_OHLC_PctChange_Lag_3", "GGBR4.SA_Media_OHLC_PctChange_Lag_4", "GGBR4.SA_Media_OHLC_PctChange_Lag_5", "GGBR4.SA_Media_OHLC_PctChange_Lag_6", "GGBR4.SA_Media_OHLC_PctChange_Lag_7", "GGBR4.SA_Media_OHLC_PctChange_Lag_8", "GGBR4.SA_Media_OHLC_PctChange_Lag_9", "GGBR4.SA_Media_OHLC_PctChange_Lag_10", "GGBR4.SA_MM10_PctChange", "GGBR4.SA_Diff_OHLC_MM10", "GGBR4.SA_MM25_PctChange", "GGBR4.SA_Diff_OHLC_MM25", "GGBR4.SA_MM100_PctChange", "GGBR4.SA_Diff_OHLC_MM100", "GGBR4.SA_MM10_Menos_MM25", "GGBR4.SA_MM25_Menos_MM100", "GGBR4.SA_MM10_Menos_MM100", "GGBR4.SA_Volume_Lag_1", "GGBR4.SA_Volume_Lag_2", "GGBR4.SA_Volume_Lag_3", "GGBR4.SA_Volume_Lag_4", "GGBR4.SA_Volume_Lag_5", "GGBR4.SA_Spread_Lag_1", "GGBR4.SA_Spread_Lag_2", "GGBR4.SA_Spread_Lag_3", "GGBR4.SA_Spread_Lag_4", "GGBR4.SA_Spread_Lag_5", "GGBR4.SA_Volatilidade_Lag_1", "GGBR4.SA_Volatilidade_Lag_2", "GGBR4.SA_Volatilidade_Lag_3", "GGBR4.SA_Volatilidade_Lag_4", "GGBR4.SA_Volatilidade_Lag_5", "GGBR4.SA_Parkinson_Volatility_Lag_1", "GGBR4.SA_Parkinson_Volatility_Lag_2", "GGBR4.SA_Parkinson_Volatility_Lag_3", "GGBR4.SA_Parkinson_Volatility_Lag_4", "GGBR4.SA_Parkinson_Volatility_Lag_5", "GGBR4.SA_EMV_Lag_1", "GGBR4.SA_EMV_Lag_2", "GGBR4.SA_EMV_Lag_3", "GGBR4.SA_EMV_Lag_4", "GGBR4.SA_EMV_Lag_5", "GGBR4.SA_EMV_MA_Lag_1", "GGBR4.SA_EMV_MA_Lag_2", "GGBR4.SA_EMV_MA_Lag_3", "GGBR4.SA_EMV_MA_Lag_4", "GGBR4.SA_EMV_MA_Lag_5", "GGBR4.SA_VO_Lag_1", "GGBR4.SA_VO_Lag_2", "GGBR4.SA_VO_Lag_3", "GGBR4.SA_VO_Lag_4", "GGBR4.SA_VO_Lag_5", "GGBR4.SA_High_Max_50_Lag_1", "GGBR4.SA_High_Max_50_Lag_2", "GGBR4.SA_High_Max_50_Lag_3", "GGBR4.SA_High_Max_50_Lag_4", "GGBR4.SA_High_Max_50_Lag_5", "GGBR4.SA_Low_Min_50_Lag_1", "GGBR4.SA_Low_Min_50_Lag_2", "GGBR4.SA_Low_Min_50_Lag_3", "GGBR4.SA_Low_Min_50_Lag_4", "GGBR4.SA_Low_Min_50_Lag_5", "GGBR4.SA_MFI_Lag_1", "GGBR4.SA_MFI_Lag_2", "GGBR4.SA_MFI_Lag_3", "GGBR4.SA_MFI_Lag_4", "GGBR4.SA_MFI_Lag_5", "GGBR4.SA_Amihud_Lag_1", "GGBR4.SA_Amihud_Lag_2", "GGBR4.SA_Amihud_Lag_3", "GGBR4.SA_Amihud_Lag_4", "GGBR4.SA_Amihud_Lag_5", "GGBR4.SA_Roll_Spread_Lag_1", "GGBR4.SA_Roll_Spread_Lag_2", "GGBR4.SA_Roll_Spread_Lag_3", "GGBR4.SA_Roll_Spread_Lag_4", "GGBR4.SA_Roll_Spread_Lag_5", "GGBR4.SA_Hurst_Lag_1", "GGBR4.<PERSON>_<PERSON>rst_Lag_2", "GGBR4.SA_<PERSON>rst_Lag_3", "GGBR4.<PERSON>_<PERSON>rst_Lag_4", "GGBR4.<PERSON>_<PERSON><PERSON>_Lag_5", "GGBR4.SA_Vol_per_Volume_Lag_1", "GGBR4.SA_Vol_per_Volume_Lag_2", "GGBR4.SA_Vol_per_Volume_Lag_3", "GGBR4.SA_Vol_per_Volume_Lag_4", "GGBR4.SA_Vol_per_Volume_Lag_5", "GGBR4.SA_CMF_Lag_1", "GGBR4.SA_CMF_Lag_2", "GGBR4.SA_CMF_Lag_3", "GGBR4.SA_CMF_Lag_4", "GGBR4.SA_CMF_Lag_5", "GGBR4.SA_AD_Line_Lag_1", "GGBR4.SA_AD_Line_Lag_2", "GGBR4.SA_AD_Line_Lag_3", "GGBR4.SA_AD_Line_Lag_4", "GGBR4.SA_AD_Line_Lag_5", "RADL3.SA_Open", "RADL3.SA_High", "RADL3.SA_Low", "RADL3.SA_Close", "RADL3.SA_Volume", "RADL3.SA_Media_OHLC", "RADL3.SA_MM10", "RADL3.SA_MM25", "RADL3.SA_MM100", "RADL3.SA_Media_OHLC_PctChange", "RADL3.SA_Volatilidade", "RADL3.SA_Spread", "RADL3.SA_Parkinson_Volatility", "RADL3.SA_MFI", "RADL3.SA_MFI_Historical", "RADL3.SA_EMV", "RADL3.SA_EMV_MA", "RADL3.SA_Amihud", "RADL3.SA_Amihud_Historical", "RADL3.SA_Roll_Spread", "RADL3.SA_Roll_Spread_Historical", "RADL3.SA_Hurst", "RADL3.<PERSON>_<PERSON><PERSON>_Historical", "RADL3.SA_Vol_per_Volume", "RADL3.SA_Vol_per_Volume_Historical", "RADL3.SA_CMF", "RADL3.SA_CMF_Historical", "RADL3.SA_AD_Line", "RADL3.SA_AD_Line_Historical", "RADL3.SA_VO", "RADL3.SA_High_Max_50", "RADL3.SA_Low_Min_50", "RADL3.SA_Segunda", "RADL3.SA_Terca", "RADL3.SA_Quarta", "RADL3.SA_Quinta", "RADL3.SA_Sexta", "RADL3.SA_Mes_1", "RADL3.SA_Mes_2", "RADL3.SA_Mes_3", "RADL3.SA_Mes_4", "RADL3.SA_Mes_5", "RADL3.SA_Mes_6", "RADL3.SA_Mes_7", "RADL3.SA_Mes_8", "RADL3.SA_Mes_9", "RADL3.SA_Mes_10", "RADL3.SA_Mes_11", "RADL3.SA_Mes_12", "RADL3.SA_Quarter_1", "RADL3.SA_Quarter_2", "RADL3.SA_Quarter_3", "RADL3.SA_Quarter_4", "RADL3.SA_Last_Day_Quarter", "RADL3.SA_Pre_Feriado_Brasil", "RADL3.SA_Media_OHLC_Anterior", "RADL3.SA_Sinal_Compra", "RADL3.SA_Sinal_Venda", "RADL3.SA_Media_OHLC_Futura", "RADL3.SA_Media_OHLC_PctChange_Lag_1", "RADL3.SA_Media_OHLC_PctChange_Lag_2", "RADL3.SA_Media_OHLC_PctChange_Lag_3", "RADL3.SA_Media_OHLC_PctChange_Lag_4", "RADL3.SA_Media_OHLC_PctChange_Lag_5", "RADL3.SA_Media_OHLC_PctChange_Lag_6", "RADL3.SA_Media_OHLC_PctChange_Lag_7", "RADL3.SA_Media_OHLC_PctChange_Lag_8", "RADL3.SA_Media_OHLC_PctChange_Lag_9", "RADL3.SA_Media_OHLC_PctChange_Lag_10", "RADL3.SA_MM10_PctChange", "RADL3.SA_Diff_OHLC_MM10", "RADL3.SA_MM25_PctChange", "RADL3.SA_Diff_OHLC_MM25", "RADL3.SA_MM100_PctChange", "RADL3.SA_Diff_OHLC_MM100", "RADL3.SA_MM10_Menos_MM25", "RADL3.SA_MM25_Menos_MM100", "RADL3.SA_MM10_Menos_MM100", "RADL3.SA_Volume_Lag_1", "RADL3.SA_Volume_Lag_2", "RADL3.SA_Volume_Lag_3", "RADL3.SA_Volume_Lag_4", "RADL3.SA_Volume_Lag_5", "RADL3.SA_Spread_Lag_1", "RADL3.SA_Spread_Lag_2", "RADL3.SA_Spread_Lag_3", "RADL3.SA_Spread_Lag_4", "RADL3.SA_Spread_Lag_5", "RADL3.SA_Volatilidade_Lag_1", "RADL3.SA_Volatilidade_Lag_2", "RADL3.SA_Volatilidade_Lag_3", "RADL3.SA_Volatilidade_Lag_4", "RADL3.SA_Volatilidade_Lag_5", "RADL3.<PERSON>_Parkinson_Volatility_Lag_1", "RADL3.<PERSON>_Parkinson_Volatility_Lag_2", "RADL3.<PERSON>_Parkinson_Volatility_Lag_3", "RADL3.<PERSON>_Parkinson_Volatility_Lag_4", "RADL3.<PERSON>_Parkinson_Volatility_Lag_5", "RADL3.SA_EMV_Lag_1", "RADL3.SA_EMV_Lag_2", "RADL3.SA_EMV_Lag_3", "RADL3.SA_EMV_Lag_4", "RADL3.SA_EMV_Lag_5", "RADL3.SA_EMV_MA_Lag_1", "RADL3.SA_EMV_MA_Lag_2", "RADL3.SA_EMV_MA_Lag_3", "RADL3.SA_EMV_MA_Lag_4", "RADL3.SA_EMV_MA_Lag_5", "RADL3.SA_VO_Lag_1", "RADL3.SA_VO_Lag_2", "RADL3.SA_VO_Lag_3", "RADL3.SA_VO_Lag_4", "RADL3.SA_VO_Lag_5", "RADL3.SA_High_Max_50_Lag_1", "RADL3.SA_High_Max_50_Lag_2", "RADL3.SA_High_Max_50_Lag_3", "RADL3.SA_High_Max_50_Lag_4", "RADL3.SA_High_Max_50_Lag_5", "RADL3.SA_Low_Min_50_Lag_1", "RADL3.SA_Low_Min_50_Lag_2", "RADL3.SA_Low_Min_50_Lag_3", "RADL3.SA_Low_Min_50_Lag_4", "RADL3.SA_Low_Min_50_Lag_5", "RADL3.SA_MFI_Lag_1", "RADL3.SA_MFI_Lag_2", "RADL3.SA_MFI_Lag_3", "RADL3.SA_MFI_Lag_4", "RADL3.SA_MFI_Lag_5", "RADL3.SA_Amihud_Lag_1", "RADL3.SA_Amihud_Lag_2", "RADL3.SA_Amihud_Lag_3", "RADL3.SA_Amihud_Lag_4", "RADL3.SA_Amihud_Lag_5", "RADL3.SA_Roll_Spread_Lag_1", "RADL3.SA_Roll_Spread_Lag_2", "RADL3.SA_Roll_Spread_Lag_3", "RADL3.SA_Roll_Spread_Lag_4", "RADL3.SA_Roll_Spread_Lag_5", "RADL3.<PERSON>_Hu<PERSON>_Lag_1", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_2", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_3", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_4", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_5", "RADL3.SA_Vol_per_Volume_Lag_1", "RADL3.SA_Vol_per_Volume_Lag_2", "RADL3.SA_Vol_per_Volume_Lag_3", "RADL3.SA_Vol_per_Volume_Lag_4", "RADL3.SA_Vol_per_Volume_Lag_5", "RADL3.SA_CMF_Lag_1", "RADL3.SA_CMF_Lag_2", "RADL3.SA_CMF_Lag_3", "RADL3.SA_CMF_Lag_4", "RADL3.SA_CMF_Lag_5", "RADL3.SA_AD_Line_Lag_1", "RADL3.SA_AD_Line_Lag_2", "RADL3.SA_AD_Line_Lag_3", "RADL3.SA_AD_Line_Lag_4", "RADL3.SA_AD_Line_Lag_5", "KLBN11.SA_Close", "KLBN11.SA_High", "KLBN11.SA_Low", "KLBN11.SA_Open", "KLBN11.SA_Volume", "KLBN11.SA_Media_OHLC", "KLBN11.SA_MM10", "KLBN11.SA_MM25", "KLBN11.SA_MM100", "KLBN11.SA_Media_OHLC_PctChange", "KLBN11.SA_Volatilidade", "KLBN11.SA_Spread", "KLBN11.SA_Parkinson_Volatility", "KLBN11.SA_MFI", "KLBN11.SA_MFI_Historical", "KLBN11.SA_EMV", "KLBN11.SA_EMV_MA", "KLBN11.SA_Amihud", "KLBN11.SA_Amihud_Historical", "KLBN11.SA_Roll_Spread", "KLBN11.SA_Roll_Spread_Historical", "KLBN11.SA_Hurst", "KLBN11.SA_Hurst_Historical", "KLBN11.SA_Vol_per_Volume", "KLBN11.SA_Vol_per_Volume_Historical", "KLBN11.SA_CMF", "KLBN11.SA_CMF_Historical", "KLBN11.SA_AD_Line", "KLBN11.SA_AD_Line_Historical", "KLBN11.SA_VO", "KLBN11.SA_High_Max_50", "KLBN11.SA_Low_Min_50", "KLBN11.SA_Segunda", "KLBN11.SA_Terca", "KLBN11.SA_Quarta", "KLBN11.SA_Quinta", "KLBN11.SA_Sexta", "KLBN11.SA_Mes_1", "KLBN11.SA_Mes_2", "KLBN11.SA_Mes_3", "KLBN11.SA_Mes_4", "KLBN11.SA_Mes_5", "KLBN11.SA_Mes_6", "KLBN11.SA_Mes_7", "KLBN11.SA_Mes_8", "KLBN11.SA_Mes_9", "KLBN11.SA_Mes_10", "KLBN11.SA_Mes_11", "KLBN11.SA_Mes_12", "KLBN11.SA_Quarter_1", "KLBN11.SA_Quarter_2", "KLBN11.SA_Quarter_3", "KLBN11.SA_Quarter_4", "KLBN11.SA_Last_Day_Quarter", "KLBN11.SA_Pre_Feriado_Brasil", "KLBN11.SA_Media_OHLC_Anterior", "KLBN11.SA_Sinal_Compra", "KLBN11.SA_Sinal_Venda", "KLBN11.SA_Media_OHLC_Futura", "KLBN11.SA_Media_OHLC_PctChange_Lag_1", "KLBN11.SA_Media_OHLC_PctChange_Lag_2", "KLBN11.SA_Media_OHLC_PctChange_Lag_3", "KLBN11.SA_Media_OHLC_PctChange_Lag_4", "KLBN11.SA_Media_OHLC_PctChange_Lag_5", "KLBN11.SA_Media_OHLC_PctChange_Lag_6", "KLBN11.SA_Media_OHLC_PctChange_Lag_7", "KLBN11.SA_Media_OHLC_PctChange_Lag_8", "KLBN11.SA_Media_OHLC_PctChange_Lag_9", "KLBN11.SA_Media_OHLC_PctChange_Lag_10", "KLBN11.SA_MM10_PctChange", "KLBN11.SA_Diff_OHLC_MM10", "KLBN11.SA_MM25_PctChange", "KLBN11.SA_Diff_OHLC_MM25", "KLBN11.SA_MM100_PctChange", "KLBN11.SA_Diff_OHLC_MM100", "KLBN11.SA_MM10_Menos_MM25", "KLBN11.SA_MM25_Menos_MM100", "KLBN11.SA_MM10_Menos_MM100", "KLBN11.SA_Volume_Lag_1", "KLBN11.SA_Volume_Lag_2", "KLBN11.SA_Volume_Lag_3", "KLBN11.SA_Volume_Lag_4", "KLBN11.SA_Volume_Lag_5", "KLBN11.SA_Spread_Lag_1", "KLBN11.SA_Spread_Lag_2", "KLBN11.SA_Spread_Lag_3", "KLBN11.SA_Spread_Lag_4", "KLBN11.SA_Spread_Lag_5", "KLBN11.SA_Volatilidade_Lag_1", "KLBN11.SA_Volatilidade_Lag_2", "KLBN11.SA_Volatilidade_Lag_3", "KLBN11.SA_Volatilidade_Lag_4", "KLBN11.SA_Volatilidade_Lag_5", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_1", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_2", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_3", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_4", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_5", "KLBN11.SA_EMV_Lag_1", "KLBN11.SA_EMV_Lag_2", "KLBN11.SA_EMV_Lag_3", "KLBN11.SA_EMV_Lag_4", "KLBN11.SA_EMV_Lag_5", "KLBN11.SA_EMV_MA_Lag_1", "KLBN11.SA_EMV_MA_Lag_2", "KLBN11.SA_EMV_MA_Lag_3", "KLBN11.SA_EMV_MA_Lag_4", "KLBN11.SA_EMV_MA_Lag_5", "KLBN11.SA_VO_Lag_1", "KLBN11.SA_VO_Lag_2", "KLBN11.SA_VO_Lag_3", "KLBN11.SA_VO_Lag_4", "KLBN11.SA_VO_Lag_5", "KLBN11.SA_High_Max_50_Lag_1", "KLBN11.SA_High_Max_50_Lag_2", "KLBN11.SA_High_Max_50_Lag_3", "KLBN11.SA_High_Max_50_Lag_4", "KLBN11.SA_High_Max_50_Lag_5", "KLBN11.SA_Low_Min_50_Lag_1", "KLBN11.SA_Low_Min_50_Lag_2", "KLBN11.SA_Low_Min_50_Lag_3", "KLBN11.SA_Low_Min_50_Lag_4", "KLBN11.SA_Low_Min_50_Lag_5", "KLBN11.SA_MFI_Lag_1", "KLBN11.SA_MFI_Lag_2", "KLBN11.SA_MFI_Lag_3", "KLBN11.SA_MFI_Lag_4", "KLBN11.SA_MFI_Lag_5", "KLBN11.SA_Amihud_Lag_1", "KLBN11.SA_Amihud_Lag_2", "KLBN11.SA_Amihud_Lag_3", "KLBN11.SA_Amihud_Lag_4", "KLBN11.SA_Amihud_Lag_5", "KLBN11.SA_Roll_Spread_Lag_1", "KLBN11.SA_Roll_Spread_Lag_2", "KLBN11.SA_Roll_Spread_Lag_3", "KLBN11.SA_Roll_Spread_Lag_4", "KLBN11.SA_Roll_Spread_Lag_5", "KLBN11.SA_Hurst_Lag_1", "KLBN11.SA_Hurst_Lag_2", "KLBN11.SA_Hurst_Lag_3", "KLBN11.SA_Hurst_Lag_4", "KLBN11.<PERSON>_Hurst_Lag_5", "KLBN11.SA_Vol_per_Volume_Lag_1", "KLBN11.SA_Vol_per_Volume_Lag_2", "KLBN11.SA_Vol_per_Volume_Lag_3", "KLBN11.SA_Vol_per_Volume_Lag_4", "KLBN11.SA_Vol_per_Volume_Lag_5", "KLBN11.SA_CMF_Lag_1", "KLBN11.SA_CMF_Lag_2", "KLBN11.SA_CMF_Lag_3", "KLBN11.SA_CMF_Lag_4", "KLBN11.SA_CMF_Lag_5", "KLBN11.SA_AD_Line_Lag_1", "KLBN11.SA_AD_Line_Lag_2", "KLBN11.SA_AD_Line_Lag_3", "KLBN11.SA_AD_Line_Lag_4", "KLBN11.SA_AD_Line_Lag_5", "ABEV3.SA_Open", "ABEV3.SA_High", "ABEV3.SA_Low", "ABEV3.SA_Close", "ABEV3.SA_Volume", "ABEV3.SA_Media_OHLC", "ABEV3.SA_MM10", "ABEV3.SA_MM25", "ABEV3.SA_MM100", "ABEV3.SA_Media_OHLC_PctChange", "ABEV3.SA_Volatilidade", "ABEV3.SA_Spread", "ABEV3.SA_Parkinson_Volatility", "ABEV3.SA_MFI", "ABEV3.SA_MFI_Historical", "ABEV3.SA_EMV", "ABEV3.SA_EMV_MA", "ABEV3.SA_Amihud", "ABEV3.SA_Amihud_Historical", "ABEV3.SA_Roll_Spread", "ABEV3.SA_Roll_Spread_Historical", "ABEV3.SA_Hurst", "ABEV3.SA_<PERSON><PERSON>_Historical", "ABEV3.SA_Vol_per_Volume", "ABEV3.SA_Vol_per_Volume_Historical", "ABEV3.SA_CMF", "ABEV3.SA_CMF_Historical", "ABEV3.SA_AD_Line", "ABEV3.SA_AD_Line_Historical", "ABEV3.SA_VO", "ABEV3.SA_High_Max_50", "ABEV3.SA_Low_Min_50", "ABEV3.SA_Segunda", "ABEV3.SA_Terca", "ABEV3.SA_Quarta", "ABEV3.SA_Quinta", "ABEV3.SA_Sexta", "ABEV3.SA_Mes_1", "ABEV3.SA_Mes_2", "ABEV3.SA_Mes_3", "ABEV3.SA_Mes_4", "ABEV3.SA_Mes_5", "ABEV3.SA_Mes_6", "ABEV3.SA_Mes_7", "ABEV3.SA_Mes_8", "ABEV3.SA_Mes_9", "ABEV3.SA_Mes_10", "ABEV3.SA_Mes_11", "ABEV3.SA_Mes_12", "ABEV3.SA_Quarter_1", "ABEV3.SA_Quarter_2", "ABEV3.SA_Quarter_3", "ABEV3.SA_Quarter_4", "ABEV3.SA_Last_Day_Quarter", "ABEV3.SA_Pre_Feriado_Brasil", "ABEV3.SA_Media_OHLC_Anterior", "ABEV3.SA_Sinal_Compra", "ABEV3.SA_Sinal_Venda", "ABEV3.SA_Media_OHLC_Futura", "ABEV3.SA_Media_OHLC_PctChange_Lag_1", "ABEV3.SA_Media_OHLC_PctChange_Lag_2", "ABEV3.SA_Media_OHLC_PctChange_Lag_3", "ABEV3.SA_Media_OHLC_PctChange_Lag_4", "ABEV3.SA_Media_OHLC_PctChange_Lag_5", "ABEV3.SA_Media_OHLC_PctChange_Lag_6", "ABEV3.SA_Media_OHLC_PctChange_Lag_7", "ABEV3.SA_Media_OHLC_PctChange_Lag_8", "ABEV3.SA_Media_OHLC_PctChange_Lag_9", "ABEV3.SA_Media_OHLC_PctChange_Lag_10", "ABEV3.SA_MM10_PctChange", "ABEV3.SA_Diff_OHLC_MM10", "ABEV3.SA_MM25_PctChange", "ABEV3.SA_Diff_OHLC_MM25", "ABEV3.SA_MM100_PctChange", "ABEV3.SA_Diff_OHLC_MM100", "ABEV3.SA_MM10_Menos_MM25", "ABEV3.SA_MM25_Menos_MM100", "ABEV3.SA_MM10_Menos_MM100", "ABEV3.SA_Volume_Lag_1", "ABEV3.SA_Volume_Lag_2", "ABEV3.SA_Volume_Lag_3", "ABEV3.SA_Volume_Lag_4", "ABEV3.SA_Volume_Lag_5", "ABEV3.SA_Spread_Lag_1", "ABEV3.SA_Spread_Lag_2", "ABEV3.SA_Spread_Lag_3", "ABEV3.SA_Spread_Lag_4", "ABEV3.SA_Spread_Lag_5", "ABEV3.SA_Volatilidade_Lag_1", "ABEV3.SA_Volatilidade_Lag_2", "ABEV3.SA_Volatilidade_Lag_3", "ABEV3.SA_Volatilidade_Lag_4", "ABEV3.SA_Volatilidade_Lag_5", "ABEV3.SA_Parkinson_Volatility_Lag_1", "ABEV3.SA_Parkinson_Volatility_Lag_2", "ABEV3.SA_Parkinson_Volatility_Lag_3", "ABEV3.SA_Parkinson_Volatility_Lag_4", "ABEV3.SA_Parkinson_Volatility_Lag_5", "ABEV3.SA_EMV_Lag_1", "ABEV3.SA_EMV_Lag_2", "ABEV3.SA_EMV_Lag_3", "ABEV3.SA_EMV_Lag_4", "ABEV3.SA_EMV_Lag_5", "ABEV3.SA_EMV_MA_Lag_1", "ABEV3.SA_EMV_MA_Lag_2", "ABEV3.SA_EMV_MA_Lag_3", "ABEV3.SA_EMV_MA_Lag_4", "ABEV3.SA_EMV_MA_Lag_5", "ABEV3.SA_VO_Lag_1", "ABEV3.SA_VO_Lag_2", "ABEV3.SA_VO_Lag_3", "ABEV3.SA_VO_Lag_4", "ABEV3.SA_VO_Lag_5", "ABEV3.SA_High_Max_50_Lag_1", "ABEV3.SA_High_Max_50_Lag_2", "ABEV3.SA_High_Max_50_Lag_3", "ABEV3.SA_High_Max_50_Lag_4", "ABEV3.SA_High_Max_50_Lag_5", "ABEV3.SA_Low_Min_50_Lag_1", "ABEV3.SA_Low_Min_50_Lag_2", "ABEV3.SA_Low_Min_50_Lag_3", "ABEV3.SA_Low_Min_50_Lag_4", "ABEV3.SA_Low_Min_50_Lag_5", "ABEV3.SA_MFI_Lag_1", "ABEV3.SA_MFI_Lag_2", "ABEV3.SA_MFI_Lag_3", "ABEV3.SA_MFI_Lag_4", "ABEV3.SA_MFI_Lag_5", "ABEV3.SA_Amihud_Lag_1", "ABEV3.SA_Amihud_Lag_2", "ABEV3.SA_Amihud_Lag_3", "ABEV3.SA_Amihud_Lag_4", "ABEV3.SA_Amihud_Lag_5", "ABEV3.SA_Roll_Spread_Lag_1", "ABEV3.SA_Roll_Spread_Lag_2", "ABEV3.SA_Roll_Spread_Lag_3", "ABEV3.SA_Roll_Spread_Lag_4", "ABEV3.SA_Roll_Spread_Lag_5", "ABEV3.SA_Hurst_Lag_1", "ABEV3.SA_Hurst_Lag_2", "ABEV3.SA_Hurst_Lag_3", "ABEV3.<PERSON>_<PERSON><PERSON>_Lag_4", "ABEV3.<PERSON>_<PERSON><PERSON>_Lag_5", "ABEV3.SA_Vol_per_Volume_Lag_1", "ABEV3.SA_Vol_per_Volume_Lag_2", "ABEV3.SA_Vol_per_Volume_Lag_3", "ABEV3.SA_Vol_per_Volume_Lag_4", "ABEV3.SA_Vol_per_Volume_Lag_5", "ABEV3.SA_CMF_Lag_1", "ABEV3.SA_CMF_Lag_2", "ABEV3.SA_CMF_Lag_3", "ABEV3.SA_CMF_Lag_4", "ABEV3.SA_CMF_Lag_5", "ABEV3.SA_AD_Line_Lag_1", "ABEV3.SA_AD_Line_Lag_2", "ABEV3.SA_AD_Line_Lag_3", "ABEV3.SA_AD_Line_Lag_4", "ABEV3.SA_AD_Line_Lag_5", "ODPV3.SA_Open", "ODPV3.SA_High", "ODPV3.SA_Low", "ODPV3.SA_Close", "ODPV3.SA_Volume", "ODPV3.SA_Media_OHLC", "ODPV3.SA_MM10", "ODPV3.SA_MM25", "ODPV3.SA_MM100", "ODPV3.SA_Media_OHLC_PctChange", "ODPV3.SA_Volatilidade", "ODPV3.SA_Spread", "ODPV3.SA_Parkinson_Volatility", "ODPV3.SA_MFI", "ODPV3.SA_MFI_Historical", "ODPV3.SA_EMV", "ODPV3.SA_EMV_MA", "ODPV3.SA_Amihud", "ODPV3.SA_Amihud_Historical", "ODPV3.SA_Roll_Spread", "ODPV3.SA_Roll_Spread_Historical", "ODPV3.SA_Hurst", "ODPV3.SA_Hurst_Historical", "ODPV3.SA_Vol_per_Volume", "ODPV3.SA_Vol_per_Volume_Historical", "ODPV3.SA_CMF", "ODPV3.SA_CMF_Historical", "ODPV3.SA_AD_Line", "ODPV3.SA_AD_Line_Historical", "ODPV3.SA_VO", "ODPV3.SA_High_Max_50", "ODPV3.SA_Low_Min_50", "ODPV3.SA_Segunda", "ODPV3.SA_Terca", "ODPV3.SA_Quarta", "ODPV3.SA_Quinta", "ODPV3.SA_Sexta", "ODPV3.SA_Mes_1", "ODPV3.SA_Mes_2", "ODPV3.SA_Mes_3", "ODPV3.SA_Mes_4", "ODPV3.SA_Mes_5", "ODPV3.SA_Mes_6", "ODPV3.SA_Mes_7", "ODPV3.SA_Mes_8", "ODPV3.SA_Mes_9", "ODPV3.SA_Mes_10", "ODPV3.SA_Mes_11", "ODPV3.SA_Mes_12", "ODPV3.SA_Quarter_1", "ODPV3.SA_Quarter_2", "ODPV3.SA_Quarter_3", "ODPV3.SA_Quarter_4", "ODPV3.SA_Last_Day_Quarter", "ODPV3.SA_Pre_Feriado_Brasil", "ODPV3.SA_Media_OHLC_Anterior", "ODPV3.SA_Sinal_Compra", "ODPV3.SA_Sinal_Venda", "ODPV3.SA_Media_OHLC_Futura", "ODPV3.SA_Media_OHLC_PctChange_Lag_1", "ODPV3.SA_Media_OHLC_PctChange_Lag_2", "ODPV3.SA_Media_OHLC_PctChange_Lag_3", "ODPV3.SA_Media_OHLC_PctChange_Lag_4", "ODPV3.SA_Media_OHLC_PctChange_Lag_5", "ODPV3.SA_Media_OHLC_PctChange_Lag_6", "ODPV3.SA_Media_OHLC_PctChange_Lag_7", "ODPV3.SA_Media_OHLC_PctChange_Lag_8", "ODPV3.SA_Media_OHLC_PctChange_Lag_9", "ODPV3.SA_Media_OHLC_PctChange_Lag_10", "ODPV3.SA_MM10_PctChange", "ODPV3.SA_Diff_OHLC_MM10", "ODPV3.SA_MM25_PctChange", "ODPV3.SA_Diff_OHLC_MM25", "ODPV3.SA_MM100_PctChange", "ODPV3.SA_Diff_OHLC_MM100", "ODPV3.SA_MM10_Menos_MM25", "ODPV3.SA_MM25_Menos_MM100", "ODPV3.SA_MM10_Menos_MM100", "ODPV3.SA_Volume_Lag_1", "ODPV3.SA_Volume_Lag_2", "ODPV3.SA_Volume_Lag_3", "ODPV3.SA_Volume_Lag_4", "ODPV3.SA_Volume_Lag_5", "ODPV3.SA_Spread_Lag_1", "ODPV3.SA_Spread_Lag_2", "ODPV3.SA_Spread_Lag_3", "ODPV3.SA_Spread_Lag_4", "ODPV3.SA_Spread_Lag_5", "ODPV3.SA_Volatilidade_Lag_1", "ODPV3.SA_Volatilidade_Lag_2", "ODPV3.SA_Volatilidade_Lag_3", "ODPV3.SA_Volatilidade_Lag_4", "ODPV3.SA_Volatilidade_Lag_5", "ODPV3.SA_Parkinson_Volatility_Lag_1", "ODPV3.SA_Parkinson_Volatility_Lag_2", "ODPV3.SA_Parkinson_Volatility_Lag_3", "ODPV3.SA_Parkinson_Volatility_Lag_4", "ODPV3.SA_Parkinson_Volatility_Lag_5", "ODPV3.SA_EMV_Lag_1", "ODPV3.SA_EMV_Lag_2", "ODPV3.SA_EMV_Lag_3", "ODPV3.SA_EMV_Lag_4", "ODPV3.SA_EMV_Lag_5", "ODPV3.SA_EMV_MA_Lag_1", "ODPV3.SA_EMV_MA_Lag_2", "ODPV3.SA_EMV_MA_Lag_3", "ODPV3.SA_EMV_MA_Lag_4", "ODPV3.SA_EMV_MA_Lag_5", "ODPV3.SA_VO_Lag_1", "ODPV3.SA_VO_Lag_2", "ODPV3.SA_VO_Lag_3", "ODPV3.SA_VO_Lag_4", "ODPV3.SA_VO_Lag_5", "ODPV3.SA_High_Max_50_Lag_1", "ODPV3.SA_High_Max_50_Lag_2", "ODPV3.SA_High_Max_50_Lag_3", "ODPV3.SA_High_Max_50_Lag_4", "ODPV3.SA_High_Max_50_Lag_5", "ODPV3.SA_Low_Min_50_Lag_1", "ODPV3.SA_Low_Min_50_Lag_2", "ODPV3.SA_Low_Min_50_Lag_3", "ODPV3.SA_Low_Min_50_Lag_4", "ODPV3.SA_Low_Min_50_Lag_5", "ODPV3.SA_MFI_Lag_1", "ODPV3.SA_MFI_Lag_2", "ODPV3.SA_MFI_Lag_3", "ODPV3.SA_MFI_Lag_4", "ODPV3.SA_MFI_Lag_5", "ODPV3.SA_Amihud_Lag_1", "ODPV3.SA_Amihud_Lag_2", "ODPV3.SA_Amihud_Lag_3", "ODPV3.SA_Amihud_Lag_4", "ODPV3.SA_Amihud_Lag_5", "ODPV3.SA_Roll_Spread_Lag_1", "ODPV3.SA_Roll_Spread_Lag_2", "ODPV3.SA_Roll_Spread_Lag_3", "ODPV3.SA_Roll_Spread_Lag_4", "ODPV3.SA_Roll_Spread_Lag_5", "ODPV3.SA_Hurst_Lag_1", "ODPV3.SA_Hurst_Lag_2", "ODPV3.SA_Hurst_Lag_3", "ODPV3.SA_<PERSON>rst_Lag_4", "ODPV3.SA_<PERSON><PERSON>_Lag_5", "ODPV3.SA_Vol_per_Volume_Lag_1", "ODPV3.SA_Vol_per_Volume_Lag_2", "ODPV3.SA_Vol_per_Volume_Lag_3", "ODPV3.SA_Vol_per_Volume_Lag_4", "ODPV3.SA_Vol_per_Volume_Lag_5", "ODPV3.SA_CMF_Lag_1", "ODPV3.SA_CMF_Lag_2", "ODPV3.SA_CMF_Lag_3", "ODPV3.SA_CMF_Lag_4", "ODPV3.SA_CMF_Lag_5", "ODPV3.SA_AD_Line_Lag_1", "ODPV3.SA_AD_Line_Lag_2", "ODPV3.SA_AD_Line_Lag_3", "ODPV3.SA_AD_Line_Lag_4", "ODPV3.SA_AD_Line_Lag_5", "PRIO3.SA_Open", "PRIO3.SA_High", "PRIO3.SA_Low", "PRIO3.SA_Close", "PRIO3.SA_Volume", "PRIO3.SA_Media_OHLC", "PRIO3.SA_MM10", "PRIO3.SA_MM25", "PRIO3.SA_MM100", "PRIO3.SA_Media_OHLC_PctChange", "PRIO3.SA_Volatilidade", "PRIO3.SA_Spread", "PRIO3.SA_Parkinson_Volatility", "PRIO3.SA_MFI", "PRIO3.SA_MFI_Historical", "PRIO3.SA_EMV", "PRIO3.SA_EMV_MA", "PRIO3.SA_Amihud", "PRIO3.SA_Amihud_Historical", "PRIO3.SA_Roll_Spread", "PRIO3.SA_Roll_Spread_Historical", "PRIO3.SA_Hurst", "PRIO3.<PERSON>_<PERSON><PERSON>_Historical", "PRIO3.SA_Vol_per_Volume", "PRIO3.SA_Vol_per_Volume_Historical", "PRIO3.SA_CMF", "PRIO3.SA_CMF_Historical", "PRIO3.SA_AD_Line", "PRIO3.SA_AD_Line_Historical", "PRIO3.SA_VO", "PRIO3.SA_High_Max_50", "PRIO3.SA_Low_Min_50", "PRIO3.SA_Segunda", "PRIO3.SA_Terca", "PRIO3.SA_Quarta", "PRIO3.SA_Quinta", "PRIO3.SA_Sexta", "PRIO3.SA_Mes_1", "PRIO3.SA_Mes_2", "PRIO3.SA_Mes_3", "PRIO3.SA_Mes_4", "PRIO3.SA_Mes_5", "PRIO3.SA_Mes_6", "PRIO3.SA_Mes_7", "PRIO3.SA_Mes_8", "PRIO3.SA_Mes_9", "PRIO3.SA_Mes_10", "PRIO3.SA_Mes_11", "PRIO3.SA_Mes_12", "PRIO3.SA_Quarter_1", "PRIO3.SA_Quarter_2", "PRIO3.SA_Quarter_3", "PRIO3.SA_Quarter_4", "PRIO3.SA_Last_Day_Quarter", "PRIO3.SA_Pre_Feriado_Brasil", "PRIO3.SA_Media_OHLC_Anterior", "PRIO3.SA_Sinal_Compra", "PRIO3.SA_Sinal_Venda", "PRIO3.SA_Media_OHLC_Futura", "PRIO3.SA_Media_OHLC_PctChange_Lag_1", "PRIO3.SA_Media_OHLC_PctChange_Lag_2", "PRIO3.SA_Media_OHLC_PctChange_Lag_3", "PRIO3.SA_Media_OHLC_PctChange_Lag_4", "PRIO3.SA_Media_OHLC_PctChange_Lag_5", "PRIO3.SA_Media_OHLC_PctChange_Lag_6", "PRIO3.SA_Media_OHLC_PctChange_Lag_7", "PRIO3.SA_Media_OHLC_PctChange_Lag_8", "PRIO3.SA_Media_OHLC_PctChange_Lag_9", "PRIO3.SA_Media_OHLC_PctChange_Lag_10", "PRIO3.SA_MM10_PctChange", "PRIO3.SA_Diff_OHLC_MM10", "PRIO3.SA_MM25_PctChange", "PRIO3.SA_Diff_OHLC_MM25", "PRIO3.SA_MM100_PctChange", "PRIO3.SA_Diff_OHLC_MM100", "PRIO3.SA_MM10_Menos_MM25", "PRIO3.SA_MM25_Menos_MM100", "PRIO3.SA_MM10_Menos_MM100", "PRIO3.SA_Volume_Lag_1", "PRIO3.SA_Volume_Lag_2", "PRIO3.SA_Volume_Lag_3", "PRIO3.SA_Volume_Lag_4", "PRIO3.SA_Volume_Lag_5", "PRIO3.SA_Spread_Lag_1", "PRIO3.SA_Spread_Lag_2", "PRIO3.SA_Spread_Lag_3", "PRIO3.SA_Spread_Lag_4", "PRIO3.SA_Spread_Lag_5", "PRIO3.SA_Volatilidade_Lag_1", "PRIO3.SA_Volatilidade_Lag_2", "PRIO3.SA_Volatilidade_Lag_3", "PRIO3.SA_Volatilidade_Lag_4", "PRIO3.SA_Volatilidade_Lag_5", "PRIO3.SA_Parkinson_Volatility_Lag_1", "PRIO3.SA_Parkinson_Volatility_Lag_2", "PRIO3.SA_Parkinson_Volatility_Lag_3", "PRIO3.SA_Parkinson_Volatility_Lag_4", "PRIO3.SA_Parkinson_Volatility_Lag_5", "PRIO3.SA_EMV_Lag_1", "PRIO3.SA_EMV_Lag_2", "PRIO3.SA_EMV_Lag_3", "PRIO3.SA_EMV_Lag_4", "PRIO3.SA_EMV_Lag_5", "PRIO3.SA_EMV_MA_Lag_1", "PRIO3.SA_EMV_MA_Lag_2", "PRIO3.SA_EMV_MA_Lag_3", "PRIO3.SA_EMV_MA_Lag_4", "PRIO3.SA_EMV_MA_Lag_5", "PRIO3.SA_VO_Lag_1", "PRIO3.SA_VO_Lag_2", "PRIO3.SA_VO_Lag_3", "PRIO3.SA_VO_Lag_4", "PRIO3.SA_VO_Lag_5", "PRIO3.SA_High_Max_50_Lag_1", "PRIO3.SA_High_Max_50_Lag_2", "PRIO3.SA_High_Max_50_Lag_3", "PRIO3.SA_High_Max_50_Lag_4", "PRIO3.SA_High_Max_50_Lag_5", "PRIO3.SA_Low_Min_50_Lag_1", "PRIO3.SA_Low_Min_50_Lag_2", "PRIO3.SA_Low_Min_50_Lag_3", "PRIO3.SA_Low_Min_50_Lag_4", "PRIO3.SA_Low_Min_50_Lag_5", "PRIO3.SA_MFI_Lag_1", "PRIO3.SA_MFI_Lag_2", "PRIO3.SA_MFI_Lag_3", "PRIO3.SA_MFI_Lag_4", "PRIO3.SA_MFI_Lag_5", "PRIO3.SA_Amihud_Lag_1", "PRIO3.SA_Amihud_Lag_2", "PRIO3.SA_Amihud_Lag_3", "PRIO3.SA_Amihud_Lag_4", "PRIO3.SA_Amihud_Lag_5", "PRIO3.SA_Roll_Spread_Lag_1", "PRIO3.SA_Roll_Spread_Lag_2", "PRIO3.SA_Roll_Spread_Lag_3", "PRIO3.SA_Roll_Spread_Lag_4", "PRIO3.SA_Roll_Spread_Lag_5", "PRIO3.SA_Hurst_Lag_1", "PRIO3.<PERSON>_<PERSON><PERSON>_Lag_2", "PRIO3.SA_<PERSON>rst_Lag_3", "PRIO3.<PERSON>_<PERSON><PERSON>_Lag_4", "PRIO3.<PERSON>_<PERSON><PERSON>_Lag_5", "PRIO3.SA_Vol_per_Volume_Lag_1", "PRIO3.SA_Vol_per_Volume_Lag_2", "PRIO3.SA_Vol_per_Volume_Lag_3", "PRIO3.SA_Vol_per_Volume_Lag_4", "PRIO3.SA_Vol_per_Volume_Lag_5", "PRIO3.SA_CMF_Lag_1", "PRIO3.SA_CMF_Lag_2", "PRIO3.SA_CMF_Lag_3", "PRIO3.SA_CMF_Lag_4", "PRIO3.SA_CMF_Lag_5", "PRIO3.SA_AD_Line_Lag_1", "PRIO3.SA_AD_Line_Lag_2", "PRIO3.SA_AD_Line_Lag_3", "PRIO3.SA_AD_Line_Lag_4", "PRIO3.SA_AD_Line_Lag_5", "VVEO3.SA_Close", "VVEO3.SA_High", "VVEO3.SA_Low", "VVEO3.SA_Open", "VVEO3.SA_Volume", "VVEO3.SA_Media_OHLC", "VVEO3.SA_MM10", "VVEO3.SA_MM25", "VVEO3.SA_MM100", "VVEO3.SA_Media_OHLC_PctChange", "VVEO3.SA_Volatilidade", "VVEO3.SA_Spread", "VVEO3.SA_Parkinson_Volatility", "VVEO3.SA_MFI", "VVEO3.SA_MFI_Historical", "VVEO3.SA_EMV", "VVEO3.SA_EMV_MA", "VVEO3.SA_Amihud", "VVEO3.SA_Amihud_Historical", "VVEO3.SA_Roll_Spread", "VVEO3.SA_Roll_Spread_Historical", "VVEO3.SA_Hurst", "VVEO3.<PERSON>_<PERSON><PERSON>_Historical", "VVEO3.SA_Vol_per_Volume", "VVEO3.SA_Vol_per_Volume_Historical", "VVEO3.SA_CMF", "VVEO3.SA_CMF_Historical", "VVEO3.SA_AD_Line", "VVEO3.SA_AD_Line_Historical", "VVEO3.SA_VO", "VVEO3.SA_High_Max_50", "VVEO3.SA_Low_Min_50", "VVEO3.SA_Segunda", "VVEO3.SA_Terca", "VVEO3.SA_Quarta", "VVEO3.SA_Quinta", "VVEO3.SA_Sexta", "VVEO3.SA_Mes_1", "VVEO3.SA_Mes_2", "VVEO3.SA_Mes_3", "VVEO3.SA_Mes_4", "VVEO3.SA_Mes_5", "VVEO3.SA_Mes_6", "VVEO3.SA_Mes_7", "VVEO3.SA_Mes_8", "VVEO3.SA_Mes_9", "VVEO3.SA_Mes_10", "VVEO3.SA_Mes_11", "VVEO3.SA_Mes_12", "VVEO3.SA_Quarter_1", "VVEO3.SA_Quarter_2", "VVEO3.SA_Quarter_3", "VVEO3.SA_Quarter_4", "VVEO3.SA_Last_Day_Quarter", "VVEO3.SA_Pre_Feriado_Brasil", "VVEO3.SA_Media_OHLC_Anterior", "VVEO3.SA_Sinal_Compra", "VVEO3.SA_Sinal_Venda", "VVEO3.SA_Media_OHLC_Futura", "VVEO3.SA_Media_OHLC_PctChange_Lag_1", "VVEO3.SA_Media_OHLC_PctChange_Lag_2", "VVEO3.SA_Media_OHLC_PctChange_Lag_3", "VVEO3.SA_Media_OHLC_PctChange_Lag_4", "VVEO3.SA_Media_OHLC_PctChange_Lag_5", "VVEO3.SA_Media_OHLC_PctChange_Lag_6", "VVEO3.SA_Media_OHLC_PctChange_Lag_7", "VVEO3.SA_Media_OHLC_PctChange_Lag_8", "VVEO3.SA_Media_OHLC_PctChange_Lag_9", "VVEO3.SA_Media_OHLC_PctChange_Lag_10", "VVEO3.SA_MM10_PctChange", "VVEO3.SA_Diff_OHLC_MM10", "VVEO3.SA_MM25_PctChange", "VVEO3.SA_Diff_OHLC_MM25", "VVEO3.SA_MM100_PctChange", "VVEO3.SA_Diff_OHLC_MM100", "VVEO3.SA_MM10_Menos_MM25", "VVEO3.SA_MM25_Menos_MM100", "VVEO3.SA_MM10_Menos_MM100", "VVEO3.SA_Volume_Lag_1", "VVEO3.SA_Volume_Lag_2", "VVEO3.SA_Volume_Lag_3", "VVEO3.SA_Volume_Lag_4", "VVEO3.SA_Volume_Lag_5", "VVEO3.SA_Spread_Lag_1", "VVEO3.SA_Spread_Lag_2", "VVEO3.SA_Spread_Lag_3", "VVEO3.SA_Spread_Lag_4", "VVEO3.SA_Spread_Lag_5", "VVEO3.SA_Volatilidade_Lag_1", "VVEO3.SA_Volatilidade_Lag_2", "VVEO3.SA_Volatilidade_Lag_3", "VVEO3.SA_Volatilidade_Lag_4", "VVEO3.SA_Volatilidade_Lag_5", "VVEO3.SA_Parkinson_Volatility_Lag_1", "VVEO3.SA_Parkinson_Volatility_Lag_2", "VVEO3.SA_Parkinson_Volatility_Lag_3", "VVEO3.SA_Parkinson_Volatility_Lag_4", "VVEO3.SA_Parkinson_Volatility_Lag_5", "VVEO3.SA_EMV_Lag_1", "VVEO3.SA_EMV_Lag_2", "VVEO3.SA_EMV_Lag_3", "VVEO3.SA_EMV_Lag_4", "VVEO3.SA_EMV_Lag_5", "VVEO3.SA_EMV_MA_Lag_1", "VVEO3.SA_EMV_MA_Lag_2", "VVEO3.SA_EMV_MA_Lag_3", "VVEO3.SA_EMV_MA_Lag_4", "VVEO3.SA_EMV_MA_Lag_5", "VVEO3.SA_VO_Lag_1", "VVEO3.SA_VO_Lag_2", "VVEO3.SA_VO_Lag_3", "VVEO3.SA_VO_Lag_4", "VVEO3.SA_VO_Lag_5", "VVEO3.SA_High_Max_50_Lag_1", "VVEO3.SA_High_Max_50_Lag_2", "VVEO3.SA_High_Max_50_Lag_3", "VVEO3.SA_High_Max_50_Lag_4", "VVEO3.SA_High_Max_50_Lag_5", "VVEO3.SA_Low_Min_50_Lag_1", "VVEO3.SA_Low_Min_50_Lag_2", "VVEO3.SA_Low_Min_50_Lag_3", "VVEO3.SA_Low_Min_50_Lag_4", "VVEO3.SA_Low_Min_50_Lag_5", "VVEO3.SA_MFI_Lag_1", "VVEO3.SA_MFI_Lag_2", "VVEO3.SA_MFI_Lag_3", "VVEO3.SA_MFI_Lag_4", "VVEO3.SA_MFI_Lag_5", "VVEO3.SA_Amihud_Lag_1", "VVEO3.SA_Amihud_Lag_2", "VVEO3.SA_Amihud_Lag_3", "VVEO3.SA_Amihud_Lag_4", "VVEO3.SA_Amihud_Lag_5", "VVEO3.SA_Roll_Spread_Lag_1", "VVEO3.SA_Roll_Spread_Lag_2", "VVEO3.SA_Roll_Spread_Lag_3", "VVEO3.SA_Roll_Spread_Lag_4", "VVEO3.SA_Roll_Spread_Lag_5", "VVEO3.SA_Hurst_Lag_1", "VVEO3.<PERSON>_Hu<PERSON>_Lag_2", "VVEO3.<PERSON>_Hurst_Lag_3", "VVEO3.<PERSON>_<PERSON><PERSON>_Lag_4", "VVEO3.<PERSON>_<PERSON><PERSON>_Lag_5", "VVEO3.SA_Vol_per_Volume_Lag_1", "VVEO3.SA_Vol_per_Volume_Lag_2", "VVEO3.SA_Vol_per_Volume_Lag_3", "VVEO3.SA_Vol_per_Volume_Lag_4", "VVEO3.SA_Vol_per_Volume_Lag_5", "VVEO3.SA_CMF_Lag_1", "VVEO3.SA_CMF_Lag_2", "VVEO3.SA_CMF_Lag_3", "VVEO3.SA_CMF_Lag_4", "VVEO3.SA_CMF_Lag_5", "VVEO3.SA_AD_Line_Lag_1", "VVEO3.SA_AD_Line_Lag_2", "VVEO3.SA_AD_Line_Lag_3", "VVEO3.SA_AD_Line_Lag_4", "VVEO3.SA_AD_Line_Lag_5", "AGRO3.SA_Open", "AGRO3.SA_High", "AGRO3.SA_Low", "AGRO3.SA_Close", "AGRO3.SA_Volume", "AGRO3.SA_Media_OHLC", "AGRO3.SA_MM10", "AGRO3.SA_MM25", "AGRO3.SA_MM100", "AGRO3.SA_Media_OHLC_PctChange", "AGRO3.SA_Volatilidade", "AGRO3.SA_Spread", "AGRO3.SA_Parkinson_Volatility", "AGRO3.SA_MFI", "AGRO3.SA_MFI_Historical", "AGRO3.SA_EMV", "AGRO3.SA_EMV_MA", "AGRO3.SA_Amihud", "AGRO3.SA_Amihud_Historical", "AGRO3.SA_Roll_Spread", "AGRO3.SA_Roll_Spread_Historical", "AGRO3.SA_Hurst", "AGRO3.SA_<PERSON>rst_Historical", "AGRO3.SA_Vol_per_Volume", "AGRO3.SA_Vol_per_Volume_Historical", "AGRO3.SA_CMF", "AGRO3.SA_CMF_Historical", "AGRO3.SA_AD_Line", "AGRO3.SA_AD_Line_Historical", "AGRO3.SA_VO", "AGRO3.SA_High_Max_50", "AGRO3.SA_Low_Min_50", "AGRO3.SA_Segunda", "AGRO3.SA_Terca", "AGRO3.SA_Quarta", "AGRO3.SA_Quinta", "AGRO3.SA_Sexta", "AGRO3.SA_Mes_1", "AGRO3.SA_Mes_2", "AGRO3.SA_Mes_3", "AGRO3.SA_Mes_4", "AGRO3.SA_Mes_5", "AGRO3.SA_Mes_6", "AGRO3.SA_Mes_7", "AGRO3.SA_Mes_8", "AGRO3.SA_Mes_9", "AGRO3.SA_Mes_10", "AGRO3.SA_Mes_11", "AGRO3.SA_Mes_12", "AGRO3.SA_Quarter_1", "AGRO3.SA_Quarter_2", "AGRO3.SA_Quarter_3", "AGRO3.SA_Quarter_4", "AGRO3.SA_Last_Day_Quarter", "AGRO3.SA_Pre_Feriado_Brasil", "AGRO3.SA_Media_OHLC_Anterior", "AGRO3.SA_Sinal_Compra", "AGRO3.SA_Sinal_Venda", "AGRO3.SA_Media_OHLC_Futura", "AGRO3.SA_Media_OHLC_PctChange_Lag_1", "AGRO3.SA_Media_OHLC_PctChange_Lag_2", "AGRO3.SA_Media_OHLC_PctChange_Lag_3", "AGRO3.SA_Media_OHLC_PctChange_Lag_4", "AGRO3.SA_Media_OHLC_PctChange_Lag_5", "AGRO3.SA_Media_OHLC_PctChange_Lag_6", "AGRO3.SA_Media_OHLC_PctChange_Lag_7", "AGRO3.SA_Media_OHLC_PctChange_Lag_8", "AGRO3.SA_Media_OHLC_PctChange_Lag_9", "AGRO3.SA_Media_OHLC_PctChange_Lag_10", "AGRO3.SA_MM10_PctChange", "AGRO3.SA_Diff_OHLC_MM10", "AGRO3.SA_MM25_PctChange", "AGRO3.SA_Diff_OHLC_MM25", "AGRO3.SA_MM100_PctChange", "AGRO3.SA_Diff_OHLC_MM100", "AGRO3.SA_MM10_Menos_MM25", "AGRO3.SA_MM25_Menos_MM100", "AGRO3.SA_MM10_Menos_MM100", "AGRO3.SA_Volume_Lag_1", "AGRO3.SA_Volume_Lag_2", "AGRO3.SA_Volume_Lag_3", "AGRO3.SA_Volume_Lag_4", "AGRO3.SA_Volume_Lag_5", "AGRO3.SA_Spread_Lag_1", "AGRO3.SA_Spread_Lag_2", "AGRO3.SA_Spread_Lag_3", "AGRO3.SA_Spread_Lag_4", "AGRO3.SA_Spread_Lag_5", "AGRO3.SA_Volatilidade_Lag_1", "AGRO3.SA_Volatilidade_Lag_2", "AGRO3.SA_Volatilidade_Lag_3", "AGRO3.SA_Volatilidade_Lag_4", "AGRO3.SA_Volatilidade_Lag_5", "AGRO3.SA_Parkinson_Volatility_Lag_1", "AGRO3.SA_Parkinson_Volatility_Lag_2", "AGRO3.SA_Parkinson_Volatility_Lag_3", "AGRO3.SA_Parkinson_Volatility_Lag_4", "AGRO3.SA_Parkinson_Volatility_Lag_5", "AGRO3.SA_EMV_Lag_1", "AGRO3.SA_EMV_Lag_2", "AGRO3.SA_EMV_Lag_3", "AGRO3.SA_EMV_Lag_4", "AGRO3.SA_EMV_Lag_5", "AGRO3.SA_EMV_MA_Lag_1", "AGRO3.SA_EMV_MA_Lag_2", "AGRO3.SA_EMV_MA_Lag_3", "AGRO3.SA_EMV_MA_Lag_4", "AGRO3.SA_EMV_MA_Lag_5", "AGRO3.SA_VO_Lag_1", "AGRO3.SA_VO_Lag_2", "AGRO3.SA_VO_Lag_3", "AGRO3.SA_VO_Lag_4", "AGRO3.SA_VO_Lag_5", "AGRO3.SA_High_Max_50_Lag_1", "AGRO3.SA_High_Max_50_Lag_2", "AGRO3.SA_High_Max_50_Lag_3", "AGRO3.SA_High_Max_50_Lag_4", "AGRO3.SA_High_Max_50_Lag_5", "AGRO3.SA_Low_Min_50_Lag_1", "AGRO3.SA_Low_Min_50_Lag_2", "AGRO3.SA_Low_Min_50_Lag_3", "AGRO3.SA_Low_Min_50_Lag_4", "AGRO3.SA_Low_Min_50_Lag_5", "AGRO3.SA_MFI_Lag_1", "AGRO3.SA_MFI_Lag_2", "AGRO3.SA_MFI_Lag_3", "AGRO3.SA_MFI_Lag_4", "AGRO3.SA_MFI_Lag_5", "AGRO3.SA_Amihud_Lag_1", "AGRO3.SA_Amihud_Lag_2", "AGRO3.SA_Amihud_Lag_3", "AGRO3.SA_Amihud_Lag_4", "AGRO3.SA_Amihud_Lag_5", "AGRO3.SA_Roll_Spread_Lag_1", "AGRO3.SA_Roll_Spread_Lag_2", "AGRO3.SA_Roll_Spread_Lag_3", "AGRO3.SA_Roll_Spread_Lag_4", "AGRO3.SA_Roll_Spread_Lag_5", "AGRO3.SA_Hurst_Lag_1", "AGRO3.SA_Hurst_Lag_2", "AGRO3.SA_Hurst_Lag_3", "AGRO3.SA_<PERSON>rst_Lag_4", "AGRO3.SA_<PERSON>rst_Lag_5", "AGRO3.SA_Vol_per_Volume_Lag_1", "AGRO3.SA_Vol_per_Volume_Lag_2", "AGRO3.SA_Vol_per_Volume_Lag_3", "AGRO3.SA_Vol_per_Volume_Lag_4", "AGRO3.SA_Vol_per_Volume_Lag_5", "AGRO3.SA_CMF_Lag_1", "AGRO3.SA_CMF_Lag_2", "AGRO3.SA_CMF_Lag_3", "AGRO3.SA_CMF_Lag_4", "AGRO3.SA_CMF_Lag_5", "AGRO3.SA_AD_Line_Lag_1", "AGRO3.SA_AD_Line_Lag_2", "AGRO3.SA_AD_Line_Lag_3", "AGRO3.SA_AD_Line_Lag_4", "AGRO3.SA_AD_Line_Lag_5", "CXSE3.SA_Close", "CXSE3.SA_High", "CXSE3.SA_Low", "CXSE3.SA_Open", "CXSE3.SA_Volume", "CXSE3.SA_Media_OHLC", "CXSE3.SA_MM10", "CXSE3.SA_MM25", "CXSE3.SA_MM100", "CXSE3.SA_Media_OHLC_PctChange", "CXSE3.SA_Volatilidade", "CXSE3.SA_Spread", "CXSE3.SA_Parkinson_Volatility", "CXSE3.SA_MFI", "CXSE3.SA_MFI_Historical", "CXSE3.SA_EMV", "CXSE3.SA_EMV_MA", "CXSE3.SA_Amihud", "CXSE3.SA_Amihud_Historical", "CXSE3.SA_Roll_Spread", "CXSE3.SA_Roll_Spread_Historical", "CXSE3.SA_Hurst", "CXSE3.<PERSON>_<PERSON><PERSON>_Historical", "CXSE3.SA_Vol_per_Volume", "CXSE3.SA_Vol_per_Volume_Historical", "CXSE3.SA_CMF", "CXSE3.SA_CMF_Historical", "CXSE3.SA_AD_Line", "CXSE3.SA_AD_Line_Historical", "CXSE3.SA_VO", "CXSE3.SA_High_Max_50", "CXSE3.SA_Low_Min_50", "CXSE3.SA_Segunda", "CXSE3.SA_Terca", "CXSE3.SA_Quarta", "CXSE3.SA_Quinta", "CXSE3.SA_Sexta", "CXSE3.SA_Mes_1", "CXSE3.SA_Mes_2", "CXSE3.SA_Mes_3", "CXSE3.SA_Mes_4", "CXSE3.SA_Mes_5", "CXSE3.SA_Mes_6", "CXSE3.SA_Mes_7", "CXSE3.SA_Mes_8", "CXSE3.SA_Mes_9", "CXSE3.SA_Mes_10", "CXSE3.SA_Mes_11", "CXSE3.SA_Mes_12", "CXSE3.SA_Quarter_1", "CXSE3.SA_Quarter_2", "CXSE3.SA_Quarter_3", "CXSE3.SA_Quarter_4", "CXSE3.SA_Last_Day_Quarter", "CXSE3.SA_Pre_Feriado_Brasil", "CXSE3.SA_Media_OHLC_Anterior", "CXSE3.SA_Sinal_Compra", "CXSE3.SA_Sinal_Venda", "CXSE3.SA_Media_OHLC_Futura", "CXSE3.SA_Media_OHLC_PctChange_Lag_1", "CXSE3.SA_Media_OHLC_PctChange_Lag_2", "CXSE3.SA_Media_OHLC_PctChange_Lag_3", "CXSE3.SA_Media_OHLC_PctChange_Lag_4", "CXSE3.SA_Media_OHLC_PctChange_Lag_5", "CXSE3.SA_Media_OHLC_PctChange_Lag_6", "CXSE3.SA_Media_OHLC_PctChange_Lag_7", "CXSE3.SA_Media_OHLC_PctChange_Lag_8", "CXSE3.SA_Media_OHLC_PctChange_Lag_9", "CXSE3.SA_Media_OHLC_PctChange_Lag_10", "CXSE3.SA_MM10_PctChange", "CXSE3.SA_Diff_OHLC_MM10", "CXSE3.SA_MM25_PctChange", "CXSE3.SA_Diff_OHLC_MM25", "CXSE3.SA_MM100_PctChange", "CXSE3.SA_Diff_OHLC_MM100", "CXSE3.SA_MM10_Menos_MM25", "CXSE3.SA_MM25_Menos_MM100", "CXSE3.SA_MM10_Menos_MM100", "CXSE3.SA_Volume_Lag_1", "CXSE3.SA_Volume_Lag_2", "CXSE3.SA_Volume_Lag_3", "CXSE3.SA_Volume_Lag_4", "CXSE3.SA_Volume_Lag_5", "CXSE3.SA_Spread_Lag_1", "CXSE3.SA_Spread_Lag_2", "CXSE3.SA_Spread_Lag_3", "CXSE3.SA_Spread_Lag_4", "CXSE3.SA_Spread_Lag_5", "CXSE3.SA_Volatilidade_Lag_1", "CXSE3.SA_Volatilidade_Lag_2", "CXSE3.SA_Volatilidade_Lag_3", "CXSE3.SA_Volatilidade_Lag_4", "CXSE3.SA_Volatilidade_Lag_5", "CXSE3.SA_Parkinson_Volatility_Lag_1", "CXSE3.SA_Parkinson_Volatility_Lag_2", "CXSE3.SA_Parkinson_Volatility_Lag_3", "CXSE3.SA_Parkinson_Volatility_Lag_4", "CXSE3.<PERSON>_Parkinson_Volatility_Lag_5", "CXSE3.SA_EMV_Lag_1", "CXSE3.SA_EMV_Lag_2", "CXSE3.SA_EMV_Lag_3", "CXSE3.SA_EMV_Lag_4", "CXSE3.SA_EMV_Lag_5", "CXSE3.SA_EMV_MA_Lag_1", "CXSE3.SA_EMV_MA_Lag_2", "CXSE3.SA_EMV_MA_Lag_3", "CXSE3.SA_EMV_MA_Lag_4", "CXSE3.SA_EMV_MA_Lag_5", "CXSE3.SA_VO_Lag_1", "CXSE3.SA_VO_Lag_2", "CXSE3.SA_VO_Lag_3", "CXSE3.SA_VO_Lag_4", "CXSE3.SA_VO_Lag_5", "CXSE3.SA_High_Max_50_Lag_1", "CXSE3.SA_High_Max_50_Lag_2", "CXSE3.SA_High_Max_50_Lag_3", "CXSE3.SA_High_Max_50_Lag_4", "CXSE3.SA_High_Max_50_Lag_5", "CXSE3.SA_Low_Min_50_Lag_1", "CXSE3.SA_Low_Min_50_Lag_2", "CXSE3.SA_Low_Min_50_Lag_3", "CXSE3.SA_Low_Min_50_Lag_4", "CXSE3.SA_Low_Min_50_Lag_5", "CXSE3.SA_MFI_Lag_1", "CXSE3.SA_MFI_Lag_2", "CXSE3.SA_MFI_Lag_3", "CXSE3.SA_MFI_Lag_4", "CXSE3.SA_MFI_Lag_5", "CXSE3.SA_Amihud_Lag_1", "CXSE3.SA_Amihud_Lag_2", "CXSE3.SA_Amihud_Lag_3", "CXSE3.SA_Amihud_Lag_4", "CXSE3.SA_Amihud_Lag_5", "CXSE3.SA_Roll_Spread_Lag_1", "CXSE3.SA_Roll_Spread_Lag_2", "CXSE3.SA_Roll_Spread_Lag_3", "CXSE3.SA_Roll_Spread_Lag_4", "CXSE3.SA_Roll_Spread_Lag_5", "CXSE3.SA_Hurst_Lag_1", "CXSE3.<PERSON>_Hurst_Lag_2", "CXSE3.<PERSON>_Hurst_Lag_3", "CXSE3.<PERSON>_Hu<PERSON>_Lag_4", "CXSE3.<PERSON>_<PERSON><PERSON>_Lag_5", "CXSE3.SA_Vol_per_Volume_Lag_1", "CXSE3.SA_Vol_per_Volume_Lag_2", "CXSE3.SA_Vol_per_Volume_Lag_3", "CXSE3.SA_Vol_per_Volume_Lag_4", "CXSE3.SA_Vol_per_Volume_Lag_5", "CXSE3.SA_CMF_Lag_1", "CXSE3.SA_CMF_Lag_2", "CXSE3.SA_CMF_Lag_3", "CXSE3.SA_CMF_Lag_4", "CXSE3.SA_CMF_Lag_5", "CXSE3.SA_AD_Line_Lag_1", "CXSE3.SA_AD_Line_Lag_2", "CXSE3.SA_AD_Line_Lag_3", "CXSE3.SA_AD_Line_Lag_4", "CXSE3.SA_AD_Line_Lag_5", "CMIN3.SA_Close", "CMIN3.SA_High", "CMIN3.SA_Low", "CMIN3.SA_Open", "CMIN3.SA_Volume", "CMIN3.SA_Media_OHLC", "CMIN3.SA_MM10", "CMIN3.SA_MM25", "CMIN3.SA_MM100", "CMIN3.SA_Media_OHLC_PctChange", "CMIN3.SA_Volatilidade", "CMIN3.SA_Spread", "CMIN3.SA_Parkinson_Volatility", "CMIN3.SA_MFI", "CMIN3.SA_MFI_Historical", "CMIN3.SA_EMV", "CMIN3.SA_EMV_MA", "CMIN3.SA_Amihud", "CMIN3.SA_Amihud_Historical", "CMIN3.SA_Roll_Spread", "CMIN3.SA_Roll_Spread_Historical", "CMIN3.SA_Hurst", "CMIN3.<PERSON>_<PERSON><PERSON>_Historical", "CMIN3.SA_Vol_per_Volume", "CMIN3.SA_Vol_per_Volume_Historical", "CMIN3.SA_CMF", "CMIN3.SA_CMF_Historical", "CMIN3.SA_AD_Line", "CMIN3.SA_AD_Line_Historical", "CMIN3.SA_VO", "CMIN3.SA_High_Max_50", "CMIN3.SA_Low_Min_50", "CMIN3.SA_Segunda", "CMIN3.SA_Terca", "CMIN3.SA_Quarta", "CMIN3.SA_Quinta", "CMIN3.SA_Sexta", "CMIN3.SA_Mes_1", "CMIN3.SA_Mes_2", "CMIN3.SA_Mes_3", "CMIN3.SA_Mes_4", "CMIN3.SA_Mes_5", "CMIN3.SA_Mes_6", "CMIN3.SA_Mes_7", "CMIN3.SA_Mes_8", "CMIN3.SA_Mes_9", "CMIN3.SA_Mes_10", "CMIN3.SA_Mes_11", "CMIN3.SA_Mes_12", "CMIN3.SA_Quarter_1", "CMIN3.SA_Quarter_2", "CMIN3.SA_Quarter_3", "CMIN3.SA_Quarter_4", "CMIN3.SA_Last_Day_Quarter", "CMIN3.SA_Pre_Feriado_Brasil", "CMIN3.SA_Media_OHLC_Anterior", "CMIN3.SA_Sinal_Compra", "CMIN3.SA_Sinal_Venda", "CMIN3.SA_Media_OHLC_Futura", "CMIN3.SA_Media_OHLC_PctChange_Lag_1", "CMIN3.SA_Media_OHLC_PctChange_Lag_2", "CMIN3.SA_Media_OHLC_PctChange_Lag_3", "CMIN3.SA_Media_OHLC_PctChange_Lag_4", "CMIN3.SA_Media_OHLC_PctChange_Lag_5", "CMIN3.SA_Media_OHLC_PctChange_Lag_6", "CMIN3.SA_Media_OHLC_PctChange_Lag_7", "CMIN3.SA_Media_OHLC_PctChange_Lag_8", "CMIN3.SA_Media_OHLC_PctChange_Lag_9", "CMIN3.SA_Media_OHLC_PctChange_Lag_10", "CMIN3.SA_MM10_PctChange", "CMIN3.SA_Diff_OHLC_MM10", "CMIN3.SA_MM25_PctChange", "CMIN3.SA_Diff_OHLC_MM25", "CMIN3.SA_MM100_PctChange", "CMIN3.SA_Diff_OHLC_MM100", "CMIN3.SA_MM10_Menos_MM25", "CMIN3.SA_MM25_Menos_MM100", "CMIN3.SA_MM10_Menos_MM100", "CMIN3.SA_Volume_Lag_1", "CMIN3.SA_Volume_Lag_2", "CMIN3.SA_Volume_Lag_3", "CMIN3.SA_Volume_Lag_4", "CMIN3.SA_Volume_Lag_5", "CMIN3.SA_Spread_Lag_1", "CMIN3.SA_Spread_Lag_2", "CMIN3.SA_Spread_Lag_3", "CMIN3.SA_Spread_Lag_4", "CMIN3.SA_Spread_Lag_5", "CMIN3.SA_Volatilidade_Lag_1", "CMIN3.SA_Volatilidade_Lag_2", "CMIN3.SA_Volatilidade_Lag_3", "CMIN3.SA_Volatilidade_Lag_4", "CMIN3.SA_Volatilidade_Lag_5", "CMIN3.<PERSON>_Parkinson_Volatility_Lag_1", "CMIN3.<PERSON>_Parkinson_Volatility_Lag_2", "CMIN3.<PERSON>_Parkinson_Volatility_Lag_3", "CMIN3.<PERSON>_Parkinson_Volatility_Lag_4", "CMIN3.<PERSON>_Parkinson_Volatility_Lag_5", "CMIN3.SA_EMV_Lag_1", "CMIN3.SA_EMV_Lag_2", "CMIN3.SA_EMV_Lag_3", "CMIN3.SA_EMV_Lag_4", "CMIN3.SA_EMV_Lag_5", "CMIN3.SA_EMV_MA_Lag_1", "CMIN3.SA_EMV_MA_Lag_2", "CMIN3.SA_EMV_MA_Lag_3", "CMIN3.SA_EMV_MA_Lag_4", "CMIN3.SA_EMV_MA_Lag_5", "CMIN3.SA_VO_Lag_1", "CMIN3.SA_VO_Lag_2", "CMIN3.SA_VO_Lag_3", "CMIN3.SA_VO_Lag_4", "CMIN3.SA_VO_Lag_5", "CMIN3.SA_High_Max_50_Lag_1", "CMIN3.SA_High_Max_50_Lag_2", "CMIN3.SA_High_Max_50_Lag_3", "CMIN3.SA_High_Max_50_Lag_4", "CMIN3.SA_High_Max_50_Lag_5", "CMIN3.SA_Low_Min_50_Lag_1", "CMIN3.SA_Low_Min_50_Lag_2", "CMIN3.SA_Low_Min_50_Lag_3", "CMIN3.SA_Low_Min_50_Lag_4", "CMIN3.SA_Low_Min_50_Lag_5", "CMIN3.SA_MFI_Lag_1", "CMIN3.SA_MFI_Lag_2", "CMIN3.SA_MFI_Lag_3", "CMIN3.SA_MFI_Lag_4", "CMIN3.SA_MFI_Lag_5", "CMIN3.SA_Amihud_Lag_1", "CMIN3.SA_Amihud_Lag_2", "CMIN3.SA_Amihud_Lag_3", "CMIN3.SA_Amihud_Lag_4", "CMIN3.SA_Amihud_Lag_5", "CMIN3.SA_Roll_Spread_Lag_1", "CMIN3.SA_Roll_Spread_Lag_2", "CMIN3.SA_Roll_Spread_Lag_3", "CMIN3.SA_Roll_Spread_Lag_4", "CMIN3.SA_Roll_Spread_Lag_5", "CMIN3.<PERSON>_Hu<PERSON>_Lag_1", "CMIN3.<PERSON>_Hu<PERSON>_Lag_2", "CMIN3.<PERSON>_<PERSON><PERSON>_Lag_3", "CMIN3.<PERSON>_<PERSON><PERSON>_Lag_4", "CMIN3.<PERSON>_<PERSON><PERSON>_Lag_5", "CMIN3.SA_Vol_per_Volume_Lag_1", "CMIN3.SA_Vol_per_Volume_Lag_2", "CMIN3.SA_Vol_per_Volume_Lag_3", "CMIN3.SA_Vol_per_Volume_Lag_4", "CMIN3.SA_Vol_per_Volume_Lag_5", "CMIN3.SA_CMF_Lag_1", "CMIN3.SA_CMF_Lag_2", "CMIN3.SA_CMF_Lag_3", "CMIN3.SA_CMF_Lag_4", "CMIN3.SA_CMF_Lag_5", "CMIN3.SA_AD_Line_Lag_1", "CMIN3.SA_AD_Line_Lag_2", "CMIN3.SA_AD_Line_Lag_3", "CMIN3.SA_AD_Line_Lag_4", "CMIN3.SA_AD_Line_Lag_5", "TUPY3.SA_Open", "TUPY3.SA_High", "TUPY3.SA_Low", "TUPY3.SA_Close", "TUPY3.SA_Volume", "TUPY3.SA_Media_OHLC", "TUPY3.SA_MM10", "TUPY3.SA_MM25", "TUPY3.SA_MM100", "TUPY3.SA_Media_OHLC_PctChange", "TUPY3.SA_Volatilidade", "TUPY3.SA_Spread", "TUPY3.SA_Parkinson_Volatility", "TUPY3.SA_MFI", "TUPY3.SA_MFI_Historical", "TUPY3.SA_EMV", "TUPY3.SA_EMV_MA", "TUPY3.SA_Amihud", "TUPY3.SA_Amihud_Historical", "TUPY3.SA_Roll_Spread", "TUPY3.SA_Roll_Spread_Historical", "TUPY3.SA_Hurst", "TUPY3.<PERSON>_<PERSON><PERSON>_Historical", "TUPY3.SA_Vol_per_Volume", "TUPY3.SA_Vol_per_Volume_Historical", "TUPY3.SA_CMF", "TUPY3.SA_CMF_Historical", "TUPY3.SA_AD_Line", "TUPY3.SA_AD_Line_Historical", "TUPY3.SA_VO", "TUPY3.SA_High_Max_50", "TUPY3.SA_Low_Min_50", "TUPY3.SA_Segunda", "TUPY3.SA_Terca", "TUPY3.SA_Quarta", "TUPY3.SA_Quinta", "TUPY3.SA_Sexta", "TUPY3.SA_Mes_1", "TUPY3.SA_Mes_2", "TUPY3.SA_Mes_3", "TUPY3.SA_Mes_4", "TUPY3.SA_Mes_5", "TUPY3.SA_Mes_6", "TUPY3.SA_Mes_7", "TUPY3.SA_Mes_8", "TUPY3.SA_Mes_9", "TUPY3.SA_Mes_10", "TUPY3.SA_Mes_11", "TUPY3.SA_Mes_12", "TUPY3.SA_Quarter_1", "TUPY3.SA_Quarter_2", "TUPY3.SA_Quarter_3", "TUPY3.SA_Quarter_4", "TUPY3.SA_Last_Day_Quarter", "TUPY3.SA_Pre_Feriado_Brasil", "TUPY3.SA_Media_OHLC_Anterior", "TUPY3.SA_Sinal_Compra", "TUPY3.SA_Sinal_Venda", "TUPY3.SA_Media_OHLC_Futura", "TUPY3.SA_Media_OHLC_PctChange_Lag_1", "TUPY3.SA_Media_OHLC_PctChange_Lag_2", "TUPY3.SA_Media_OHLC_PctChange_Lag_3", "TUPY3.SA_Media_OHLC_PctChange_Lag_4", "TUPY3.SA_Media_OHLC_PctChange_Lag_5", "TUPY3.SA_Media_OHLC_PctChange_Lag_6", "TUPY3.SA_Media_OHLC_PctChange_Lag_7", "TUPY3.SA_Media_OHLC_PctChange_Lag_8", "TUPY3.SA_Media_OHLC_PctChange_Lag_9", "TUPY3.SA_Media_OHLC_PctChange_Lag_10", "TUPY3.SA_MM10_PctChange", "TUPY3.SA_Diff_OHLC_MM10", "TUPY3.SA_MM25_PctChange", "TUPY3.SA_Diff_OHLC_MM25", "TUPY3.SA_MM100_PctChange", "TUPY3.SA_Diff_OHLC_MM100", "TUPY3.SA_MM10_Menos_MM25", "TUPY3.SA_MM25_Menos_MM100", "TUPY3.SA_MM10_Menos_MM100", "TUPY3.SA_Volume_Lag_1", "TUPY3.SA_Volume_Lag_2", "TUPY3.SA_Volume_Lag_3", "TUPY3.SA_Volume_Lag_4", "TUPY3.SA_Volume_Lag_5", "TUPY3.SA_Spread_Lag_1", "TUPY3.SA_Spread_Lag_2", "TUPY3.SA_Spread_Lag_3", "TUPY3.SA_Spread_Lag_4", "TUPY3.SA_Spread_Lag_5", "TUPY3.SA_Volatilidade_Lag_1", "TUPY3.SA_Volatilidade_Lag_2", "TUPY3.SA_Volatilidade_Lag_3", "TUPY3.SA_Volatilidade_Lag_4", "TUPY3.SA_Volatilidade_Lag_5", "TUPY3.SA_Parkinson_Volatility_Lag_1", "TUPY3.<PERSON>_Parkinson_Volatility_Lag_2", "TUPY3.SA_Parkinson_Volatility_Lag_3", "TUPY3.SA_Parkinson_Volatility_Lag_4", "TUPY3.<PERSON>_Parkinson_Volatility_Lag_5", "TUPY3.SA_EMV_Lag_1", "TUPY3.SA_EMV_Lag_2", "TUPY3.SA_EMV_Lag_3", "TUPY3.SA_EMV_Lag_4", "TUPY3.SA_EMV_Lag_5", "TUPY3.SA_EMV_MA_Lag_1", "TUPY3.SA_EMV_MA_Lag_2", "TUPY3.SA_EMV_MA_Lag_3", "TUPY3.SA_EMV_MA_Lag_4", "TUPY3.SA_EMV_MA_Lag_5", "TUPY3.SA_VO_Lag_1", "TUPY3.SA_VO_Lag_2", "TUPY3.SA_VO_Lag_3", "TUPY3.SA_VO_Lag_4", "TUPY3.SA_VO_Lag_5", "TUPY3.SA_High_Max_50_Lag_1", "TUPY3.SA_High_Max_50_Lag_2", "TUPY3.SA_High_Max_50_Lag_3", "TUPY3.SA_High_Max_50_Lag_4", "TUPY3.SA_High_Max_50_Lag_5", "TUPY3.SA_Low_Min_50_Lag_1", "TUPY3.SA_Low_Min_50_Lag_2", "TUPY3.SA_Low_Min_50_Lag_3", "TUPY3.SA_Low_Min_50_Lag_4", "TUPY3.SA_Low_Min_50_Lag_5", "TUPY3.SA_MFI_Lag_1", "TUPY3.SA_MFI_Lag_2", "TUPY3.SA_MFI_Lag_3", "TUPY3.SA_MFI_Lag_4", "TUPY3.SA_MFI_Lag_5", "TUPY3.SA_Amihud_Lag_1", "TUPY3.SA_Amihud_Lag_2", "TUPY3.SA_Amihud_Lag_3", "TUPY3.SA_Amihud_Lag_4", "TUPY3.SA_Amihud_Lag_5", "TUPY3.SA_Roll_Spread_Lag_1", "TUPY3.SA_Roll_Spread_Lag_2", "TUPY3.SA_Roll_Spread_Lag_3", "TUPY3.SA_Roll_Spread_Lag_4", "TUPY3.SA_Roll_Spread_Lag_5", "TUPY3.SA_Hurst_Lag_1", "TUPY3.<PERSON>_Hu<PERSON>_Lag_2", "TUPY3.<PERSON>_Hurst_Lag_3", "TUPY3.<PERSON>_<PERSON><PERSON>_Lag_4", "TUPY3.<PERSON>_<PERSON><PERSON>_Lag_5", "TUPY3.SA_Vol_per_Volume_Lag_1", "TUPY3.SA_Vol_per_Volume_Lag_2", "TUPY3.SA_Vol_per_Volume_Lag_3", "TUPY3.SA_Vol_per_Volume_Lag_4", "TUPY3.SA_Vol_per_Volume_Lag_5", "TUPY3.SA_CMF_Lag_1", "TUPY3.SA_CMF_Lag_2", "TUPY3.SA_CMF_Lag_3", "TUPY3.SA_CMF_Lag_4", "TUPY3.SA_CMF_Lag_5", "TUPY3.SA_AD_Line_Lag_1", "TUPY3.SA_AD_Line_Lag_2", "TUPY3.SA_AD_Line_Lag_3", "TUPY3.SA_AD_Line_Lag_4", "TUPY3.SA_AD_Line_Lag_5", "TTEN3.SA_Close", "TTEN3.SA_High", "TTEN3.SA_Low", "TTEN3.SA_Open", "TTEN3.SA_Volume", "TTEN3.SA_Media_OHLC", "TTEN3.SA_MM10", "TTEN3.SA_MM25", "TTEN3.SA_MM100", "TTEN3.SA_Media_OHLC_PctChange", "TTEN3.SA_Volatilidade", "TTEN3.SA_Spread", "TTEN3.SA_Parkinson_Volatility", "TTEN3.SA_MFI", "TTEN3.SA_MFI_Historical", "TTEN3.SA_EMV", "TTEN3.SA_EMV_MA", "TTEN3.SA_Amihud", "TTEN3.SA_Amihud_Historical", "TTEN3.SA_Roll_Spread", "TTEN3.SA_Roll_Spread_Historical", "TTEN3.SA_Hurst", "TTEN3.SA_<PERSON><PERSON>_Historical", "TTEN3.SA_Vol_per_Volume", "TTEN3.SA_Vol_per_Volume_Historical", "TTEN3.SA_CMF", "TTEN3.SA_CMF_Historical", "TTEN3.SA_AD_Line", "TTEN3.SA_AD_Line_Historical", "TTEN3.SA_VO", "TTEN3.SA_High_Max_50", "TTEN3.SA_Low_Min_50", "TTEN3.SA_Segunda", "TTEN3.SA_Terca", "TTEN3.SA_Quarta", "TTEN3.SA_Quinta", "TTEN3.SA_Sexta", "TTEN3.SA_Mes_1", "TTEN3.SA_Mes_2", "TTEN3.SA_Mes_3", "TTEN3.SA_Mes_4", "TTEN3.SA_Mes_5", "TTEN3.SA_Mes_6", "TTEN3.SA_Mes_7", "TTEN3.SA_Mes_8", "TTEN3.SA_Mes_9", "TTEN3.SA_Mes_10", "TTEN3.SA_Mes_11", "TTEN3.SA_Mes_12", "TTEN3.SA_Quarter_1", "TTEN3.SA_Quarter_2", "TTEN3.SA_Quarter_3", "TTEN3.SA_Quarter_4", "TTEN3.SA_Last_Day_Quarter", "TTEN3.SA_Pre_Feriado_Brasil", "TTEN3.SA_Media_OHLC_Anterior", "TTEN3.SA_Sinal_Compra", "TTEN3.SA_Sinal_Venda", "TTEN3.SA_Media_OHLC_Futura", "TTEN3.SA_Media_OHLC_PctChange_Lag_1", "TTEN3.SA_Media_OHLC_PctChange_Lag_2", "TTEN3.SA_Media_OHLC_PctChange_Lag_3", "TTEN3.SA_Media_OHLC_PctChange_Lag_4", "TTEN3.SA_Media_OHLC_PctChange_Lag_5", "TTEN3.SA_Media_OHLC_PctChange_Lag_6", "TTEN3.SA_Media_OHLC_PctChange_Lag_7", "TTEN3.SA_Media_OHLC_PctChange_Lag_8", "TTEN3.SA_Media_OHLC_PctChange_Lag_9", "TTEN3.SA_Media_OHLC_PctChange_Lag_10", "TTEN3.SA_MM10_PctChange", "TTEN3.SA_Diff_OHLC_MM10", "TTEN3.SA_MM25_PctChange", "TTEN3.SA_Diff_OHLC_MM25", "TTEN3.SA_MM100_PctChange", "TTEN3.SA_Diff_OHLC_MM100", "TTEN3.SA_MM10_Menos_MM25", "TTEN3.SA_MM25_Menos_MM100", "TTEN3.SA_MM10_Menos_MM100", "TTEN3.SA_Volume_Lag_1", "TTEN3.SA_Volume_Lag_2", "TTEN3.SA_Volume_Lag_3", "TTEN3.SA_Volume_Lag_4", "TTEN3.SA_Volume_Lag_5", "TTEN3.SA_Spread_Lag_1", "TTEN3.SA_Spread_Lag_2", "TTEN3.SA_Spread_Lag_3", "TTEN3.SA_Spread_Lag_4", "TTEN3.SA_Spread_Lag_5", "TTEN3.SA_Volatilidade_Lag_1", "TTEN3.SA_Volatilidade_Lag_2", "TTEN3.SA_Volatilidade_Lag_3", "TTEN3.SA_Volatilidade_Lag_4", "TTEN3.SA_Volatilidade_Lag_5", "TTEN3.SA_Parkinson_Volatility_Lag_1", "TTEN3.SA_Parkinson_Volatility_Lag_2", "TTEN3.SA_Parkinson_Volatility_Lag_3", "TTEN3.SA_Parkinson_Volatility_Lag_4", "TTEN3.SA_Parkinson_Volatility_Lag_5", "TTEN3.SA_EMV_Lag_1", "TTEN3.SA_EMV_Lag_2", "TTEN3.SA_EMV_Lag_3", "TTEN3.SA_EMV_Lag_4", "TTEN3.SA_EMV_Lag_5", "TTEN3.SA_EMV_MA_Lag_1", "TTEN3.SA_EMV_MA_Lag_2", "TTEN3.SA_EMV_MA_Lag_3", "TTEN3.SA_EMV_MA_Lag_4", "TTEN3.SA_EMV_MA_Lag_5", "TTEN3.SA_VO_Lag_1", "TTEN3.SA_VO_Lag_2", "TTEN3.SA_VO_Lag_3", "TTEN3.SA_VO_Lag_4", "TTEN3.SA_VO_Lag_5", "TTEN3.SA_High_Max_50_Lag_1", "TTEN3.SA_High_Max_50_Lag_2", "TTEN3.SA_High_Max_50_Lag_3", "TTEN3.SA_High_Max_50_Lag_4", "TTEN3.SA_High_Max_50_Lag_5", "TTEN3.SA_Low_Min_50_Lag_1", "TTEN3.SA_Low_Min_50_Lag_2", "TTEN3.SA_Low_Min_50_Lag_3", "TTEN3.SA_Low_Min_50_Lag_4", "TTEN3.SA_Low_Min_50_Lag_5", "TTEN3.SA_MFI_Lag_1", "TTEN3.SA_MFI_Lag_2", "TTEN3.SA_MFI_Lag_3", "TTEN3.SA_MFI_Lag_4", "TTEN3.SA_MFI_Lag_5", "TTEN3.SA_Amihud_Lag_1", "TTEN3.SA_Amihud_Lag_2", "TTEN3.SA_Amihud_Lag_3", "TTEN3.SA_Amihud_Lag_4", "TTEN3.SA_Amihud_Lag_5", "TTEN3.SA_Roll_Spread_Lag_1", "TTEN3.SA_Roll_Spread_Lag_2", "TTEN3.SA_Roll_Spread_Lag_3", "TTEN3.SA_Roll_Spread_Lag_4", "TTEN3.SA_Roll_Spread_Lag_5", "TTEN3.SA_Hurst_Lag_1", "TTEN3.SA_Hurst_Lag_2", "TTEN3.SA_Hurst_Lag_3", "TTEN3.<PERSON>_Hu<PERSON>_Lag_4", "TTEN3.<PERSON>_<PERSON><PERSON>_Lag_5", "TTEN3.SA_Vol_per_Volume_Lag_1", "TTEN3.SA_Vol_per_Volume_Lag_2", "TTEN3.SA_Vol_per_Volume_Lag_3", "TTEN3.SA_Vol_per_Volume_Lag_4", "TTEN3.SA_Vol_per_Volume_Lag_5", "TTEN3.SA_CMF_Lag_1", "TTEN3.SA_CMF_Lag_2", "TTEN3.SA_CMF_Lag_3", "TTEN3.SA_CMF_Lag_4", "TTEN3.SA_CMF_Lag_5", "TTEN3.SA_AD_Line_Lag_1", "TTEN3.SA_AD_Line_Lag_2", "TTEN3.SA_AD_Line_Lag_3", "TTEN3.SA_AD_Line_Lag_4", "TTEN3.SA_AD_Line_Lag_5", "MDIA3.SA_Open", "MDIA3.SA_High", "MDIA3.SA_Low", "MDIA3.SA_Close", "MDIA3.SA_Volume", "MDIA3.SA_Media_OHLC", "MDIA3.SA_MM10", "MDIA3.SA_MM25", "MDIA3.SA_MM100", "MDIA3.SA_Media_OHLC_PctChange", "MDIA3.SA_Volatilidade", "MDIA3.SA_Spread", "MDIA3.SA_Parkinson_Volatility", "MDIA3.SA_MFI", "MDIA3.SA_MFI_Historical", "MDIA3.SA_EMV", "MDIA3.SA_EMV_MA", "MDIA3.SA_Amihud", "MDIA3.SA_Amihud_Historical", "MDIA3.SA_Roll_Spread", "MDIA3.SA_Roll_Spread_Historical", "MDIA3.SA_Hurst", "MDIA3.SA_<PERSON><PERSON>_Historical", "MDIA3.SA_Vol_per_Volume", "MDIA3.SA_Vol_per_Volume_Historical", "MDIA3.SA_CMF", "MDIA3.SA_CMF_Historical", "MDIA3.SA_AD_Line", "MDIA3.SA_AD_Line_Historical", "MDIA3.SA_VO", "MDIA3.SA_High_Max_50", "MDIA3.SA_Low_Min_50", "MDIA3.SA_Segunda", "MDIA3.SA_Terca", "MDIA3.SA_Quarta", "MDIA3.SA_Quinta", "MDIA3.SA_Sexta", "MDIA3.SA_Mes_1", "MDIA3.SA_Mes_2", "MDIA3.SA_Mes_3", "MDIA3.SA_Mes_4", "MDIA3.SA_Mes_5", "MDIA3.SA_Mes_6", "MDIA3.SA_Mes_7", "MDIA3.SA_Mes_8", "MDIA3.SA_Mes_9", "MDIA3.SA_Mes_10", "MDIA3.SA_Mes_11", "MDIA3.SA_Mes_12", "MDIA3.SA_Quarter_1", "MDIA3.SA_Quarter_2", "MDIA3.SA_Quarter_3", "MDIA3.SA_Quarter_4", "MDIA3.SA_Last_Day_Quarter", "MDIA3.SA_Pre_Feriado_Brasil", "MDIA3.SA_Media_OHLC_Anterior", "MDIA3.SA_Sinal_Compra", "MDIA3.SA_Sinal_Venda", "MDIA3.SA_Media_OHLC_Futura", "MDIA3.SA_Media_OHLC_PctChange_Lag_1", "MDIA3.SA_Media_OHLC_PctChange_Lag_2", "MDIA3.SA_Media_OHLC_PctChange_Lag_3", "MDIA3.SA_Media_OHLC_PctChange_Lag_4", "MDIA3.SA_Media_OHLC_PctChange_Lag_5", "MDIA3.SA_Media_OHLC_PctChange_Lag_6", "MDIA3.SA_Media_OHLC_PctChange_Lag_7", "MDIA3.SA_Media_OHLC_PctChange_Lag_8", "MDIA3.SA_Media_OHLC_PctChange_Lag_9", "MDIA3.SA_Media_OHLC_PctChange_Lag_10", "MDIA3.SA_MM10_PctChange", "MDIA3.SA_Diff_OHLC_MM10", "MDIA3.SA_MM25_PctChange", "MDIA3.SA_Diff_OHLC_MM25", "MDIA3.SA_MM100_PctChange", "MDIA3.SA_Diff_OHLC_MM100", "MDIA3.SA_MM10_Menos_MM25", "MDIA3.SA_MM25_Menos_MM100", "MDIA3.SA_MM10_Menos_MM100", "MDIA3.SA_Volume_Lag_1", "MDIA3.SA_Volume_Lag_2", "MDIA3.SA_Volume_Lag_3", "MDIA3.SA_Volume_Lag_4", "MDIA3.SA_Volume_Lag_5", "MDIA3.SA_Spread_Lag_1", "MDIA3.SA_Spread_Lag_2", "MDIA3.SA_Spread_Lag_3", "MDIA3.SA_Spread_Lag_4", "MDIA3.SA_Spread_Lag_5", "MDIA3.SA_Volatilidade_Lag_1", "MDIA3.SA_Volatilidade_Lag_2", "MDIA3.SA_Volatilidade_Lag_3", "MDIA3.SA_Volatilidade_Lag_4", "MDIA3.SA_Volatilidade_Lag_5", "MDIA3.SA_Parkinson_Volatility_Lag_1", "MDIA3.SA_Parkinson_Volatility_Lag_2", "MDIA3.SA_Parkinson_Volatility_Lag_3", "MDIA3.SA_Parkinson_Volatility_Lag_4", "MDIA3.SA_Parkinson_Volatility_Lag_5", "MDIA3.SA_EMV_Lag_1", "MDIA3.SA_EMV_Lag_2", "MDIA3.SA_EMV_Lag_3", "MDIA3.SA_EMV_Lag_4", "MDIA3.SA_EMV_Lag_5", "MDIA3.SA_EMV_MA_Lag_1", "MDIA3.SA_EMV_MA_Lag_2", "MDIA3.SA_EMV_MA_Lag_3", "MDIA3.SA_EMV_MA_Lag_4", "MDIA3.SA_EMV_MA_Lag_5", "MDIA3.SA_VO_Lag_1", "MDIA3.SA_VO_Lag_2", "MDIA3.SA_VO_Lag_3", "MDIA3.SA_VO_Lag_4", "MDIA3.SA_VO_Lag_5", "MDIA3.SA_High_Max_50_Lag_1", "MDIA3.SA_High_Max_50_Lag_2", "MDIA3.SA_High_Max_50_Lag_3", "MDIA3.SA_High_Max_50_Lag_4", "MDIA3.SA_High_Max_50_Lag_5", "MDIA3.SA_Low_Min_50_Lag_1", "MDIA3.SA_Low_Min_50_Lag_2", "MDIA3.SA_Low_Min_50_Lag_3", "MDIA3.SA_Low_Min_50_Lag_4", "MDIA3.SA_Low_Min_50_Lag_5", "MDIA3.SA_MFI_Lag_1", "MDIA3.SA_MFI_Lag_2", "MDIA3.SA_MFI_Lag_3", "MDIA3.SA_MFI_Lag_4", "MDIA3.SA_MFI_Lag_5", "MDIA3.SA_Amihud_Lag_1", "MDIA3.SA_Amihud_Lag_2", "MDIA3.SA_Amihud_Lag_3", "MDIA3.SA_Amihud_Lag_4", "MDIA3.SA_Amihud_Lag_5", "MDIA3.SA_Roll_Spread_Lag_1", "MDIA3.SA_Roll_Spread_Lag_2", "MDIA3.SA_Roll_Spread_Lag_3", "MDIA3.SA_Roll_Spread_Lag_4", "MDIA3.SA_Roll_Spread_Lag_5", "MDIA3.SA_Hurst_Lag_1", "MDIA3.SA_Hurst_Lag_2", "MDIA3.SA_Hurst_Lag_3", "MDIA3.SA_<PERSON>rst_Lag_4", "MDIA3.<PERSON>_<PERSON><PERSON>_Lag_5", "MDIA3.SA_Vol_per_Volume_Lag_1", "MDIA3.SA_Vol_per_Volume_Lag_2", "MDIA3.SA_Vol_per_Volume_Lag_3", "MDIA3.SA_Vol_per_Volume_Lag_4", "MDIA3.SA_Vol_per_Volume_Lag_5", "MDIA3.SA_CMF_Lag_1", "MDIA3.SA_CMF_Lag_2", "MDIA3.SA_CMF_Lag_3", "MDIA3.SA_CMF_Lag_4", "MDIA3.SA_CMF_Lag_5", "MDIA3.SA_AD_Line_Lag_1", "MDIA3.SA_AD_Line_Lag_2", "MDIA3.SA_AD_Line_Lag_3", "MDIA3.SA_AD_Line_Lag_4", "MDIA3.SA_AD_Line_Lag_5", "CPFE3.SA_Open", "CPFE3.SA_High", "CPFE3.SA_Low", "CPFE3.SA_Close", "CPFE3.SA_Volume", "CPFE3.SA_Media_OHLC", "CPFE3.SA_MM10", "CPFE3.SA_MM25", "CPFE3.SA_MM100", "CPFE3.SA_Media_OHLC_PctChange", "CPFE3.SA_Volatilidade", "CPFE3.SA_Spread", "CPFE3.SA_Parkinson_Volatility", "CPFE3.SA_MFI", "CPFE3.SA_MFI_Historical", "CPFE3.SA_EMV", "CPFE3.SA_EMV_MA", "CPFE3.SA_Amihud", "CPFE3.SA_Amihud_Historical", "CPFE3.SA_Roll_Spread", "CPFE3.SA_Roll_Spread_Historical", "CPFE3.SA_Hurst", "CPFE3.SA_<PERSON><PERSON>_Historical", "CPFE3.SA_Vol_per_Volume", "CPFE3.SA_Vol_per_Volume_Historical", "CPFE3.SA_CMF", "CPFE3.SA_CMF_Historical", "CPFE3.SA_AD_Line", "CPFE3.SA_AD_Line_Historical", "CPFE3.SA_VO", "CPFE3.SA_High_Max_50", "CPFE3.SA_Low_Min_50", "CPFE3.SA_Segunda", "CPFE3.SA_Terca", "CPFE3.SA_Quarta", "CPFE3.SA_Quinta", "CPFE3.SA_Sexta", "CPFE3.SA_Mes_1", "CPFE3.SA_Mes_2", "CPFE3.SA_Mes_3", "CPFE3.SA_Mes_4", "CPFE3.SA_Mes_5", "CPFE3.SA_Mes_6", "CPFE3.SA_Mes_7", "CPFE3.SA_Mes_8", "CPFE3.SA_Mes_9", "CPFE3.SA_Mes_10", "CPFE3.SA_Mes_11", "CPFE3.SA_Mes_12", "CPFE3.SA_Quarter_1", "CPFE3.SA_Quarter_2", "CPFE3.SA_Quarter_3", "CPFE3.SA_Quarter_4", "CPFE3.SA_Last_Day_Quarter", "CPFE3.SA_Pre_Feriado_Brasil", "CPFE3.SA_Media_OHLC_Anterior", "CPFE3.SA_Sinal_Compra", "CPFE3.SA_Sinal_Venda", "CPFE3.SA_Media_OHLC_Futura", "CPFE3.SA_Media_OHLC_PctChange_Lag_1", "CPFE3.SA_Media_OHLC_PctChange_Lag_2", "CPFE3.SA_Media_OHLC_PctChange_Lag_3", "CPFE3.SA_Media_OHLC_PctChange_Lag_4", "CPFE3.SA_Media_OHLC_PctChange_Lag_5", "CPFE3.SA_Media_OHLC_PctChange_Lag_6", "CPFE3.SA_Media_OHLC_PctChange_Lag_7", "CPFE3.SA_Media_OHLC_PctChange_Lag_8", "CPFE3.SA_Media_OHLC_PctChange_Lag_9", "CPFE3.SA_Media_OHLC_PctChange_Lag_10", "CPFE3.SA_MM10_PctChange", "CPFE3.SA_Diff_OHLC_MM10", "CPFE3.SA_MM25_PctChange", "CPFE3.SA_Diff_OHLC_MM25", "CPFE3.SA_MM100_PctChange", "CPFE3.SA_Diff_OHLC_MM100", "CPFE3.SA_MM10_Menos_MM25", "CPFE3.SA_MM25_Menos_MM100", "CPFE3.SA_MM10_Menos_MM100", "CPFE3.SA_Volume_Lag_1", "CPFE3.SA_Volume_Lag_2", "CPFE3.SA_Volume_Lag_3", "CPFE3.SA_Volume_Lag_4", "CPFE3.SA_Volume_Lag_5", "CPFE3.SA_Spread_Lag_1", "CPFE3.SA_Spread_Lag_2", "CPFE3.SA_Spread_Lag_3", "CPFE3.SA_Spread_Lag_4", "CPFE3.SA_Spread_Lag_5", "CPFE3.SA_Volatilidade_Lag_1", "CPFE3.SA_Volatilidade_Lag_2", "CPFE3.SA_Volatilidade_Lag_3", "CPFE3.SA_Volatilidade_Lag_4", "CPFE3.SA_Volatilidade_Lag_5", "CPFE3.SA_Parkinson_Volatility_Lag_1", "CPFE3.<PERSON>_Parkinson_Volatility_Lag_2", "CPFE3.SA_Parkinson_Volatility_Lag_3", "CPFE3.SA_Parkinson_Volatility_Lag_4", "CPFE3.<PERSON>_Parkinson_Volatility_Lag_5", "CPFE3.SA_EMV_Lag_1", "CPFE3.SA_EMV_Lag_2", "CPFE3.SA_EMV_Lag_3", "CPFE3.SA_EMV_Lag_4", "CPFE3.SA_EMV_Lag_5", "CPFE3.SA_EMV_MA_Lag_1", "CPFE3.SA_EMV_MA_Lag_2", "CPFE3.SA_EMV_MA_Lag_3", "CPFE3.SA_EMV_MA_Lag_4", "CPFE3.SA_EMV_MA_Lag_5", "CPFE3.SA_VO_Lag_1", "CPFE3.SA_VO_Lag_2", "CPFE3.SA_VO_Lag_3", "CPFE3.SA_VO_Lag_4", "CPFE3.SA_VO_Lag_5", "CPFE3.SA_High_Max_50_Lag_1", "CPFE3.SA_High_Max_50_Lag_2", "CPFE3.SA_High_Max_50_Lag_3", "CPFE3.SA_High_Max_50_Lag_4", "CPFE3.SA_High_Max_50_Lag_5", "CPFE3.SA_Low_Min_50_Lag_1", "CPFE3.SA_Low_Min_50_Lag_2", "CPFE3.SA_Low_Min_50_Lag_3", "CPFE3.SA_Low_Min_50_Lag_4", "CPFE3.SA_Low_Min_50_Lag_5", "CPFE3.SA_MFI_Lag_1", "CPFE3.SA_MFI_Lag_2", "CPFE3.SA_MFI_Lag_3", "CPFE3.SA_MFI_Lag_4", "CPFE3.SA_MFI_Lag_5", "CPFE3.SA_Amihud_Lag_1", "CPFE3.SA_Amihud_Lag_2", "CPFE3.SA_Amihud_Lag_3", "CPFE3.SA_Amihud_Lag_4", "CPFE3.SA_Amihud_Lag_5", "CPFE3.SA_Roll_Spread_Lag_1", "CPFE3.SA_Roll_Spread_Lag_2", "CPFE3.SA_Roll_Spread_Lag_3", "CPFE3.SA_Roll_Spread_Lag_4", "CPFE3.SA_Roll_Spread_Lag_5", "CPFE3.SA_Hurst_Lag_1", "CPFE3.<PERSON>_Hu<PERSON>_Lag_2", "CPFE3.SA_Hurst_Lag_3", "CPFE3.<PERSON>_<PERSON><PERSON>_Lag_4", "CPFE3.<PERSON>_<PERSON><PERSON>_Lag_5", "CPFE3.SA_Vol_per_Volume_Lag_1", "CPFE3.SA_Vol_per_Volume_Lag_2", "CPFE3.SA_Vol_per_Volume_Lag_3", "CPFE3.SA_Vol_per_Volume_Lag_4", "CPFE3.SA_Vol_per_Volume_Lag_5", "CPFE3.SA_CMF_Lag_1", "CPFE3.SA_CMF_Lag_2", "CPFE3.SA_CMF_Lag_3", "CPFE3.SA_CMF_Lag_4", "CPFE3.SA_CMF_Lag_5", "CPFE3.SA_AD_Line_Lag_1", "CPFE3.SA_AD_Line_Lag_2", "CPFE3.SA_AD_Line_Lag_3", "CPFE3.SA_AD_Line_Lag_4", "CPFE3.SA_AD_Line_Lag_5"], "features_cached": 3648, "has_features": true, "cached_features_list": ["EMBR3.SA_Media_OHLC", "EMBR3.SA_MFI_Historical", "EMBR3.SA_Amihud_Historical", "EMBR3.SA_Roll_Spread_Historical", "EMBR3.SA_<PERSON><PERSON>_Historical", "EMBR3.SA_Vol_per_Volume_Historical", "EMBR3.SA_CMF_Historical", "EMBR3.SA_AD_Line_Historical", "EMBR3.SA_Segunda", "EMBR3.SA_Terca", "EMBR3.SA_Quarta", "EMBR3.SA_Quinta", "EMBR3.SA_Sexta", "EMBR3.SA_Mes_1", "EMBR3.SA_Mes_2", "EMBR3.SA_Mes_3", "EMBR3.SA_Mes_4", "EMBR3.SA_Mes_5", "EMBR3.SA_Mes_6", "EMBR3.SA_Mes_7", "EMBR3.SA_Mes_8", "EMBR3.SA_Mes_9", "EMBR3.SA_Mes_10", "EMBR3.SA_Mes_11", "EMBR3.SA_Mes_12", "EMBR3.SA_Quarter_1", "EMBR3.SA_Quarter_2", "EMBR3.SA_Quarter_3", "EMBR3.SA_Quarter_4", "EMBR3.SA_Last_Day_Quarter", "EMBR3.SA_Pre_Feriado_Brasil", "EMBR3.SA_Sinal_Compra", "EMBR3.SA_Sinal_Venda", "EMBR3.SA_Media_OHLC_Futura", "EMBR3.SA_Media_OHLC_PctChange_Lag_1", "EMBR3.SA_Media_OHLC_PctChange_Lag_2", "EMBR3.SA_Media_OHLC_PctChange_Lag_3", "EMBR3.SA_Media_OHLC_PctChange_Lag_4", "EMBR3.SA_Media_OHLC_PctChange_Lag_5", "EMBR3.SA_Media_OHLC_PctChange_Lag_6", "EMBR3.SA_Media_OHLC_PctChange_Lag_7", "EMBR3.SA_Media_OHLC_PctChange_Lag_8", "EMBR3.SA_Media_OHLC_PctChange_Lag_9", "EMBR3.SA_Media_OHLC_PctChange_Lag_10", "EMBR3.SA_Volume_Lag_1", "EMBR3.SA_Volume_Lag_2", "EMBR3.SA_Volume_Lag_3", "EMBR3.SA_Volume_Lag_4", "EMBR3.SA_Volume_Lag_5", "EMBR3.SA_Spread_Lag_1", "EMBR3.SA_Spread_Lag_2", "EMBR3.SA_Spread_Lag_3", "EMBR3.SA_Spread_Lag_4", "EMBR3.SA_Spread_Lag_5", "EMBR3.SA_Volatilidade_Lag_1", "EMBR3.SA_Volatilidade_Lag_2", "EMBR3.SA_Volatilidade_Lag_3", "EMBR3.SA_Volatilidade_Lag_4", "EMBR3.SA_Volatilidade_Lag_5", "EMBR3.SA_Parkinson_Volatility_Lag_1", "EMBR3.SA_Parkinson_Volatility_Lag_2", "EMBR3.SA_Parkinson_Volatility_Lag_3", "EMBR3.SA_Parkinson_Volatility_Lag_4", "EMBR3.SA_Parkinson_Volatility_Lag_5", "EMBR3.SA_EMV_Lag_1", "EMBR3.SA_EMV_Lag_2", "EMBR3.SA_EMV_Lag_3", "EMBR3.SA_EMV_Lag_4", "EMBR3.SA_EMV_Lag_5", "EMBR3.SA_EMV_MA_Lag_1", "EMBR3.SA_EMV_MA_Lag_2", "EMBR3.SA_EMV_MA_Lag_3", "EMBR3.SA_EMV_MA_Lag_4", "EMBR3.SA_EMV_MA_Lag_5", "EMBR3.SA_VO_Lag_1", "EMBR3.SA_VO_Lag_2", "EMBR3.SA_VO_Lag_3", "EMBR3.SA_VO_Lag_4", "EMBR3.SA_VO_Lag_5", "EMBR3.SA_MFI_Lag_1", "EMBR3.SA_MFI_Lag_2", "EMBR3.SA_MFI_Lag_3", "EMBR3.SA_MFI_Lag_4", "EMBR3.SA_MFI_Lag_5", "EMBR3.SA_Amihud_Lag_1", "EMBR3.SA_Amihud_Lag_2", "EMBR3.SA_Amihud_Lag_3", "EMBR3.SA_Amihud_Lag_4", "EMBR3.SA_Amihud_Lag_5", "EMBR3.SA_Roll_Spread_Lag_1", "EMBR3.SA_Roll_Spread_Lag_2", "EMBR3.SA_Roll_Spread_Lag_3", "EMBR3.SA_Roll_Spread_Lag_4", "EMBR3.SA_Roll_Spread_Lag_5", "EMBR3.SA_Hurst_Lag_1", "EMBR3.<PERSON>_<PERSON><PERSON>_Lag_2", "EMBR3.<PERSON>_<PERSON><PERSON>_Lag_3", "EMBR3.<PERSON>_<PERSON><PERSON>_Lag_4", "EMBR3.<PERSON>_<PERSON><PERSON>_Lag_5", "EMBR3.SA_Vol_per_Volume_Lag_1", "EMBR3.SA_Vol_per_Volume_Lag_2", "EMBR3.SA_Vol_per_Volume_Lag_3", "EMBR3.SA_Vol_per_Volume_Lag_4", "EMBR3.SA_Vol_per_Volume_Lag_5", "EMBR3.SA_CMF_Lag_1", "EMBR3.SA_CMF_Lag_2", "EMBR3.SA_CMF_Lag_3", "EMBR3.SA_CMF_Lag_4", "EMBR3.SA_CMF_Lag_5", "EMBR3.SA_AD_Line_Lag_1", "EMBR3.SA_AD_Line_Lag_2", "EMBR3.SA_AD_Line_Lag_3", "EMBR3.SA_AD_Line_Lag_4", "EMBR3.SA_AD_Line_Lag_5", "SYNE3.SA_Media_OHLC", "SYNE3.SA_MFI_Historical", "SYNE3.SA_Amihud_Historical", "SYNE3.SA_Roll_Spread_Historical", "SYNE3.<PERSON>_<PERSON><PERSON>_Historical", "SYNE3.SA_Vol_per_Volume_Historical", "SYNE3.SA_CMF_Historical", "SYNE3.SA_AD_Line_Historical", "SYNE3.SA_Segunda", "SYNE3.SA_Terca", "SYNE3.SA_Quarta", "SYNE3.SA_Quinta", "SYNE3.SA_Sexta", "SYNE3.SA_Mes_1", "SYNE3.SA_Mes_2", "SYNE3.SA_Mes_3", "SYNE3.SA_Mes_4", "SYNE3.SA_Mes_5", "SYNE3.SA_Mes_6", "SYNE3.SA_Mes_7", "SYNE3.SA_Mes_8", "SYNE3.SA_Mes_9", "SYNE3.SA_Mes_10", "SYNE3.SA_Mes_11", "SYNE3.SA_Mes_12", "SYNE3.SA_Quarter_1", "SYNE3.SA_Quarter_2", "SYNE3.SA_Quarter_3", "SYNE3.SA_Quarter_4", "SYNE3.SA_Last_Day_Quarter", "SYNE3.SA_Pre_Feriado_Brasil", "SYNE3.SA_Sinal_Compra", "SYNE3.SA_Sinal_Venda", "SYNE3.SA_Media_OHLC_Futura", "SYNE3.SA_Media_OHLC_PctChange_Lag_1", "SYNE3.SA_Media_OHLC_PctChange_Lag_2", "SYNE3.SA_Media_OHLC_PctChange_Lag_3", "SYNE3.SA_Media_OHLC_PctChange_Lag_4", "SYNE3.SA_Media_OHLC_PctChange_Lag_5", "SYNE3.SA_Media_OHLC_PctChange_Lag_6", "SYNE3.SA_Media_OHLC_PctChange_Lag_7", "SYNE3.SA_Media_OHLC_PctChange_Lag_8", "SYNE3.SA_Media_OHLC_PctChange_Lag_9", "SYNE3.SA_Media_OHLC_PctChange_Lag_10", "SYNE3.SA_Volume_Lag_1", "SYNE3.SA_Volume_Lag_2", "SYNE3.SA_Volume_Lag_3", "SYNE3.SA_Volume_Lag_4", "SYNE3.SA_Volume_Lag_5", "SYNE3.SA_Spread_Lag_1", "SYNE3.SA_Spread_Lag_2", "SYNE3.SA_Spread_Lag_3", "SYNE3.SA_Spread_Lag_4", "SYNE3.SA_Spread_Lag_5", "SYNE3.SA_Volatilidade_Lag_1", "SYNE3.SA_Volatilidade_Lag_2", "SYNE3.SA_Volatilidade_Lag_3", "SYNE3.SA_Volatilidade_Lag_4", "SYNE3.SA_Volatilidade_Lag_5", "SYNE3.<PERSON>_Parkinson_Volatility_Lag_1", "SYNE3.<PERSON>_Parkinson_Volatility_Lag_2", "SYNE3.<PERSON>_Parkinson_Volatility_Lag_3", "SYNE3.<PERSON>_Parkinson_Volatility_Lag_4", "SYNE3.<PERSON>_Parkinson_Volatility_Lag_5", "SYNE3.SA_EMV_Lag_1", "SYNE3.SA_EMV_Lag_2", "SYNE3.SA_EMV_Lag_3", "SYNE3.SA_EMV_Lag_4", "SYNE3.SA_EMV_Lag_5", "SYNE3.SA_EMV_MA_Lag_1", "SYNE3.SA_EMV_MA_Lag_2", "SYNE3.SA_EMV_MA_Lag_3", "SYNE3.SA_EMV_MA_Lag_4", "SYNE3.SA_EMV_MA_Lag_5", "SYNE3.SA_VO_Lag_1", "SYNE3.SA_VO_Lag_2", "SYNE3.SA_VO_Lag_3", "SYNE3.SA_VO_Lag_4", "SYNE3.SA_VO_Lag_5", "SYNE3.SA_MFI_Lag_1", "SYNE3.SA_MFI_Lag_2", "SYNE3.SA_MFI_Lag_3", "SYNE3.SA_MFI_Lag_4", "SYNE3.SA_MFI_Lag_5", "SYNE3.SA_Amihud_Lag_1", "SYNE3.SA_Amihud_Lag_2", "SYNE3.SA_Amihud_Lag_3", "SYNE3.SA_Amihud_Lag_4", "SYNE3.SA_Amihud_Lag_5", "SYNE3.SA_Roll_Spread_Lag_1", "SYNE3.SA_Roll_Spread_Lag_2", "SYNE3.SA_Roll_Spread_Lag_3", "SYNE3.SA_Roll_Spread_Lag_4", "SYNE3.SA_Roll_Spread_Lag_5", "SYNE3.<PERSON>_Hurst_Lag_1", "SYNE3.<PERSON>_<PERSON><PERSON>_Lag_2", "SYNE3.<PERSON>_<PERSON><PERSON>_Lag_3", "SYNE3.<PERSON>_<PERSON><PERSON>_Lag_4", "SYNE3.<PERSON>_<PERSON><PERSON>_Lag_5", "SYNE3.SA_Vol_per_Volume_Lag_1", "SYNE3.SA_Vol_per_Volume_Lag_2", "SYNE3.SA_Vol_per_Volume_Lag_3", "SYNE3.SA_Vol_per_Volume_Lag_4", "SYNE3.SA_Vol_per_Volume_Lag_5", "SYNE3.SA_CMF_Lag_1", "SYNE3.SA_CMF_Lag_2", "SYNE3.SA_CMF_Lag_3", "SYNE3.SA_CMF_Lag_4", "SYNE3.SA_CMF_Lag_5", "SYNE3.SA_AD_Line_Lag_1", "SYNE3.SA_AD_Line_Lag_2", "SYNE3.SA_AD_Line_Lag_3", "SYNE3.SA_AD_Line_Lag_4", "SYNE3.SA_AD_Line_Lag_5", "WEGE3.SA_Media_OHLC", "WEGE3.SA_MFI_Historical", "WEGE3.SA_Amihud_Historical", "WEGE3.SA_Roll_Spread_Historical", "WEGE3.<PERSON>_<PERSON><PERSON>_Historical", "WEGE3.SA_Vol_per_Volume_Historical", "WEGE3.SA_CMF_Historical", "WEGE3.SA_AD_Line_Historical", "WEGE3.SA_Segunda", "WEGE3.SA_Terca", "WEGE3.SA_Quarta", "WEGE3.SA_Quinta", "WEGE3.SA_Sexta", "WEGE3.SA_Mes_1", "WEGE3.SA_Mes_2", "WEGE3.SA_Mes_3", "WEGE3.SA_Mes_4", "WEGE3.SA_Mes_5", "WEGE3.SA_Mes_6", "WEGE3.SA_Mes_7", "WEGE3.SA_Mes_8", "WEGE3.SA_Mes_9", "WEGE3.SA_Mes_10", "WEGE3.SA_Mes_11", "WEGE3.SA_Mes_12", "WEGE3.SA_Quarter_1", "WEGE3.SA_Quarter_2", "WEGE3.SA_Quarter_3", "WEGE3.SA_Quarter_4", "WEGE3.SA_Last_Day_Quarter", "WEGE3.SA_Pre_Feriado_Brasil", "WEGE3.SA_Sinal_Compra", "WEGE3.SA_Sinal_Venda", "WEGE3.SA_Media_OHLC_Futura", "WEGE3.SA_Media_OHLC_PctChange_Lag_1", "WEGE3.SA_Media_OHLC_PctChange_Lag_2", "WEGE3.SA_Media_OHLC_PctChange_Lag_3", "WEGE3.SA_Media_OHLC_PctChange_Lag_4", "WEGE3.SA_Media_OHLC_PctChange_Lag_5", "WEGE3.SA_Media_OHLC_PctChange_Lag_6", "WEGE3.SA_Media_OHLC_PctChange_Lag_7", "WEGE3.SA_Media_OHLC_PctChange_Lag_8", "WEGE3.SA_Media_OHLC_PctChange_Lag_9", "WEGE3.SA_Media_OHLC_PctChange_Lag_10", "WEGE3.SA_Volume_Lag_1", "WEGE3.SA_Volume_Lag_2", "WEGE3.SA_Volume_Lag_3", "WEGE3.SA_Volume_Lag_4", "WEGE3.SA_Volume_Lag_5", "WEGE3.SA_Spread_Lag_1", "WEGE3.SA_Spread_Lag_2", "WEGE3.SA_Spread_Lag_3", "WEGE3.SA_Spread_Lag_4", "WEGE3.SA_Spread_Lag_5", "WEGE3.SA_Volatilidade_Lag_1", "WEGE3.SA_Volatilidade_Lag_2", "WEGE3.SA_Volatilidade_Lag_3", "WEGE3.SA_Volatilidade_Lag_4", "WEGE3.SA_Volatilidade_Lag_5", "WEGE3.SA_Parkinson_Volatility_Lag_1", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_2", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_3", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_4", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_5", "WEGE3.SA_EMV_Lag_1", "WEGE3.SA_EMV_Lag_2", "WEGE3.SA_EMV_Lag_3", "WEGE3.SA_EMV_Lag_4", "WEGE3.SA_EMV_Lag_5", "WEGE3.SA_EMV_MA_Lag_1", "WEGE3.SA_EMV_MA_Lag_2", "WEGE3.SA_EMV_MA_Lag_3", "WEGE3.SA_EMV_MA_Lag_4", "WEGE3.SA_EMV_MA_Lag_5", "WEGE3.SA_VO_Lag_1", "WEGE3.SA_VO_Lag_2", "WEGE3.SA_VO_Lag_3", "WEGE3.SA_VO_Lag_4", "WEGE3.SA_VO_Lag_5", "WEGE3.SA_MFI_Lag_1", "WEGE3.SA_MFI_Lag_2", "WEGE3.SA_MFI_Lag_3", "WEGE3.SA_MFI_Lag_4", "WEGE3.SA_MFI_Lag_5", "WEGE3.SA_Amihud_Lag_1", "WEGE3.SA_Amihud_Lag_2", "WEGE3.SA_Amihud_Lag_3", "WEGE3.SA_Amihud_Lag_4", "WEGE3.SA_Amihud_Lag_5", "WEGE3.SA_Roll_Spread_Lag_1", "WEGE3.SA_Roll_Spread_Lag_2", "WEGE3.SA_Roll_Spread_Lag_3", "WEGE3.SA_Roll_Spread_Lag_4", "WEGE3.SA_Roll_Spread_Lag_5", "WEGE3.<PERSON>_Hurst_Lag_1", "WEGE3.<PERSON>_Hu<PERSON>_Lag_2", "WEGE3.<PERSON>_Hu<PERSON>_Lag_3", "WEGE3.<PERSON>_<PERSON><PERSON>_Lag_4", "WEGE3.<PERSON>_<PERSON><PERSON>_Lag_5", "WEGE3.SA_Vol_per_Volume_Lag_1", "WEGE3.SA_Vol_per_Volume_Lag_2", "WEGE3.SA_Vol_per_Volume_Lag_3", "WEGE3.SA_Vol_per_Volume_Lag_4", "WEGE3.SA_Vol_per_Volume_Lag_5", "WEGE3.SA_CMF_Lag_1", "WEGE3.SA_CMF_Lag_2", "WEGE3.SA_CMF_Lag_3", "WEGE3.SA_CMF_Lag_4", "WEGE3.SA_CMF_Lag_5", "WEGE3.SA_AD_Line_Lag_1", "WEGE3.SA_AD_Line_Lag_2", "WEGE3.SA_AD_Line_Lag_3", "WEGE3.SA_AD_Line_Lag_4", "WEGE3.SA_AD_Line_Lag_5", "STBP3.SA_Media_OHLC", "STBP3.SA_MFI_Historical", "STBP3.SA_Amihud_Historical", "STBP3.SA_Roll_Spread_Historical", "STBP3.SA_<PERSON><PERSON>_Historical", "STBP3.SA_Vol_per_Volume_Historical", "STBP3.SA_CMF_Historical", "STBP3.SA_AD_Line_Historical", "STBP3.SA_Segunda", "STBP3.SA_Terca", "STBP3.SA_Quarta", "STBP3.SA_Quinta", "STBP3.SA_Sexta", "STBP3.SA_Mes_1", "STBP3.SA_Mes_2", "STBP3.SA_Mes_3", "STBP3.SA_Mes_4", "STBP3.SA_Mes_5", "STBP3.SA_Mes_6", "STBP3.SA_Mes_7", "STBP3.SA_Mes_8", "STBP3.SA_Mes_9", "STBP3.SA_Mes_10", "STBP3.SA_Mes_11", "STBP3.SA_Mes_12", "STBP3.SA_Quarter_1", "STBP3.SA_Quarter_2", "STBP3.SA_Quarter_3", "STBP3.SA_Quarter_4", "STBP3.SA_Last_Day_Quarter", "STBP3.SA_Pre_Feriado_Brasil", "STBP3.SA_Sinal_Compra", "STBP3.SA_Sinal_Venda", "STBP3.SA_Media_OHLC_Futura", "STBP3.SA_Media_OHLC_PctChange_Lag_1", "STBP3.SA_Media_OHLC_PctChange_Lag_2", "STBP3.SA_Media_OHLC_PctChange_Lag_3", "STBP3.SA_Media_OHLC_PctChange_Lag_4", "STBP3.SA_Media_OHLC_PctChange_Lag_5", "STBP3.SA_Media_OHLC_PctChange_Lag_6", "STBP3.SA_Media_OHLC_PctChange_Lag_7", "STBP3.SA_Media_OHLC_PctChange_Lag_8", "STBP3.SA_Media_OHLC_PctChange_Lag_9", "STBP3.SA_Media_OHLC_PctChange_Lag_10", "STBP3.SA_Volume_Lag_1", "STBP3.SA_Volume_Lag_2", "STBP3.SA_Volume_Lag_3", "STBP3.SA_Volume_Lag_4", "STBP3.SA_Volume_Lag_5", "STBP3.SA_Spread_Lag_1", "STBP3.SA_Spread_Lag_2", "STBP3.SA_Spread_Lag_3", "STBP3.SA_Spread_Lag_4", "STBP3.SA_Spread_Lag_5", "STBP3.SA_Volatilidade_Lag_1", "STBP3.SA_Volatilidade_Lag_2", "STBP3.SA_Volatilidade_Lag_3", "STBP3.SA_Volatilidade_Lag_4", "STBP3.SA_Volatilidade_Lag_5", "STBP3.SA_Parkinson_Volatility_Lag_1", "STBP3.SA_Parkinson_Volatility_Lag_2", "STBP3.SA_Parkinson_Volatility_Lag_3", "STBP3.SA_Parkinson_Volatility_Lag_4", "STBP3.<PERSON>_Parkinson_Volatility_Lag_5", "STBP3.SA_EMV_Lag_1", "STBP3.SA_EMV_Lag_2", "STBP3.SA_EMV_Lag_3", "STBP3.SA_EMV_Lag_4", "STBP3.SA_EMV_Lag_5", "STBP3.SA_EMV_MA_Lag_1", "STBP3.SA_EMV_MA_Lag_2", "STBP3.SA_EMV_MA_Lag_3", "STBP3.SA_EMV_MA_Lag_4", "STBP3.SA_EMV_MA_Lag_5", "STBP3.SA_VO_Lag_1", "STBP3.SA_VO_Lag_2", "STBP3.SA_VO_Lag_3", "STBP3.SA_VO_Lag_4", "STBP3.SA_VO_Lag_5", "STBP3.SA_MFI_Lag_1", "STBP3.SA_MFI_Lag_2", "STBP3.SA_MFI_Lag_3", "STBP3.SA_MFI_Lag_4", "STBP3.SA_MFI_Lag_5", "STBP3.SA_Amihud_Lag_1", "STBP3.SA_Amihud_Lag_2", "STBP3.SA_Amihud_Lag_3", "STBP3.SA_Amihud_Lag_4", "STBP3.SA_Amihud_Lag_5", "STBP3.SA_Roll_Spread_Lag_1", "STBP3.SA_Roll_Spread_Lag_2", "STBP3.SA_Roll_Spread_Lag_3", "STBP3.SA_Roll_Spread_Lag_4", "STBP3.SA_Roll_Spread_Lag_5", "STBP3.SA_Hurst_Lag_1", "STBP3.SA_Hurst_Lag_2", "STBP3.SA_Hurst_Lag_3", "STBP3.SA_Hurst_Lag_4", "STBP3.<PERSON>_<PERSON><PERSON>_Lag_5", "STBP3.SA_Vol_per_Volume_Lag_1", "STBP3.SA_Vol_per_Volume_Lag_2", "STBP3.SA_Vol_per_Volume_Lag_3", "STBP3.SA_Vol_per_Volume_Lag_4", "STBP3.SA_Vol_per_Volume_Lag_5", "STBP3.SA_CMF_Lag_1", "STBP3.SA_CMF_Lag_2", "STBP3.SA_CMF_Lag_3", "STBP3.SA_CMF_Lag_4", "STBP3.SA_CMF_Lag_5", "STBP3.SA_AD_Line_Lag_1", "STBP3.SA_AD_Line_Lag_2", "STBP3.SA_AD_Line_Lag_3", "STBP3.SA_AD_Line_Lag_4", "STBP3.SA_AD_Line_Lag_5", "SLCE3.SA_Media_OHLC", "SLCE3.SA_MFI_Historical", "SLCE3.SA_Amihud_Historical", "SLCE3.SA_Roll_Spread_Historical", "SLCE3.SA_<PERSON><PERSON>_Historical", "SLCE3.SA_Vol_per_Volume_Historical", "SLCE3.SA_CMF_Historical", "SLCE3.SA_AD_Line_Historical", "SLCE3.SA_Segunda", "SLCE3.SA_Terca", "SLCE3.SA_Quarta", "SLCE3.SA_Quinta", "SLCE3.SA_Sexta", "SLCE3.SA_Mes_1", "SLCE3.SA_Mes_2", "SLCE3.SA_Mes_3", "SLCE3.SA_Mes_4", "SLCE3.SA_Mes_5", "SLCE3.SA_Mes_6", "SLCE3.SA_Mes_7", "SLCE3.SA_Mes_8", "SLCE3.SA_Mes_9", "SLCE3.SA_Mes_10", "SLCE3.SA_Mes_11", "SLCE3.SA_Mes_12", "SLCE3.SA_Quarter_1", "SLCE3.SA_Quarter_2", "SLCE3.SA_Quarter_3", "SLCE3.SA_Quarter_4", "SLCE3.SA_Last_Day_Quarter", "SLCE3.SA_Pre_Feriado_Brasil", "SLCE3.SA_Sinal_Compra", "SLCE3.SA_Sinal_Venda", "SLCE3.SA_Media_OHLC_Futura", "SLCE3.SA_Media_OHLC_PctChange_Lag_1", "SLCE3.SA_Media_OHLC_PctChange_Lag_2", "SLCE3.SA_Media_OHLC_PctChange_Lag_3", "SLCE3.SA_Media_OHLC_PctChange_Lag_4", "SLCE3.SA_Media_OHLC_PctChange_Lag_5", "SLCE3.SA_Media_OHLC_PctChange_Lag_6", "SLCE3.SA_Media_OHLC_PctChange_Lag_7", "SLCE3.SA_Media_OHLC_PctChange_Lag_8", "SLCE3.SA_Media_OHLC_PctChange_Lag_9", "SLCE3.SA_Media_OHLC_PctChange_Lag_10", "SLCE3.SA_Volume_Lag_1", "SLCE3.SA_Volume_Lag_2", "SLCE3.SA_Volume_Lag_3", "SLCE3.SA_Volume_Lag_4", "SLCE3.SA_Volume_Lag_5", "SLCE3.SA_Spread_Lag_1", "SLCE3.SA_Spread_Lag_2", "SLCE3.SA_Spread_Lag_3", "SLCE3.SA_Spread_Lag_4", "SLCE3.SA_Spread_Lag_5", "SLCE3.SA_Volatilidade_Lag_1", "SLCE3.SA_Volatilidade_Lag_2", "SLCE3.SA_Volatilidade_Lag_3", "SLCE3.SA_Volatilidade_Lag_4", "SLCE3.SA_Volatilidade_Lag_5", "SLCE3.SA_Parkinson_Volatility_Lag_1", "SLCE3.SA_Parkinson_Volatility_Lag_2", "SLCE3.SA_Parkinson_Volatility_Lag_3", "SLCE3.SA_Parkinson_Volatility_Lag_4", "SLCE3.SA_Parkinson_Volatility_Lag_5", "SLCE3.SA_EMV_Lag_1", "SLCE3.SA_EMV_Lag_2", "SLCE3.SA_EMV_Lag_3", "SLCE3.SA_EMV_Lag_4", "SLCE3.SA_EMV_Lag_5", "SLCE3.SA_EMV_MA_Lag_1", "SLCE3.SA_EMV_MA_Lag_2", "SLCE3.SA_EMV_MA_Lag_3", "SLCE3.SA_EMV_MA_Lag_4", "SLCE3.SA_EMV_MA_Lag_5", "SLCE3.SA_VO_Lag_1", "SLCE3.SA_VO_Lag_2", "SLCE3.SA_VO_Lag_3", "SLCE3.SA_VO_Lag_4", "SLCE3.SA_VO_Lag_5", "SLCE3.SA_MFI_Lag_1", "SLCE3.SA_MFI_Lag_2", "SLCE3.SA_MFI_Lag_3", "SLCE3.SA_MFI_Lag_4", "SLCE3.SA_MFI_Lag_5", "SLCE3.SA_Amihud_Lag_1", "SLCE3.SA_Amihud_Lag_2", "SLCE3.SA_Amihud_Lag_3", "SLCE3.SA_Amihud_Lag_4", "SLCE3.SA_Amihud_Lag_5", "SLCE3.SA_Roll_Spread_Lag_1", "SLCE3.SA_Roll_Spread_Lag_2", "SLCE3.SA_Roll_Spread_Lag_3", "SLCE3.SA_Roll_Spread_Lag_4", "SLCE3.SA_Roll_Spread_Lag_5", "SLCE3.SA_Hurst_Lag_1", "SLCE3.SA_Hurst_Lag_2", "SLCE3.SA_<PERSON>rst_Lag_3", "SLCE3.SA_<PERSON>rst_Lag_4", "SLCE3.<PERSON>_<PERSON><PERSON>_Lag_5", "SLCE3.SA_Vol_per_Volume_Lag_1", "SLCE3.SA_Vol_per_Volume_Lag_2", "SLCE3.SA_Vol_per_Volume_Lag_3", "SLCE3.SA_Vol_per_Volume_Lag_4", "SLCE3.SA_Vol_per_Volume_Lag_5", "SLCE3.SA_CMF_Lag_1", "SLCE3.SA_CMF_Lag_2", "SLCE3.SA_CMF_Lag_3", "SLCE3.SA_CMF_Lag_4", "SLCE3.SA_CMF_Lag_5", "SLCE3.SA_AD_Line_Lag_1", "SLCE3.SA_AD_Line_Lag_2", "SLCE3.SA_AD_Line_Lag_3", "SLCE3.SA_AD_Line_Lag_4", "SLCE3.SA_AD_Line_Lag_5", "MRFG3.SA_Media_OHLC", "MRFG3.SA_MFI_Historical", "MRFG3.SA_Amihud_Historical", "MRFG3.SA_Roll_Spread_Historical", "MRFG3.SA_<PERSON><PERSON>_Historical", "MRFG3.SA_Vol_per_Volume_Historical", "MRFG3.SA_CMF_Historical", "MRFG3.SA_AD_Line_Historical", "MRFG3.SA_Segunda", "MRFG3.SA_Terca", "MRFG3.SA_Quarta", "MRFG3.SA_Quinta", "MRFG3.SA_Sexta", "MRFG3.SA_Mes_1", "MRFG3.SA_Mes_2", "MRFG3.SA_Mes_3", "MRFG3.SA_Mes_4", "MRFG3.SA_Mes_5", "MRFG3.SA_Mes_6", "MRFG3.SA_Mes_7", "MRFG3.SA_Mes_8", "MRFG3.SA_Mes_9", "MRFG3.SA_Mes_10", "MRFG3.SA_Mes_11", "MRFG3.SA_Mes_12", "MRFG3.SA_Quarter_1", "MRFG3.SA_Quarter_2", "MRFG3.SA_Quarter_3", "MRFG3.SA_Quarter_4", "MRFG3.SA_Last_Day_Quarter", "MRFG3.SA_Pre_Feriado_Brasil", "MRFG3.SA_Sinal_Compra", "MRFG3.SA_Sinal_Venda", "MRFG3.SA_Media_OHLC_Futura", "MRFG3.SA_Media_OHLC_PctChange_Lag_1", "MRFG3.SA_Media_OHLC_PctChange_Lag_2", "MRFG3.SA_Media_OHLC_PctChange_Lag_3", "MRFG3.SA_Media_OHLC_PctChange_Lag_4", "MRFG3.SA_Media_OHLC_PctChange_Lag_5", "MRFG3.SA_Media_OHLC_PctChange_Lag_6", "MRFG3.SA_Media_OHLC_PctChange_Lag_7", "MRFG3.SA_Media_OHLC_PctChange_Lag_8", "MRFG3.SA_Media_OHLC_PctChange_Lag_9", "MRFG3.SA_Media_OHLC_PctChange_Lag_10", "MRFG3.SA_Volume_Lag_1", "MRFG3.SA_Volume_Lag_2", "MRFG3.SA_Volume_Lag_3", "MRFG3.SA_Volume_Lag_4", "MRFG3.SA_Volume_Lag_5", "MRFG3.SA_Spread_Lag_1", "MRFG3.SA_Spread_Lag_2", "MRFG3.SA_Spread_Lag_3", "MRFG3.SA_Spread_Lag_4", "MRFG3.SA_Spread_Lag_5", "MRFG3.SA_Volatilidade_Lag_1", "MRFG3.SA_Volatilidade_Lag_2", "MRFG3.SA_Volatilidade_Lag_3", "MRFG3.SA_Volatilidade_Lag_4", "MRFG3.SA_Volatilidade_Lag_5", "MRFG3.SA_Parkinson_Volatility_Lag_1", "MRFG3.SA_Parkinson_Volatility_Lag_2", "MRFG3.SA_Parkinson_Volatility_Lag_3", "MRFG3.SA_Parkinson_Volatility_Lag_4", "MRFG3.SA_Parkinson_Volatility_Lag_5", "MRFG3.SA_EMV_Lag_1", "MRFG3.SA_EMV_Lag_2", "MRFG3.SA_EMV_Lag_3", "MRFG3.SA_EMV_Lag_4", "MRFG3.SA_EMV_Lag_5", "MRFG3.SA_EMV_MA_Lag_1", "MRFG3.SA_EMV_MA_Lag_2", "MRFG3.SA_EMV_MA_Lag_3", "MRFG3.SA_EMV_MA_Lag_4", "MRFG3.SA_EMV_MA_Lag_5", "MRFG3.SA_VO_Lag_1", "MRFG3.SA_VO_Lag_2", "MRFG3.SA_VO_Lag_3", "MRFG3.SA_VO_Lag_4", "MRFG3.SA_VO_Lag_5", "MRFG3.SA_MFI_Lag_1", "MRFG3.SA_MFI_Lag_2", "MRFG3.SA_MFI_Lag_3", "MRFG3.SA_MFI_Lag_4", "MRFG3.SA_MFI_Lag_5", "MRFG3.SA_Amihud_Lag_1", "MRFG3.SA_Amihud_Lag_2", "MRFG3.SA_Amihud_Lag_3", "MRFG3.SA_Amihud_Lag_4", "MRFG3.SA_Amihud_Lag_5", "MRFG3.SA_Roll_Spread_Lag_1", "MRFG3.SA_Roll_Spread_Lag_2", "MRFG3.SA_Roll_Spread_Lag_3", "MRFG3.SA_Roll_Spread_Lag_4", "MRFG3.SA_Roll_Spread_Lag_5", "MRFG3.SA_Hurst_Lag_1", "MRFG3.<PERSON>_Hurst_Lag_2", "MRFG3.SA_<PERSON>rst_Lag_3", "MRFG3.<PERSON>_<PERSON><PERSON>_Lag_4", "MRFG3.<PERSON>_<PERSON><PERSON>_Lag_5", "MRFG3.SA_Vol_per_Volume_Lag_1", "MRFG3.SA_Vol_per_Volume_Lag_2", "MRFG3.SA_Vol_per_Volume_Lag_3", "MRFG3.SA_Vol_per_Volume_Lag_4", "MRFG3.SA_Vol_per_Volume_Lag_5", "MRFG3.SA_CMF_Lag_1", "MRFG3.SA_CMF_Lag_2", "MRFG3.SA_CMF_Lag_3", "MRFG3.SA_CMF_Lag_4", "MRFG3.SA_CMF_Lag_5", "MRFG3.SA_AD_Line_Lag_1", "MRFG3.SA_AD_Line_Lag_2", "MRFG3.SA_AD_Line_Lag_3", "MRFG3.SA_AD_Line_Lag_4", "MRFG3.SA_AD_Line_Lag_5", "SUZB3.SA_Media_OHLC", "SUZB3.SA_MFI_Historical", "SUZB3.SA_Amihud_Historical", "SUZB3.SA_Roll_Spread_Historical", "SUZB3.SA_Hurst_Historical", "SUZB3.SA_Vol_per_Volume_Historical", "SUZB3.SA_CMF_Historical", "SUZB3.SA_AD_Line_Historical", "SUZB3.SA_Segunda", "SUZB3.SA_Terca", "SUZB3.SA_Quarta", "SUZB3.SA_Quinta", "SUZB3.SA_Sexta", "SUZB3.SA_Mes_1", "SUZB3.SA_Mes_2", "SUZB3.SA_Mes_3", "SUZB3.SA_Mes_4", "SUZB3.SA_Mes_5", "SUZB3.SA_Mes_6", "SUZB3.SA_Mes_7", "SUZB3.SA_Mes_8", "SUZB3.SA_Mes_9", "SUZB3.SA_Mes_10", "SUZB3.SA_Mes_11", "SUZB3.SA_Mes_12", "SUZB3.SA_Quarter_1", "SUZB3.SA_Quarter_2", "SUZB3.SA_Quarter_3", "SUZB3.SA_Quarter_4", "SUZB3.SA_Last_Day_Quarter", "SUZB3.SA_Pre_Feriado_Brasil", "SUZB3.SA_Sinal_Compra", "SUZB3.SA_Sinal_Venda", "SUZB3.SA_Media_OHLC_Futura", "SUZB3.SA_Media_OHLC_PctChange_Lag_1", "SUZB3.SA_Media_OHLC_PctChange_Lag_2", "SUZB3.SA_Media_OHLC_PctChange_Lag_3", "SUZB3.SA_Media_OHLC_PctChange_Lag_4", "SUZB3.SA_Media_OHLC_PctChange_Lag_5", "SUZB3.SA_Media_OHLC_PctChange_Lag_6", "SUZB3.SA_Media_OHLC_PctChange_Lag_7", "SUZB3.SA_Media_OHLC_PctChange_Lag_8", "SUZB3.SA_Media_OHLC_PctChange_Lag_9", "SUZB3.SA_Media_OHLC_PctChange_Lag_10", "SUZB3.SA_Volume_Lag_1", "SUZB3.SA_Volume_Lag_2", "SUZB3.SA_Volume_Lag_3", "SUZB3.SA_Volume_Lag_4", "SUZB3.SA_Volume_Lag_5", "SUZB3.SA_Spread_Lag_1", "SUZB3.SA_Spread_Lag_2", "SUZB3.SA_Spread_Lag_3", "SUZB3.SA_Spread_Lag_4", "SUZB3.SA_Spread_Lag_5", "SUZB3.SA_Volatilidade_Lag_1", "SUZB3.SA_Volatilidade_Lag_2", "SUZB3.SA_Volatilidade_Lag_3", "SUZB3.SA_Volatilidade_Lag_4", "SUZB3.SA_Volatilidade_Lag_5", "SUZB3.SA_Parkinson_Volatility_Lag_1", "SUZB3.SA_Parkinson_Volatility_Lag_2", "SUZB3.SA_Parkinson_Volatility_Lag_3", "SUZB3.SA_Parkinson_Volatility_Lag_4", "SUZB3.<PERSON>_Parkinson_Volatility_Lag_5", "SUZB3.SA_EMV_Lag_1", "SUZB3.SA_EMV_Lag_2", "SUZB3.SA_EMV_Lag_3", "SUZB3.SA_EMV_Lag_4", "SUZB3.SA_EMV_Lag_5", "SUZB3.SA_EMV_MA_Lag_1", "SUZB3.SA_EMV_MA_Lag_2", "SUZB3.SA_EMV_MA_Lag_3", "SUZB3.SA_EMV_MA_Lag_4", "SUZB3.SA_EMV_MA_Lag_5", "SUZB3.SA_VO_Lag_1", "SUZB3.SA_VO_Lag_2", "SUZB3.SA_VO_Lag_3", "SUZB3.SA_VO_Lag_4", "SUZB3.SA_VO_Lag_5", "SUZB3.SA_MFI_Lag_1", "SUZB3.SA_MFI_Lag_2", "SUZB3.SA_MFI_Lag_3", "SUZB3.SA_MFI_Lag_4", "SUZB3.SA_MFI_Lag_5", "SUZB3.SA_Amihud_Lag_1", "SUZB3.SA_Amihud_Lag_2", "SUZB3.SA_Amihud_Lag_3", "SUZB3.SA_Amihud_Lag_4", "SUZB3.SA_Amihud_Lag_5", "SUZB3.SA_Roll_Spread_Lag_1", "SUZB3.SA_Roll_Spread_Lag_2", "SUZB3.SA_Roll_Spread_Lag_3", "SUZB3.SA_Roll_Spread_Lag_4", "SUZB3.SA_Roll_Spread_Lag_5", "SUZB3.SA_Hurst_Lag_1", "SUZB3.SA_Hurst_Lag_2", "SUZB3.SA_Hurst_Lag_3", "SUZB3.SA_Hurst_Lag_4", "SUZB3.SA_<PERSON>rst_Lag_5", "SUZB3.SA_Vol_per_Volume_Lag_1", "SUZB3.SA_Vol_per_Volume_Lag_2", "SUZB3.SA_Vol_per_Volume_Lag_3", "SUZB3.SA_Vol_per_Volume_Lag_4", "SUZB3.SA_Vol_per_Volume_Lag_5", "SUZB3.SA_CMF_Lag_1", "SUZB3.SA_CMF_Lag_2", "SUZB3.SA_CMF_Lag_3", "SUZB3.SA_CMF_Lag_4", "SUZB3.SA_CMF_Lag_5", "SUZB3.SA_AD_Line_Lag_1", "SUZB3.SA_AD_Line_Lag_2", "SUZB3.SA_AD_Line_Lag_3", "SUZB3.SA_AD_Line_Lag_4", "SUZB3.SA_AD_Line_Lag_5", "BRFS3.SA_Media_OHLC", "BRFS3.SA_MFI_Historical", "BRFS3.SA_Amihud_Historical", "BRFS3.SA_Roll_Spread_Historical", "BRFS3.SA_Hurst_Historical", "BRFS3.SA_Vol_per_Volume_Historical", "BRFS3.SA_CMF_Historical", "BRFS3.SA_AD_Line_Historical", "BRFS3.SA_Segunda", "BRFS3.SA_Terca", "BRFS3.SA_Quarta", "BRFS3.SA_Quinta", "BRFS3.SA_Sexta", "BRFS3.SA_Mes_1", "BRFS3.SA_Mes_2", "BRFS3.SA_Mes_3", "BRFS3.SA_Mes_4", "BRFS3.SA_Mes_5", "BRFS3.SA_Mes_6", "BRFS3.SA_Mes_7", "BRFS3.SA_Mes_8", "BRFS3.SA_Mes_9", "BRFS3.SA_Mes_10", "BRFS3.SA_Mes_11", "BRFS3.SA_Mes_12", "BRFS3.SA_Quarter_1", "BRFS3.SA_Quarter_2", "BRFS3.SA_Quarter_3", "BRFS3.SA_Quarter_4", "BRFS3.SA_Last_Day_Quarter", "BRFS3.SA_Pre_Feriado_Brasil", "BRFS3.SA_Sinal_Compra", "BRFS3.SA_Sinal_Venda", "BRFS3.SA_Media_OHLC_Futura", "BRFS3.SA_Media_OHLC_PctChange_Lag_1", "BRFS3.SA_Media_OHLC_PctChange_Lag_2", "BRFS3.SA_Media_OHLC_PctChange_Lag_3", "BRFS3.SA_Media_OHLC_PctChange_Lag_4", "BRFS3.SA_Media_OHLC_PctChange_Lag_5", "BRFS3.SA_Media_OHLC_PctChange_Lag_6", "BRFS3.SA_Media_OHLC_PctChange_Lag_7", "BRFS3.SA_Media_OHLC_PctChange_Lag_8", "BRFS3.SA_Media_OHLC_PctChange_Lag_9", "BRFS3.SA_Media_OHLC_PctChange_Lag_10", "BRFS3.SA_Volume_Lag_1", "BRFS3.SA_Volume_Lag_2", "BRFS3.SA_Volume_Lag_3", "BRFS3.SA_Volume_Lag_4", "BRFS3.SA_Volume_Lag_5", "BRFS3.SA_Spread_Lag_1", "BRFS3.SA_Spread_Lag_2", "BRFS3.SA_Spread_Lag_3", "BRFS3.SA_Spread_Lag_4", "BRFS3.SA_Spread_Lag_5", "BRFS3.SA_Volatilidade_Lag_1", "BRFS3.SA_Volatilidade_Lag_2", "BRFS3.SA_Volatilidade_Lag_3", "BRFS3.SA_Volatilidade_Lag_4", "BRFS3.SA_Volatilidade_Lag_5", "BRFS3.SA_Parkinson_Volatility_Lag_1", "BRFS3.SA_Parkinson_Volatility_Lag_2", "BRFS3.SA_Parkinson_Volatility_Lag_3", "BRFS3.SA_Parkinson_Volatility_Lag_4", "BRFS3.SA_Parkinson_Volatility_Lag_5", "BRFS3.SA_EMV_Lag_1", "BRFS3.SA_EMV_Lag_2", "BRFS3.SA_EMV_Lag_3", "BRFS3.SA_EMV_Lag_4", "BRFS3.SA_EMV_Lag_5", "BRFS3.SA_EMV_MA_Lag_1", "BRFS3.SA_EMV_MA_Lag_2", "BRFS3.SA_EMV_MA_Lag_3", "BRFS3.SA_EMV_MA_Lag_4", "BRFS3.SA_EMV_MA_Lag_5", "BRFS3.SA_VO_Lag_1", "BRFS3.SA_VO_Lag_2", "BRFS3.SA_VO_Lag_3", "BRFS3.SA_VO_Lag_4", "BRFS3.SA_VO_Lag_5", "BRFS3.SA_MFI_Lag_1", "BRFS3.SA_MFI_Lag_2", "BRFS3.SA_MFI_Lag_3", "BRFS3.SA_MFI_Lag_4", "BRFS3.SA_MFI_Lag_5", "BRFS3.SA_Amihud_Lag_1", "BRFS3.SA_Amihud_Lag_2", "BRFS3.SA_Amihud_Lag_3", "BRFS3.SA_Amihud_Lag_4", "BRFS3.SA_Amihud_Lag_5", "BRFS3.SA_Roll_Spread_Lag_1", "BRFS3.SA_Roll_Spread_Lag_2", "BRFS3.SA_Roll_Spread_Lag_3", "BRFS3.SA_Roll_Spread_Lag_4", "BRFS3.SA_Roll_Spread_Lag_5", "BRFS3.SA_Hurst_Lag_1", "BRFS3.SA_Hurst_Lag_2", "BRFS3.SA_Hurst_Lag_3", "BRFS3.SA_Hurst_Lag_4", "BRFS3.SA_<PERSON>rst_Lag_5", "BRFS3.SA_Vol_per_Volume_Lag_1", "BRFS3.SA_Vol_per_Volume_Lag_2", "BRFS3.SA_Vol_per_Volume_Lag_3", "BRFS3.SA_Vol_per_Volume_Lag_4", "BRFS3.SA_Vol_per_Volume_Lag_5", "BRFS3.SA_CMF_Lag_1", "BRFS3.SA_CMF_Lag_2", "BRFS3.SA_CMF_Lag_3", "BRFS3.SA_CMF_Lag_4", "BRFS3.SA_CMF_Lag_5", "BRFS3.SA_AD_Line_Lag_1", "BRFS3.SA_AD_Line_Lag_2", "BRFS3.SA_AD_Line_Lag_3", "BRFS3.SA_AD_Line_Lag_4", "BRFS3.SA_AD_Line_Lag_5", "PORT3.SA_Media_OHLC", "PORT3.SA_MFI_Historical", "PORT3.SA_Amihud_Historical", "PORT3.SA_Roll_Spread_Historical", "PORT3.<PERSON>_<PERSON><PERSON>_Historical", "PORT3.SA_Vol_per_Volume_Historical", "PORT3.SA_CMF_Historical", "PORT3.SA_AD_Line_Historical", "PORT3.SA_Segunda", "PORT3.SA_Terca", "PORT3.SA_Quarta", "PORT3.SA_Quinta", "PORT3.SA_Sexta", "PORT3.SA_Mes_1", "PORT3.SA_Mes_2", "PORT3.SA_Mes_3", "PORT3.SA_Mes_4", "PORT3.SA_Mes_5", "PORT3.SA_Mes_6", "PORT3.SA_Mes_7", "PORT3.SA_Mes_8", "PORT3.SA_Mes_9", "PORT3.SA_Mes_10", "PORT3.SA_Mes_11", "PORT3.SA_Mes_12", "PORT3.SA_Quarter_1", "PORT3.SA_Quarter_2", "PORT3.SA_Quarter_3", "PORT3.SA_Quarter_4", "PORT3.SA_Last_Day_Quarter", "PORT3.SA_Pre_Feriado_Brasil", "PORT3.SA_Sinal_Compra", "PORT3.SA_Sinal_Venda", "PORT3.SA_Media_OHLC_Futura", "PORT3.SA_Media_OHLC_PctChange_Lag_1", "PORT3.SA_Media_OHLC_PctChange_Lag_2", "PORT3.SA_Media_OHLC_PctChange_Lag_3", "PORT3.SA_Media_OHLC_PctChange_Lag_4", "PORT3.SA_Media_OHLC_PctChange_Lag_5", "PORT3.SA_Media_OHLC_PctChange_Lag_6", "PORT3.SA_Media_OHLC_PctChange_Lag_7", "PORT3.SA_Media_OHLC_PctChange_Lag_8", "PORT3.SA_Media_OHLC_PctChange_Lag_9", "PORT3.SA_Media_OHLC_PctChange_Lag_10", "PORT3.SA_Volume_Lag_1", "PORT3.SA_Volume_Lag_2", "PORT3.SA_Volume_Lag_3", "PORT3.SA_Volume_Lag_4", "PORT3.SA_Volume_Lag_5", "PORT3.SA_Spread_Lag_1", "PORT3.SA_Spread_Lag_2", "PORT3.SA_Spread_Lag_3", "PORT3.SA_Spread_Lag_4", "PORT3.SA_Spread_Lag_5", "PORT3.SA_Volatilidade_Lag_1", "PORT3.SA_Volatilidade_Lag_2", "PORT3.SA_Volatilidade_Lag_3", "PORT3.SA_Volatilidade_Lag_4", "PORT3.SA_Volatilidade_Lag_5", "PORT3.SA_Parkinson_Volatility_Lag_1", "PORT3.<PERSON>_Parkinson_Volatility_Lag_2", "PORT3.SA_Parkinson_Volatility_Lag_3", "PORT3.SA_Parkinson_Volatility_Lag_4", "PORT3.<PERSON>_Parkinson_Volatility_Lag_5", "PORT3.SA_EMV_Lag_1", "PORT3.SA_EMV_Lag_2", "PORT3.SA_EMV_Lag_3", "PORT3.SA_EMV_Lag_4", "PORT3.SA_EMV_Lag_5", "PORT3.SA_EMV_MA_Lag_1", "PORT3.SA_EMV_MA_Lag_2", "PORT3.SA_EMV_MA_Lag_3", "PORT3.SA_EMV_MA_Lag_4", "PORT3.SA_EMV_MA_Lag_5", "PORT3.SA_VO_Lag_1", "PORT3.SA_VO_Lag_2", "PORT3.SA_VO_Lag_3", "PORT3.SA_VO_Lag_4", "PORT3.SA_VO_Lag_5", "PORT3.SA_MFI_Lag_1", "PORT3.SA_MFI_Lag_2", "PORT3.SA_MFI_Lag_3", "PORT3.SA_MFI_Lag_4", "PORT3.SA_MFI_Lag_5", "PORT3.SA_Amihud_Lag_1", "PORT3.SA_Amihud_Lag_2", "PORT3.SA_Amihud_Lag_3", "PORT3.SA_Amihud_Lag_4", "PORT3.SA_Amihud_Lag_5", "PORT3.SA_Roll_Spread_Lag_1", "PORT3.SA_Roll_Spread_Lag_2", "PORT3.SA_Roll_Spread_Lag_3", "PORT3.SA_Roll_Spread_Lag_4", "PORT3.SA_Roll_Spread_Lag_5", "PORT3.<PERSON>_Hu<PERSON>_Lag_1", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_2", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_3", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_4", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_5", "PORT3.SA_Vol_per_Volume_Lag_1", "PORT3.SA_Vol_per_Volume_Lag_2", "PORT3.SA_Vol_per_Volume_Lag_3", "PORT3.SA_Vol_per_Volume_Lag_4", "PORT3.SA_Vol_per_Volume_Lag_5", "PORT3.SA_CMF_Lag_1", "PORT3.SA_CMF_Lag_2", "PORT3.SA_CMF_Lag_3", "PORT3.SA_CMF_Lag_4", "PORT3.SA_CMF_Lag_5", "PORT3.SA_AD_Line_Lag_1", "PORT3.SA_AD_Line_Lag_2", "PORT3.SA_AD_Line_Lag_3", "PORT3.SA_AD_Line_Lag_4", "PORT3.SA_AD_Line_Lag_5", "ESPA3.SA_Media_OHLC", "ESPA3.SA_MFI_Historical", "ESPA3.SA_Amihud_Historical", "ESPA3.SA_Roll_Spread_Historical", "ESPA3.<PERSON>_<PERSON><PERSON>_Historical", "ESPA3.SA_Vol_per_Volume_Historical", "ESPA3.SA_CMF_Historical", "ESPA3.SA_AD_Line_Historical", "ESPA3.SA_Segunda", "ESPA3.SA_Terca", "ESPA3.SA_Quarta", "ESPA3.SA_Quinta", "ESPA3.SA_Sexta", "ESPA3.SA_Mes_1", "ESPA3.SA_Mes_2", "ESPA3.SA_Mes_3", "ESPA3.SA_Mes_4", "ESPA3.SA_Mes_5", "ESPA3.SA_Mes_6", "ESPA3.SA_Mes_7", "ESPA3.SA_Mes_8", "ESPA3.SA_Mes_9", "ESPA3.SA_Mes_10", "ESPA3.SA_Mes_11", "ESPA3.SA_Mes_12", "ESPA3.SA_Quarter_1", "ESPA3.SA_Quarter_2", "ESPA3.SA_Quarter_3", "ESPA3.SA_Quarter_4", "ESPA3.SA_Last_Day_Quarter", "ESPA3.SA_Pre_Feriado_Brasil", "ESPA3.SA_Sinal_Compra", "ESPA3.SA_Sinal_Venda", "ESPA3.SA_Media_OHLC_Futura", "ESPA3.SA_Media_OHLC_PctChange_Lag_1", "ESPA3.SA_Media_OHLC_PctChange_Lag_2", "ESPA3.SA_Media_OHLC_PctChange_Lag_3", "ESPA3.SA_Media_OHLC_PctChange_Lag_4", "ESPA3.SA_Media_OHLC_PctChange_Lag_5", "ESPA3.SA_Media_OHLC_PctChange_Lag_6", "ESPA3.SA_Media_OHLC_PctChange_Lag_7", "ESPA3.SA_Media_OHLC_PctChange_Lag_8", "ESPA3.SA_Media_OHLC_PctChange_Lag_9", "ESPA3.SA_Media_OHLC_PctChange_Lag_10", "ESPA3.SA_Volume_Lag_1", "ESPA3.SA_Volume_Lag_2", "ESPA3.SA_Volume_Lag_3", "ESPA3.SA_Volume_Lag_4", "ESPA3.SA_Volume_Lag_5", "ESPA3.SA_Spread_Lag_1", "ESPA3.SA_Spread_Lag_2", "ESPA3.SA_Spread_Lag_3", "ESPA3.SA_Spread_Lag_4", "ESPA3.SA_Spread_Lag_5", "ESPA3.SA_Volatilidade_Lag_1", "ESPA3.SA_Volatilidade_Lag_2", "ESPA3.SA_Volatilidade_Lag_3", "ESPA3.SA_Volatilidade_Lag_4", "ESPA3.SA_Volatilidade_Lag_5", "ESPA3.SA_Parkinson_Volatility_Lag_1", "ESPA3.SA_Parkinson_Volatility_Lag_2", "ESPA3.SA_Parkinson_Volatility_Lag_3", "ESPA3.SA_Parkinson_Volatility_Lag_4", "ESPA3.SA_Parkinson_Volatility_Lag_5", "ESPA3.SA_EMV_Lag_1", "ESPA3.SA_EMV_Lag_2", "ESPA3.SA_EMV_Lag_3", "ESPA3.SA_EMV_Lag_4", "ESPA3.SA_EMV_Lag_5", "ESPA3.SA_EMV_MA_Lag_1", "ESPA3.SA_EMV_MA_Lag_2", "ESPA3.SA_EMV_MA_Lag_3", "ESPA3.SA_EMV_MA_Lag_4", "ESPA3.SA_EMV_MA_Lag_5", "ESPA3.SA_VO_Lag_1", "ESPA3.SA_VO_Lag_2", "ESPA3.SA_VO_Lag_3", "ESPA3.SA_VO_Lag_4", "ESPA3.SA_VO_Lag_5", "ESPA3.SA_MFI_Lag_1", "ESPA3.SA_MFI_Lag_2", "ESPA3.SA_MFI_Lag_3", "ESPA3.SA_MFI_Lag_4", "ESPA3.SA_MFI_Lag_5", "ESPA3.SA_Amihud_Lag_1", "ESPA3.SA_Amihud_Lag_2", "ESPA3.SA_Amihud_Lag_3", "ESPA3.SA_Amihud_Lag_4", "ESPA3.SA_Amihud_Lag_5", "ESPA3.SA_Roll_Spread_Lag_1", "ESPA3.SA_Roll_Spread_Lag_2", "ESPA3.SA_Roll_Spread_Lag_3", "ESPA3.SA_Roll_Spread_Lag_4", "ESPA3.SA_Roll_Spread_Lag_5", "ESPA3.SA_Hurst_Lag_1", "ESPA3.<PERSON>_<PERSON><PERSON>_Lag_2", "ESPA3.<PERSON>_<PERSON><PERSON>_Lag_3", "ESPA3.<PERSON>_<PERSON><PERSON>_Lag_4", "ESPA3.<PERSON>_<PERSON><PERSON>_Lag_5", "ESPA3.SA_Vol_per_Volume_Lag_1", "ESPA3.SA_Vol_per_Volume_Lag_2", "ESPA3.SA_Vol_per_Volume_Lag_3", "ESPA3.SA_Vol_per_Volume_Lag_4", "ESPA3.SA_Vol_per_Volume_Lag_5", "ESPA3.SA_CMF_Lag_1", "ESPA3.SA_CMF_Lag_2", "ESPA3.SA_CMF_Lag_3", "ESPA3.SA_CMF_Lag_4", "ESPA3.SA_CMF_Lag_5", "ESPA3.SA_AD_Line_Lag_1", "ESPA3.SA_AD_Line_Lag_2", "ESPA3.SA_AD_Line_Lag_3", "ESPA3.SA_AD_Line_Lag_4", "ESPA3.SA_AD_Line_Lag_5", "PETR3.SA_Media_OHLC", "PETR3.SA_MFI_Historical", "PETR3.SA_Amihud_Historical", "PETR3.SA_Roll_Spread_Historical", "PETR3.SA_<PERSON><PERSON>_Historical", "PETR3.SA_Vol_per_Volume_Historical", "PETR3.SA_CMF_Historical", "PETR3.SA_AD_Line_Historical", "PETR3.SA_Segunda", "PETR3.SA_Terca", "PETR3.SA_Quarta", "PETR3.SA_Quinta", "PETR3.SA_Sexta", "PETR3.SA_Mes_1", "PETR3.SA_Mes_2", "PETR3.SA_Mes_3", "PETR3.SA_Mes_4", "PETR3.SA_Mes_5", "PETR3.SA_Mes_6", "PETR3.SA_Mes_7", "PETR3.SA_Mes_8", "PETR3.SA_Mes_9", "PETR3.SA_Mes_10", "PETR3.SA_Mes_11", "PETR3.SA_Mes_12", "PETR3.SA_Quarter_1", "PETR3.SA_Quarter_2", "PETR3.SA_Quarter_3", "PETR3.SA_Quarter_4", "PETR3.SA_Last_Day_Quarter", "PETR3.SA_Pre_Feriado_Brasil", "PETR3.SA_Sinal_Compra", "PETR3.SA_Sinal_Venda", "PETR3.SA_Media_OHLC_Futura", "PETR3.SA_Media_OHLC_PctChange_Lag_1", "PETR3.SA_Media_OHLC_PctChange_Lag_2", "PETR3.SA_Media_OHLC_PctChange_Lag_3", "PETR3.SA_Media_OHLC_PctChange_Lag_4", "PETR3.SA_Media_OHLC_PctChange_Lag_5", "PETR3.SA_Media_OHLC_PctChange_Lag_6", "PETR3.SA_Media_OHLC_PctChange_Lag_7", "PETR3.SA_Media_OHLC_PctChange_Lag_8", "PETR3.SA_Media_OHLC_PctChange_Lag_9", "PETR3.SA_Media_OHLC_PctChange_Lag_10", "PETR3.SA_Volume_Lag_1", "PETR3.SA_Volume_Lag_2", "PETR3.SA_Volume_Lag_3", "PETR3.SA_Volume_Lag_4", "PETR3.SA_Volume_Lag_5", "PETR3.SA_Spread_Lag_1", "PETR3.SA_Spread_Lag_2", "PETR3.SA_Spread_Lag_3", "PETR3.SA_Spread_Lag_4", "PETR3.SA_Spread_Lag_5", "PETR3.SA_Volatilidade_Lag_1", "PETR3.SA_Volatilidade_Lag_2", "PETR3.SA_Volatilidade_Lag_3", "PETR3.SA_Volatilidade_Lag_4", "PETR3.SA_Volatilidade_Lag_5", "PETR3.SA_Parkinson_Volatility_Lag_1", "PETR3.<PERSON>_Parkinson_Volatility_Lag_2", "PETR3.SA_Parkinson_Volatility_Lag_3", "PETR3.SA_Parkinson_Volatility_Lag_4", "PETR3.<PERSON>_Parkinson_Volatility_Lag_5", "PETR3.SA_EMV_Lag_1", "PETR3.SA_EMV_Lag_2", "PETR3.SA_EMV_Lag_3", "PETR3.SA_EMV_Lag_4", "PETR3.SA_EMV_Lag_5", "PETR3.SA_EMV_MA_Lag_1", "PETR3.SA_EMV_MA_Lag_2", "PETR3.SA_EMV_MA_Lag_3", "PETR3.SA_EMV_MA_Lag_4", "PETR3.SA_EMV_MA_Lag_5", "PETR3.SA_VO_Lag_1", "PETR3.SA_VO_Lag_2", "PETR3.SA_VO_Lag_3", "PETR3.SA_VO_Lag_4", "PETR3.SA_VO_Lag_5", "PETR3.SA_MFI_Lag_1", "PETR3.SA_MFI_Lag_2", "PETR3.SA_MFI_Lag_3", "PETR3.SA_MFI_Lag_4", "PETR3.SA_MFI_Lag_5", "PETR3.SA_Amihud_Lag_1", "PETR3.SA_Amihud_Lag_2", "PETR3.SA_Amihud_Lag_3", "PETR3.SA_Amihud_Lag_4", "PETR3.SA_Amihud_Lag_5", "PETR3.SA_Roll_Spread_Lag_1", "PETR3.SA_Roll_Spread_Lag_2", "PETR3.SA_Roll_Spread_Lag_3", "PETR3.SA_Roll_Spread_Lag_4", "PETR3.SA_Roll_Spread_Lag_5", "PETR3.<PERSON>_Hurst_Lag_1", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_2", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_3", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_4", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_5", "PETR3.SA_Vol_per_Volume_Lag_1", "PETR3.SA_Vol_per_Volume_Lag_2", "PETR3.SA_Vol_per_Volume_Lag_3", "PETR3.SA_Vol_per_Volume_Lag_4", "PETR3.SA_Vol_per_Volume_Lag_5", "PETR3.SA_CMF_Lag_1", "PETR3.SA_CMF_Lag_2", "PETR3.SA_CMF_Lag_3", "PETR3.SA_CMF_Lag_4", "PETR3.SA_CMF_Lag_5", "PETR3.SA_AD_Line_Lag_1", "PETR3.SA_AD_Line_Lag_2", "PETR3.SA_AD_Line_Lag_3", "PETR3.SA_AD_Line_Lag_4", "PETR3.SA_AD_Line_Lag_5", "VLID3.SA_Media_OHLC", "VLID3.SA_MFI_Historical", "VLID3.SA_Amihud_Historical", "VLID3.SA_Roll_Spread_Historical", "VLID3.SA_Hurst_Historical", "VLID3.SA_Vol_per_Volume_Historical", "VLID3.SA_CMF_Historical", "VLID3.SA_AD_Line_Historical", "VLID3.SA_Segunda", "VLID3.SA_Terca", "VLID3.SA_Quarta", "VLID3.SA_Quinta", "VLID3.SA_Sexta", "VLID3.SA_Mes_1", "VLID3.SA_Mes_2", "VLID3.SA_Mes_3", "VLID3.SA_Mes_4", "VLID3.SA_Mes_5", "VLID3.SA_Mes_6", "VLID3.SA_Mes_7", "VLID3.SA_Mes_8", "VLID3.SA_Mes_9", "VLID3.SA_Mes_10", "VLID3.SA_Mes_11", "VLID3.SA_Mes_12", "VLID3.SA_Quarter_1", "VLID3.SA_Quarter_2", "VLID3.SA_Quarter_3", "VLID3.SA_Quarter_4", "VLID3.SA_Last_Day_Quarter", "VLID3.SA_Pre_Feriado_Brasil", "VLID3.SA_Sinal_Compra", "VLID3.SA_Sinal_Venda", "VLID3.SA_Media_OHLC_Futura", "VLID3.SA_Media_OHLC_PctChange_Lag_1", "VLID3.SA_Media_OHLC_PctChange_Lag_2", "VLID3.SA_Media_OHLC_PctChange_Lag_3", "VLID3.SA_Media_OHLC_PctChange_Lag_4", "VLID3.SA_Media_OHLC_PctChange_Lag_5", "VLID3.SA_Media_OHLC_PctChange_Lag_6", "VLID3.SA_Media_OHLC_PctChange_Lag_7", "VLID3.SA_Media_OHLC_PctChange_Lag_8", "VLID3.SA_Media_OHLC_PctChange_Lag_9", "VLID3.SA_Media_OHLC_PctChange_Lag_10", "VLID3.SA_Volume_Lag_1", "VLID3.SA_Volume_Lag_2", "VLID3.SA_Volume_Lag_3", "VLID3.SA_Volume_Lag_4", "VLID3.SA_Volume_Lag_5", "VLID3.SA_Spread_Lag_1", "VLID3.SA_Spread_Lag_2", "VLID3.SA_Spread_Lag_3", "VLID3.SA_Spread_Lag_4", "VLID3.SA_Spread_Lag_5", "VLID3.SA_Volatilidade_Lag_1", "VLID3.SA_Volatilidade_Lag_2", "VLID3.SA_Volatilidade_Lag_3", "VLID3.SA_Volatilidade_Lag_4", "VLID3.SA_Volatilidade_Lag_5", "VLID3.SA_Parkinson_Volatility_Lag_1", "VLID3.SA_Parkinson_Volatility_Lag_2", "VLID3.SA_Parkinson_Volatility_Lag_3", "VLID3.SA_Parkinson_Volatility_Lag_4", "VLID3.SA_Parkinson_Volatility_Lag_5", "VLID3.SA_EMV_Lag_1", "VLID3.SA_EMV_Lag_2", "VLID3.SA_EMV_Lag_3", "VLID3.SA_EMV_Lag_4", "VLID3.SA_EMV_Lag_5", "VLID3.SA_EMV_MA_Lag_1", "VLID3.SA_EMV_MA_Lag_2", "VLID3.SA_EMV_MA_Lag_3", "VLID3.SA_EMV_MA_Lag_4", "VLID3.SA_EMV_MA_Lag_5", "VLID3.SA_VO_Lag_1", "VLID3.SA_VO_Lag_2", "VLID3.SA_VO_Lag_3", "VLID3.SA_VO_Lag_4", "VLID3.SA_VO_Lag_5", "VLID3.SA_MFI_Lag_1", "VLID3.SA_MFI_Lag_2", "VLID3.SA_MFI_Lag_3", "VLID3.SA_MFI_Lag_4", "VLID3.SA_MFI_Lag_5", "VLID3.SA_Amihud_Lag_1", "VLID3.SA_Amihud_Lag_2", "VLID3.SA_Amihud_Lag_3", "VLID3.SA_Amihud_Lag_4", "VLID3.SA_Amihud_Lag_5", "VLID3.SA_Roll_Spread_Lag_1", "VLID3.SA_Roll_Spread_Lag_2", "VLID3.SA_Roll_Spread_Lag_3", "VLID3.SA_Roll_Spread_Lag_4", "VLID3.SA_Roll_Spread_Lag_5", "VLID3.SA_Hurst_Lag_1", "VLID3.SA_Hurst_Lag_2", "VLID3.SA_Hurst_Lag_3", "VLID3.SA_Hurst_Lag_4", "VLID3.SA_Hurst_Lag_5", "VLID3.SA_Vol_per_Volume_Lag_1", "VLID3.SA_Vol_per_Volume_Lag_2", "VLID3.SA_Vol_per_Volume_Lag_3", "VLID3.SA_Vol_per_Volume_Lag_4", "VLID3.SA_Vol_per_Volume_Lag_5", "VLID3.SA_CMF_Lag_1", "VLID3.SA_CMF_Lag_2", "VLID3.SA_CMF_Lag_3", "VLID3.SA_CMF_Lag_4", "VLID3.SA_CMF_Lag_5", "VLID3.SA_AD_Line_Lag_1", "VLID3.SA_AD_Line_Lag_2", "VLID3.SA_AD_Line_Lag_3", "VLID3.SA_AD_Line_Lag_4", "VLID3.SA_AD_Line_Lag_5", "VALE3.SA_Media_OHLC", "VALE3.SA_MFI_Historical", "VALE3.SA_Amihud_Historical", "VALE3.SA_Roll_Spread_Historical", "VALE3.SA_<PERSON><PERSON>_Historical", "VALE3.SA_Vol_per_Volume_Historical", "VALE3.SA_CMF_Historical", "VALE3.SA_AD_Line_Historical", "VALE3.SA_Segunda", "VALE3.SA_Terca", "VALE3.SA_Quarta", "VALE3.SA_Quinta", "VALE3.SA_Sexta", "VALE3.SA_Mes_1", "VALE3.SA_Mes_2", "VALE3.SA_Mes_3", "VALE3.SA_Mes_4", "VALE3.SA_Mes_5", "VALE3.SA_Mes_6", "VALE3.SA_Mes_7", "VALE3.SA_Mes_8", "VALE3.SA_Mes_9", "VALE3.SA_Mes_10", "VALE3.SA_Mes_11", "VALE3.SA_Mes_12", "VALE3.SA_Quarter_1", "VALE3.SA_Quarter_2", "VALE3.SA_Quarter_3", "VALE3.SA_Quarter_4", "VALE3.SA_Last_Day_Quarter", "VALE3.SA_Pre_Feriado_Brasil", "VALE3.SA_Sinal_Compra", "VALE3.SA_Sinal_Venda", "VALE3.SA_Media_OHLC_Futura", "VALE3.SA_Media_OHLC_PctChange_Lag_1", "VALE3.SA_Media_OHLC_PctChange_Lag_2", "VALE3.SA_Media_OHLC_PctChange_Lag_3", "VALE3.SA_Media_OHLC_PctChange_Lag_4", "VALE3.SA_Media_OHLC_PctChange_Lag_5", "VALE3.SA_Media_OHLC_PctChange_Lag_6", "VALE3.SA_Media_OHLC_PctChange_Lag_7", "VALE3.SA_Media_OHLC_PctChange_Lag_8", "VALE3.SA_Media_OHLC_PctChange_Lag_9", "VALE3.SA_Media_OHLC_PctChange_Lag_10", "VALE3.SA_Volume_Lag_1", "VALE3.SA_Volume_Lag_2", "VALE3.SA_Volume_Lag_3", "VALE3.SA_Volume_Lag_4", "VALE3.SA_Volume_Lag_5", "VALE3.SA_Spread_Lag_1", "VALE3.SA_Spread_Lag_2", "VALE3.SA_Spread_Lag_3", "VALE3.SA_Spread_Lag_4", "VALE3.SA_Spread_Lag_5", "VALE3.SA_Volatilidade_Lag_1", "VALE3.SA_Volatilidade_Lag_2", "VALE3.SA_Volatilidade_Lag_3", "VALE3.SA_Volatilidade_Lag_4", "VALE3.SA_Volatilidade_Lag_5", "VALE3.SA_Parkinson_Volatility_Lag_1", "VALE3.SA_Parkinson_Volatility_Lag_2", "VALE3.SA_Parkinson_Volatility_Lag_3", "VALE3.SA_Parkinson_Volatility_Lag_4", "VALE3.SA_Parkinson_Volatility_Lag_5", "VALE3.SA_EMV_Lag_1", "VALE3.SA_EMV_Lag_2", "VALE3.SA_EMV_Lag_3", "VALE3.SA_EMV_Lag_4", "VALE3.SA_EMV_Lag_5", "VALE3.SA_EMV_MA_Lag_1", "VALE3.SA_EMV_MA_Lag_2", "VALE3.SA_EMV_MA_Lag_3", "VALE3.SA_EMV_MA_Lag_4", "VALE3.SA_EMV_MA_Lag_5", "VALE3.SA_VO_Lag_1", "VALE3.SA_VO_Lag_2", "VALE3.SA_VO_Lag_3", "VALE3.SA_VO_Lag_4", "VALE3.SA_VO_Lag_5", "VALE3.SA_MFI_Lag_1", "VALE3.SA_MFI_Lag_2", "VALE3.SA_MFI_Lag_3", "VALE3.SA_MFI_Lag_4", "VALE3.SA_MFI_Lag_5", "VALE3.SA_Amihud_Lag_1", "VALE3.SA_Amihud_Lag_2", "VALE3.SA_Amihud_Lag_3", "VALE3.SA_Amihud_Lag_4", "VALE3.SA_Amihud_Lag_5", "VALE3.SA_Roll_Spread_Lag_1", "VALE3.SA_Roll_Spread_Lag_2", "VALE3.SA_Roll_Spread_Lag_3", "VALE3.SA_Roll_Spread_Lag_4", "VALE3.SA_Roll_Spread_Lag_5", "VALE3.SA_Hurst_Lag_1", "VALE3.SA_Hurst_Lag_2", "VALE3.SA_Hurst_Lag_3", "VALE3.SA_<PERSON>rst_Lag_4", "VALE3.<PERSON>_<PERSON><PERSON>_Lag_5", "VALE3.SA_Vol_per_Volume_Lag_1", "VALE3.SA_Vol_per_Volume_Lag_2", "VALE3.SA_Vol_per_Volume_Lag_3", "VALE3.SA_Vol_per_Volume_Lag_4", "VALE3.SA_Vol_per_Volume_Lag_5", "VALE3.SA_CMF_Lag_1", "VALE3.SA_CMF_Lag_2", "VALE3.SA_CMF_Lag_3", "VALE3.SA_CMF_Lag_4", "VALE3.SA_CMF_Lag_5", "VALE3.SA_AD_Line_Lag_1", "VALE3.SA_AD_Line_Lag_2", "VALE3.SA_AD_Line_Lag_3", "VALE3.SA_AD_Line_Lag_4", "VALE3.SA_AD_Line_Lag_5", "LPSB3.SA_Media_OHLC", "LPSB3.SA_MFI_Historical", "LPSB3.SA_Amihud_Historical", "LPSB3.SA_Roll_Spread_Historical", "LPSB3.SA_Hurst_Historical", "LPSB3.SA_Vol_per_Volume_Historical", "LPSB3.SA_CMF_Historical", "LPSB3.SA_AD_Line_Historical", "LPSB3.SA_Segunda", "LPSB3.SA_Terca", "LPSB3.SA_Quarta", "LPSB3.SA_Quinta", "LPSB3.SA_Sexta", "LPSB3.SA_Mes_1", "LPSB3.SA_Mes_2", "LPSB3.SA_Mes_3", "LPSB3.SA_Mes_4", "LPSB3.SA_Mes_5", "LPSB3.SA_Mes_6", "LPSB3.SA_Mes_7", "LPSB3.SA_Mes_8", "LPSB3.SA_Mes_9", "LPSB3.SA_Mes_10", "LPSB3.SA_Mes_11", "LPSB3.SA_Mes_12", "LPSB3.SA_Quarter_1", "LPSB3.SA_Quarter_2", "LPSB3.SA_Quarter_3", "LPSB3.SA_Quarter_4", "LPSB3.SA_Last_Day_Quarter", "LPSB3.SA_Pre_Feriado_Brasil", "LPSB3.SA_Sinal_Compra", "LPSB3.SA_Sinal_Venda", "LPSB3.SA_Media_OHLC_Futura", "LPSB3.SA_Media_OHLC_PctChange_Lag_1", "LPSB3.SA_Media_OHLC_PctChange_Lag_2", "LPSB3.SA_Media_OHLC_PctChange_Lag_3", "LPSB3.SA_Media_OHLC_PctChange_Lag_4", "LPSB3.SA_Media_OHLC_PctChange_Lag_5", "LPSB3.SA_Media_OHLC_PctChange_Lag_6", "LPSB3.SA_Media_OHLC_PctChange_Lag_7", "LPSB3.SA_Media_OHLC_PctChange_Lag_8", "LPSB3.SA_Media_OHLC_PctChange_Lag_9", "LPSB3.SA_Media_OHLC_PctChange_Lag_10", "LPSB3.SA_Volume_Lag_1", "LPSB3.SA_Volume_Lag_2", "LPSB3.SA_Volume_Lag_3", "LPSB3.SA_Volume_Lag_4", "LPSB3.SA_Volume_Lag_5", "LPSB3.SA_Spread_Lag_1", "LPSB3.SA_Spread_Lag_2", "LPSB3.SA_Spread_Lag_3", "LPSB3.SA_Spread_Lag_4", "LPSB3.SA_Spread_Lag_5", "LPSB3.SA_Volatilidade_Lag_1", "LPSB3.SA_Volatilidade_Lag_2", "LPSB3.SA_Volatilidade_Lag_3", "LPSB3.SA_Volatilidade_Lag_4", "LPSB3.SA_Volatilidade_Lag_5", "LPSB3.SA_Parkinson_Volatility_Lag_1", "LPSB3.SA_Parkinson_Volatility_Lag_2", "LPSB3.SA_Parkinson_Volatility_Lag_3", "LPSB3.SA_Parkinson_Volatility_Lag_4", "LPSB3.SA_Parkinson_Volatility_Lag_5", "LPSB3.SA_EMV_Lag_1", "LPSB3.SA_EMV_Lag_2", "LPSB3.SA_EMV_Lag_3", "LPSB3.SA_EMV_Lag_4", "LPSB3.SA_EMV_Lag_5", "LPSB3.SA_EMV_MA_Lag_1", "LPSB3.SA_EMV_MA_Lag_2", "LPSB3.SA_EMV_MA_Lag_3", "LPSB3.SA_EMV_MA_Lag_4", "LPSB3.SA_EMV_MA_Lag_5", "LPSB3.SA_VO_Lag_1", "LPSB3.SA_VO_Lag_2", "LPSB3.SA_VO_Lag_3", "LPSB3.SA_VO_Lag_4", "LPSB3.SA_VO_Lag_5", "LPSB3.SA_MFI_Lag_1", "LPSB3.SA_MFI_Lag_2", "LPSB3.SA_MFI_Lag_3", "LPSB3.SA_MFI_Lag_4", "LPSB3.SA_MFI_Lag_5", "LPSB3.SA_Amihud_Lag_1", "LPSB3.SA_Amihud_Lag_2", "LPSB3.SA_Amihud_Lag_3", "LPSB3.SA_Amihud_Lag_4", "LPSB3.SA_Amihud_Lag_5", "LPSB3.SA_Roll_Spread_Lag_1", "LPSB3.SA_Roll_Spread_Lag_2", "LPSB3.SA_Roll_Spread_Lag_3", "LPSB3.SA_Roll_Spread_Lag_4", "LPSB3.SA_Roll_Spread_Lag_5", "LPSB3.SA_Hurst_Lag_1", "LPSB3.SA_Hurst_Lag_2", "LPSB3.SA_Hurst_Lag_3", "LPSB3.SA_Hurst_Lag_4", "LPSB3.SA_<PERSON>rst_Lag_5", "LPSB3.SA_Vol_per_Volume_Lag_1", "LPSB3.SA_Vol_per_Volume_Lag_2", "LPSB3.SA_Vol_per_Volume_Lag_3", "LPSB3.SA_Vol_per_Volume_Lag_4", "LPSB3.SA_Vol_per_Volume_Lag_5", "LPSB3.SA_CMF_Lag_1", "LPSB3.SA_CMF_Lag_2", "LPSB3.SA_CMF_Lag_3", "LPSB3.SA_CMF_Lag_4", "LPSB3.SA_CMF_Lag_5", "LPSB3.SA_AD_Line_Lag_1", "LPSB3.SA_AD_Line_Lag_2", "LPSB3.SA_AD_Line_Lag_3", "LPSB3.SA_AD_Line_Lag_4", "LPSB3.SA_AD_Line_Lag_5", "CSMG3.SA_Media_OHLC", "CSMG3.SA_MFI_Historical", "CSMG3.SA_Amihud_Historical", "CSMG3.SA_Roll_Spread_Historical", "CSMG3.SA_<PERSON><PERSON>_Historical", "CSMG3.SA_Vol_per_Volume_Historical", "CSMG3.SA_CMF_Historical", "CSMG3.SA_AD_Line_Historical", "CSMG3.SA_Segunda", "CSMG3.SA_Terca", "CSMG3.SA_Quarta", "CSMG3.SA_Quinta", "CSMG3.SA_Sexta", "CSMG3.SA_Mes_1", "CSMG3.SA_Mes_2", "CSMG3.SA_Mes_3", "CSMG3.SA_Mes_4", "CSMG3.SA_Mes_5", "CSMG3.SA_Mes_6", "CSMG3.SA_Mes_7", "CSMG3.SA_Mes_8", "CSMG3.SA_Mes_9", "CSMG3.SA_Mes_10", "CSMG3.SA_Mes_11", "CSMG3.SA_Mes_12", "CSMG3.SA_Quarter_1", "CSMG3.SA_Quarter_2", "CSMG3.SA_Quarter_3", "CSMG3.SA_Quarter_4", "CSMG3.SA_Last_Day_Quarter", "CSMG3.SA_Pre_Feriado_Brasil", "CSMG3.SA_Sinal_Compra", "CSMG3.SA_Sinal_Venda", "CSMG3.SA_Media_OHLC_Futura", "CSMG3.SA_Media_OHLC_PctChange_Lag_1", "CSMG3.SA_Media_OHLC_PctChange_Lag_2", "CSMG3.SA_Media_OHLC_PctChange_Lag_3", "CSMG3.SA_Media_OHLC_PctChange_Lag_4", "CSMG3.SA_Media_OHLC_PctChange_Lag_5", "CSMG3.SA_Media_OHLC_PctChange_Lag_6", "CSMG3.SA_Media_OHLC_PctChange_Lag_7", "CSMG3.SA_Media_OHLC_PctChange_Lag_8", "CSMG3.SA_Media_OHLC_PctChange_Lag_9", "CSMG3.SA_Media_OHLC_PctChange_Lag_10", "CSMG3.SA_Volume_Lag_1", "CSMG3.SA_Volume_Lag_2", "CSMG3.SA_Volume_Lag_3", "CSMG3.SA_Volume_Lag_4", "CSMG3.SA_Volume_Lag_5", "CSMG3.SA_Spread_Lag_1", "CSMG3.SA_Spread_Lag_2", "CSMG3.SA_Spread_Lag_3", "CSMG3.SA_Spread_Lag_4", "CSMG3.SA_Spread_Lag_5", "CSMG3.SA_Volatilidade_Lag_1", "CSMG3.SA_Volatilidade_Lag_2", "CSMG3.SA_Volatilidade_Lag_3", "CSMG3.SA_Volatilidade_Lag_4", "CSMG3.SA_Volatilidade_Lag_5", "CSMG3.SA_Parkinson_Volatility_Lag_1", "CSMG3.SA_Parkinson_Volatility_Lag_2", "CSMG3.SA_Parkinson_Volatility_Lag_3", "CSMG3.SA_Parkinson_Volatility_Lag_4", "CSMG3.SA_Parkinson_Volatility_Lag_5", "CSMG3.SA_EMV_Lag_1", "CSMG3.SA_EMV_Lag_2", "CSMG3.SA_EMV_Lag_3", "CSMG3.SA_EMV_Lag_4", "CSMG3.SA_EMV_Lag_5", "CSMG3.SA_EMV_MA_Lag_1", "CSMG3.SA_EMV_MA_Lag_2", "CSMG3.SA_EMV_MA_Lag_3", "CSMG3.SA_EMV_MA_Lag_4", "CSMG3.SA_EMV_MA_Lag_5", "CSMG3.SA_VO_Lag_1", "CSMG3.SA_VO_Lag_2", "CSMG3.SA_VO_Lag_3", "CSMG3.SA_VO_Lag_4", "CSMG3.SA_VO_Lag_5", "CSMG3.SA_MFI_Lag_1", "CSMG3.SA_MFI_Lag_2", "CSMG3.SA_MFI_Lag_3", "CSMG3.SA_MFI_Lag_4", "CSMG3.SA_MFI_Lag_5", "CSMG3.SA_Amihud_Lag_1", "CSMG3.SA_Amihud_Lag_2", "CSMG3.SA_Amihud_Lag_3", "CSMG3.SA_Amihud_Lag_4", "CSMG3.SA_Amihud_Lag_5", "CSMG3.SA_Roll_Spread_Lag_1", "CSMG3.SA_Roll_Spread_Lag_2", "CSMG3.SA_Roll_Spread_Lag_3", "CSMG3.SA_Roll_Spread_Lag_4", "CSMG3.SA_Roll_Spread_Lag_5", "CSMG3.SA_Hurst_Lag_1", "CSMG3.SA_Hurst_Lag_2", "CSMG3.SA_Hurst_Lag_3", "CSMG3.<PERSON>_<PERSON>rst_Lag_4", "CSMG3.<PERSON>_<PERSON><PERSON>_Lag_5", "CSMG3.SA_Vol_per_Volume_Lag_1", "CSMG3.SA_Vol_per_Volume_Lag_2", "CSMG3.SA_Vol_per_Volume_Lag_3", "CSMG3.SA_Vol_per_Volume_Lag_4", "CSMG3.SA_Vol_per_Volume_Lag_5", "CSMG3.SA_CMF_Lag_1", "CSMG3.SA_CMF_Lag_2", "CSMG3.SA_CMF_Lag_3", "CSMG3.SA_CMF_Lag_4", "CSMG3.SA_CMF_Lag_5", "CSMG3.SA_AD_Line_Lag_1", "CSMG3.SA_AD_Line_Lag_2", "CSMG3.SA_AD_Line_Lag_3", "CSMG3.SA_AD_Line_Lag_4", "CSMG3.SA_AD_Line_Lag_5", "NTCO3.SA_Media_OHLC", "NTCO3.SA_MFI_Historical", "NTCO3.SA_Amihud_Historical", "NTCO3.SA_Roll_Spread_Historical", "NTCO3.SA_<PERSON>rst_Historical", "NTCO3.SA_Vol_per_Volume_Historical", "NTCO3.SA_CMF_Historical", "NTCO3.SA_AD_Line_Historical", "NTCO3.SA_Segunda", "NTCO3.SA_Terca", "NTCO3.SA_Quarta", "NTCO3.SA_Quinta", "NTCO3.SA_Sexta", "NTCO3.SA_Mes_1", "NTCO3.SA_Mes_2", "NTCO3.SA_Mes_3", "NTCO3.SA_Mes_4", "NTCO3.SA_Mes_5", "NTCO3.SA_Mes_6", "NTCO3.SA_Mes_7", "NTCO3.SA_Mes_8", "NTCO3.SA_Mes_9", "NTCO3.SA_Mes_10", "NTCO3.SA_Mes_11", "NTCO3.SA_Mes_12", "NTCO3.SA_Quarter_1", "NTCO3.SA_Quarter_2", "NTCO3.SA_Quarter_3", "NTCO3.SA_Quarter_4", "NTCO3.SA_Last_Day_Quarter", "NTCO3.SA_Pre_Feriado_Brasil", "NTCO3.SA_Sinal_Compra", "NTCO3.SA_Sinal_Venda", "NTCO3.SA_Media_OHLC_Futura", "NTCO3.SA_Media_OHLC_PctChange_Lag_1", "NTCO3.SA_Media_OHLC_PctChange_Lag_2", "NTCO3.SA_Media_OHLC_PctChange_Lag_3", "NTCO3.SA_Media_OHLC_PctChange_Lag_4", "NTCO3.SA_Media_OHLC_PctChange_Lag_5", "NTCO3.SA_Media_OHLC_PctChange_Lag_6", "NTCO3.SA_Media_OHLC_PctChange_Lag_7", "NTCO3.SA_Media_OHLC_PctChange_Lag_8", "NTCO3.SA_Media_OHLC_PctChange_Lag_9", "NTCO3.SA_Media_OHLC_PctChange_Lag_10", "NTCO3.SA_Volume_Lag_1", "NTCO3.SA_Volume_Lag_2", "NTCO3.SA_Volume_Lag_3", "NTCO3.SA_Volume_Lag_4", "NTCO3.SA_Volume_Lag_5", "NTCO3.SA_Spread_Lag_1", "NTCO3.SA_Spread_Lag_2", "NTCO3.SA_Spread_Lag_3", "NTCO3.SA_Spread_Lag_4", "NTCO3.SA_Spread_Lag_5", "NTCO3.SA_Volatilidade_Lag_1", "NTCO3.SA_Volatilidade_Lag_2", "NTCO3.SA_Volatilidade_Lag_3", "NTCO3.SA_Volatilidade_Lag_4", "NTCO3.SA_Volatilidade_Lag_5", "NTCO3.SA_Parkinson_Volatility_Lag_1", "NTCO3.SA_Parkinson_Volatility_Lag_2", "NTCO3.SA_Parkinson_Volatility_Lag_3", "NTCO3.SA_Parkinson_Volatility_Lag_4", "NTCO3.SA_Parkinson_Volatility_Lag_5", "NTCO3.SA_EMV_Lag_1", "NTCO3.SA_EMV_Lag_2", "NTCO3.SA_EMV_Lag_3", "NTCO3.SA_EMV_Lag_4", "NTCO3.SA_EMV_Lag_5", "NTCO3.SA_EMV_MA_Lag_1", "NTCO3.SA_EMV_MA_Lag_2", "NTCO3.SA_EMV_MA_Lag_3", "NTCO3.SA_EMV_MA_Lag_4", "NTCO3.SA_EMV_MA_Lag_5", "NTCO3.SA_VO_Lag_1", "NTCO3.SA_VO_Lag_2", "NTCO3.SA_VO_Lag_3", "NTCO3.SA_VO_Lag_4", "NTCO3.SA_VO_Lag_5", "NTCO3.SA_MFI_Lag_1", "NTCO3.SA_MFI_Lag_2", "NTCO3.SA_MFI_Lag_3", "NTCO3.SA_MFI_Lag_4", "NTCO3.SA_MFI_Lag_5", "NTCO3.SA_Amihud_Lag_1", "NTCO3.SA_Amihud_Lag_2", "NTCO3.SA_Amihud_Lag_3", "NTCO3.SA_Amihud_Lag_4", "NTCO3.SA_Amihud_Lag_5", "NTCO3.SA_Roll_Spread_Lag_1", "NTCO3.SA_Roll_Spread_Lag_2", "NTCO3.SA_Roll_Spread_Lag_3", "NTCO3.SA_Roll_Spread_Lag_4", "NTCO3.SA_Roll_Spread_Lag_5", "NTCO3.SA_Hurst_Lag_1", "NTCO3.SA_Hurst_Lag_2", "NTCO3.SA_Hurst_Lag_3", "NTCO3.SA_<PERSON>rst_Lag_4", "NTCO3.<PERSON>_<PERSON><PERSON>_Lag_5", "NTCO3.SA_Vol_per_Volume_Lag_1", "NTCO3.SA_Vol_per_Volume_Lag_2", "NTCO3.SA_Vol_per_Volume_Lag_3", "NTCO3.SA_Vol_per_Volume_Lag_4", "NTCO3.SA_Vol_per_Volume_Lag_5", "NTCO3.SA_CMF_Lag_1", "NTCO3.SA_CMF_Lag_2", "NTCO3.SA_CMF_Lag_3", "NTCO3.SA_CMF_Lag_4", "NTCO3.SA_CMF_Lag_5", "NTCO3.SA_AD_Line_Lag_1", "NTCO3.SA_AD_Line_Lag_2", "NTCO3.SA_AD_Line_Lag_3", "NTCO3.SA_AD_Line_Lag_4", "NTCO3.SA_AD_Line_Lag_5", "BBSE3.SA_Media_OHLC", "BBSE3.SA_MFI_Historical", "BBSE3.SA_Amihud_Historical", "BBSE3.SA_Roll_Spread_Historical", "BBSE3.SA_<PERSON><PERSON>_Historical", "BBSE3.SA_Vol_per_Volume_Historical", "BBSE3.SA_CMF_Historical", "BBSE3.SA_AD_Line_Historical", "BBSE3.SA_Segunda", "BBSE3.SA_Terca", "BBSE3.SA_Quarta", "BBSE3.SA_Quinta", "BBSE3.SA_Sexta", "BBSE3.SA_Mes_1", "BBSE3.SA_Mes_2", "BBSE3.SA_Mes_3", "BBSE3.SA_Mes_4", "BBSE3.SA_Mes_5", "BBSE3.SA_Mes_6", "BBSE3.SA_Mes_7", "BBSE3.SA_Mes_8", "BBSE3.SA_Mes_9", "BBSE3.SA_Mes_10", "BBSE3.SA_Mes_11", "BBSE3.SA_Mes_12", "BBSE3.SA_Quarter_1", "BBSE3.SA_Quarter_2", "BBSE3.SA_Quarter_3", "BBSE3.SA_Quarter_4", "BBSE3.SA_Last_Day_Quarter", "BBSE3.SA_Pre_Feriado_Brasil", "BBSE3.SA_Sinal_Compra", "BBSE3.SA_Sinal_Venda", "BBSE3.SA_Media_OHLC_Futura", "BBSE3.SA_Media_OHLC_PctChange_Lag_1", "BBSE3.SA_Media_OHLC_PctChange_Lag_2", "BBSE3.SA_Media_OHLC_PctChange_Lag_3", "BBSE3.SA_Media_OHLC_PctChange_Lag_4", "BBSE3.SA_Media_OHLC_PctChange_Lag_5", "BBSE3.SA_Media_OHLC_PctChange_Lag_6", "BBSE3.SA_Media_OHLC_PctChange_Lag_7", "BBSE3.SA_Media_OHLC_PctChange_Lag_8", "BBSE3.SA_Media_OHLC_PctChange_Lag_9", "BBSE3.SA_Media_OHLC_PctChange_Lag_10", "BBSE3.SA_Volume_Lag_1", "BBSE3.SA_Volume_Lag_2", "BBSE3.SA_Volume_Lag_3", "BBSE3.SA_Volume_Lag_4", "BBSE3.SA_Volume_Lag_5", "BBSE3.SA_Spread_Lag_1", "BBSE3.SA_Spread_Lag_2", "BBSE3.SA_Spread_Lag_3", "BBSE3.SA_Spread_Lag_4", "BBSE3.SA_Spread_Lag_5", "BBSE3.SA_Volatilidade_Lag_1", "BBSE3.SA_Volatilidade_Lag_2", "BBSE3.SA_Volatilidade_Lag_3", "BBSE3.SA_Volatilidade_Lag_4", "BBSE3.SA_Volatilidade_Lag_5", "BBSE3.SA_Parkinson_Volatility_Lag_1", "BBSE3.SA_Parkinson_Volatility_Lag_2", "BBSE3.SA_Parkinson_Volatility_Lag_3", "BBSE3.SA_Parkinson_Volatility_Lag_4", "BBSE3.SA_Parkinson_Volatility_Lag_5", "BBSE3.SA_EMV_Lag_1", "BBSE3.SA_EMV_Lag_2", "BBSE3.SA_EMV_Lag_3", "BBSE3.SA_EMV_Lag_4", "BBSE3.SA_EMV_Lag_5", "BBSE3.SA_EMV_MA_Lag_1", "BBSE3.SA_EMV_MA_Lag_2", "BBSE3.SA_EMV_MA_Lag_3", "BBSE3.SA_EMV_MA_Lag_4", "BBSE3.SA_EMV_MA_Lag_5", "BBSE3.SA_VO_Lag_1", "BBSE3.SA_VO_Lag_2", "BBSE3.SA_VO_Lag_3", "BBSE3.SA_VO_Lag_4", "BBSE3.SA_VO_Lag_5", "BBSE3.SA_MFI_Lag_1", "BBSE3.SA_MFI_Lag_2", "BBSE3.SA_MFI_Lag_3", "BBSE3.SA_MFI_Lag_4", "BBSE3.SA_MFI_Lag_5", "BBSE3.SA_Amihud_Lag_1", "BBSE3.SA_Amihud_Lag_2", "BBSE3.SA_Amihud_Lag_3", "BBSE3.SA_Amihud_Lag_4", "BBSE3.SA_Amihud_Lag_5", "BBSE3.SA_Roll_Spread_Lag_1", "BBSE3.SA_Roll_Spread_Lag_2", "BBSE3.SA_Roll_Spread_Lag_3", "BBSE3.SA_Roll_Spread_Lag_4", "BBSE3.SA_Roll_Spread_Lag_5", "BBSE3.SA_Hurst_Lag_1", "BBSE3.SA_Hu<PERSON>_Lag_2", "BBSE3.SA_Hurst_Lag_3", "BBSE3.<PERSON>_<PERSON><PERSON>_Lag_4", "BBSE3.<PERSON>_<PERSON><PERSON>_Lag_5", "BBSE3.SA_Vol_per_Volume_Lag_1", "BBSE3.SA_Vol_per_Volume_Lag_2", "BBSE3.SA_Vol_per_Volume_Lag_3", "BBSE3.SA_Vol_per_Volume_Lag_4", "BBSE3.SA_Vol_per_Volume_Lag_5", "BBSE3.SA_CMF_Lag_1", "BBSE3.SA_CMF_Lag_2", "BBSE3.SA_CMF_Lag_3", "BBSE3.SA_CMF_Lag_4", "BBSE3.SA_CMF_Lag_5", "BBSE3.SA_AD_Line_Lag_1", "BBSE3.SA_AD_Line_Lag_2", "BBSE3.SA_AD_Line_Lag_3", "BBSE3.SA_AD_Line_Lag_4", "BBSE3.SA_AD_Line_Lag_5", "CMIG3.SA_Media_OHLC", "CMIG3.SA_MFI_Historical", "CMIG3.SA_Amihud_Historical", "CMIG3.SA_Roll_Spread_Historical", "CMIG3.<PERSON>_<PERSON><PERSON>_Historical", "CMIG3.SA_Vol_per_Volume_Historical", "CMIG3.SA_CMF_Historical", "CMIG3.SA_AD_Line_Historical", "CMIG3.SA_Segunda", "CMIG3.SA_Terca", "CMIG3.SA_Quarta", "CMIG3.SA_Quinta", "CMIG3.SA_Sexta", "CMIG3.SA_Mes_1", "CMIG3.SA_Mes_2", "CMIG3.SA_Mes_3", "CMIG3.SA_Mes_4", "CMIG3.SA_Mes_5", "CMIG3.SA_Mes_6", "CMIG3.SA_Mes_7", "CMIG3.SA_Mes_8", "CMIG3.SA_Mes_9", "CMIG3.SA_Mes_10", "CMIG3.SA_Mes_11", "CMIG3.SA_Mes_12", "CMIG3.SA_Quarter_1", "CMIG3.SA_Quarter_2", "CMIG3.SA_Quarter_3", "CMIG3.SA_Quarter_4", "CMIG3.SA_Last_Day_Quarter", "CMIG3.SA_Pre_Feriado_Brasil", "CMIG3.SA_Sinal_Compra", "CMIG3.SA_Sinal_Venda", "CMIG3.SA_Media_OHLC_Futura", "CMIG3.SA_Media_OHLC_PctChange_Lag_1", "CMIG3.SA_Media_OHLC_PctChange_Lag_2", "CMIG3.SA_Media_OHLC_PctChange_Lag_3", "CMIG3.SA_Media_OHLC_PctChange_Lag_4", "CMIG3.SA_Media_OHLC_PctChange_Lag_5", "CMIG3.SA_Media_OHLC_PctChange_Lag_6", "CMIG3.SA_Media_OHLC_PctChange_Lag_7", "CMIG3.SA_Media_OHLC_PctChange_Lag_8", "CMIG3.SA_Media_OHLC_PctChange_Lag_9", "CMIG3.SA_Media_OHLC_PctChange_Lag_10", "CMIG3.SA_Volume_Lag_1", "CMIG3.SA_Volume_Lag_2", "CMIG3.SA_Volume_Lag_3", "CMIG3.SA_Volume_Lag_4", "CMIG3.SA_Volume_Lag_5", "CMIG3.SA_Spread_Lag_1", "CMIG3.SA_Spread_Lag_2", "CMIG3.SA_Spread_Lag_3", "CMIG3.SA_Spread_Lag_4", "CMIG3.SA_Spread_Lag_5", "CMIG3.SA_Volatilidade_Lag_1", "CMIG3.SA_Volatilidade_Lag_2", "CMIG3.SA_Volatilidade_Lag_3", "CMIG3.SA_Volatilidade_Lag_4", "CMIG3.SA_Volatilidade_Lag_5", "CMIG3.SA_Parkinson_Volatility_Lag_1", "CMIG3.<PERSON>_Parkinson_Volatility_Lag_2", "CMIG3.SA_Parkinson_Volatility_Lag_3", "CMIG3.SA_Parkinson_Volatility_Lag_4", "CMIG3.<PERSON>_Parkinson_Volatility_Lag_5", "CMIG3.SA_EMV_Lag_1", "CMIG3.SA_EMV_Lag_2", "CMIG3.SA_EMV_Lag_3", "CMIG3.SA_EMV_Lag_4", "CMIG3.SA_EMV_Lag_5", "CMIG3.SA_EMV_MA_Lag_1", "CMIG3.SA_EMV_MA_Lag_2", "CMIG3.SA_EMV_MA_Lag_3", "CMIG3.SA_EMV_MA_Lag_4", "CMIG3.SA_EMV_MA_Lag_5", "CMIG3.SA_VO_Lag_1", "CMIG3.SA_VO_Lag_2", "CMIG3.SA_VO_Lag_3", "CMIG3.SA_VO_Lag_4", "CMIG3.SA_VO_Lag_5", "CMIG3.SA_MFI_Lag_1", "CMIG3.SA_MFI_Lag_2", "CMIG3.SA_MFI_Lag_3", "CMIG3.SA_MFI_Lag_4", "CMIG3.SA_MFI_Lag_5", "CMIG3.SA_Amihud_Lag_1", "CMIG3.SA_Amihud_Lag_2", "CMIG3.SA_Amihud_Lag_3", "CMIG3.SA_Amihud_Lag_4", "CMIG3.SA_Amihud_Lag_5", "CMIG3.SA_Roll_Spread_Lag_1", "CMIG3.SA_Roll_Spread_Lag_2", "CMIG3.SA_Roll_Spread_Lag_3", "CMIG3.SA_Roll_Spread_Lag_4", "CMIG3.SA_Roll_Spread_Lag_5", "CMIG3.SA_Hurst_Lag_1", "CMIG3.<PERSON>_Hu<PERSON>_Lag_2", "CMIG3.<PERSON>_Hurst_Lag_3", "CMIG3.<PERSON>_<PERSON><PERSON>_Lag_4", "CMIG3.<PERSON>_<PERSON><PERSON>_Lag_5", "CMIG3.SA_Vol_per_Volume_Lag_1", "CMIG3.SA_Vol_per_Volume_Lag_2", "CMIG3.SA_Vol_per_Volume_Lag_3", "CMIG3.SA_Vol_per_Volume_Lag_4", "CMIG3.SA_Vol_per_Volume_Lag_5", "CMIG3.SA_CMF_Lag_1", "CMIG3.SA_CMF_Lag_2", "CMIG3.SA_CMF_Lag_3", "CMIG3.SA_CMF_Lag_4", "CMIG3.SA_CMF_Lag_5", "CMIG3.SA_AD_Line_Lag_1", "CMIG3.SA_AD_Line_Lag_2", "CMIG3.SA_AD_Line_Lag_3", "CMIG3.SA_AD_Line_Lag_4", "CMIG3.SA_AD_Line_Lag_5", "GGBR4.SA_Media_OHLC", "GGBR4.SA_MFI_Historical", "GGBR4.SA_Amihud_Historical", "GGBR4.SA_Roll_Spread_Historical", "GGBR4.SA_Hurst_Historical", "GGBR4.SA_Vol_per_Volume_Historical", "GGBR4.SA_CMF_Historical", "GGBR4.SA_AD_Line_Historical", "GGBR4.SA_Segunda", "GGBR4.SA_Terca", "GGBR4.SA_Quarta", "GGBR4.SA_Quinta", "GGBR4.SA_Sexta", "GGBR4.SA_Mes_1", "GGBR4.SA_Mes_2", "GGBR4.SA_Mes_3", "GGBR4.SA_Mes_4", "GGBR4.SA_Mes_5", "GGBR4.SA_Mes_6", "GGBR4.SA_Mes_7", "GGBR4.SA_Mes_8", "GGBR4.SA_Mes_9", "GGBR4.SA_Mes_10", "GGBR4.SA_Mes_11", "GGBR4.SA_Mes_12", "GGBR4.SA_Quarter_1", "GGBR4.SA_Quarter_2", "GGBR4.SA_Quarter_3", "GGBR4.SA_Quarter_4", "GGBR4.SA_Last_Day_Quarter", "GGBR4.SA_Pre_Feriado_Brasil", "GGBR4.SA_Sinal_Compra", "GGBR4.SA_Sinal_Venda", "GGBR4.SA_Media_OHLC_Futura", "GGBR4.SA_Media_OHLC_PctChange_Lag_1", "GGBR4.SA_Media_OHLC_PctChange_Lag_2", "GGBR4.SA_Media_OHLC_PctChange_Lag_3", "GGBR4.SA_Media_OHLC_PctChange_Lag_4", "GGBR4.SA_Media_OHLC_PctChange_Lag_5", "GGBR4.SA_Media_OHLC_PctChange_Lag_6", "GGBR4.SA_Media_OHLC_PctChange_Lag_7", "GGBR4.SA_Media_OHLC_PctChange_Lag_8", "GGBR4.SA_Media_OHLC_PctChange_Lag_9", "GGBR4.SA_Media_OHLC_PctChange_Lag_10", "GGBR4.SA_Volume_Lag_1", "GGBR4.SA_Volume_Lag_2", "GGBR4.SA_Volume_Lag_3", "GGBR4.SA_Volume_Lag_4", "GGBR4.SA_Volume_Lag_5", "GGBR4.SA_Spread_Lag_1", "GGBR4.SA_Spread_Lag_2", "GGBR4.SA_Spread_Lag_3", "GGBR4.SA_Spread_Lag_4", "GGBR4.SA_Spread_Lag_5", "GGBR4.SA_Volatilidade_Lag_1", "GGBR4.SA_Volatilidade_Lag_2", "GGBR4.SA_Volatilidade_Lag_3", "GGBR4.SA_Volatilidade_Lag_4", "GGBR4.SA_Volatilidade_Lag_5", "GGBR4.SA_Parkinson_Volatility_Lag_1", "GGBR4.SA_Parkinson_Volatility_Lag_2", "GGBR4.SA_Parkinson_Volatility_Lag_3", "GGBR4.SA_Parkinson_Volatility_Lag_4", "GGBR4.SA_Parkinson_Volatility_Lag_5", "GGBR4.SA_EMV_Lag_1", "GGBR4.SA_EMV_Lag_2", "GGBR4.SA_EMV_Lag_3", "GGBR4.SA_EMV_Lag_4", "GGBR4.SA_EMV_Lag_5", "GGBR4.SA_EMV_MA_Lag_1", "GGBR4.SA_EMV_MA_Lag_2", "GGBR4.SA_EMV_MA_Lag_3", "GGBR4.SA_EMV_MA_Lag_4", "GGBR4.SA_EMV_MA_Lag_5", "GGBR4.SA_VO_Lag_1", "GGBR4.SA_VO_Lag_2", "GGBR4.SA_VO_Lag_3", "GGBR4.SA_VO_Lag_4", "GGBR4.SA_VO_Lag_5", "GGBR4.SA_MFI_Lag_1", "GGBR4.SA_MFI_Lag_2", "GGBR4.SA_MFI_Lag_3", "GGBR4.SA_MFI_Lag_4", "GGBR4.SA_MFI_Lag_5", "GGBR4.SA_Amihud_Lag_1", "GGBR4.SA_Amihud_Lag_2", "GGBR4.SA_Amihud_Lag_3", "GGBR4.SA_Amihud_Lag_4", "GGBR4.SA_Amihud_Lag_5", "GGBR4.SA_Roll_Spread_Lag_1", "GGBR4.SA_Roll_Spread_Lag_2", "GGBR4.SA_Roll_Spread_Lag_3", "GGBR4.SA_Roll_Spread_Lag_4", "GGBR4.SA_Roll_Spread_Lag_5", "GGBR4.SA_Hurst_Lag_1", "GGBR4.<PERSON>_<PERSON>rst_Lag_2", "GGBR4.SA_<PERSON>rst_Lag_3", "GGBR4.<PERSON>_<PERSON>rst_Lag_4", "GGBR4.<PERSON>_<PERSON><PERSON>_Lag_5", "GGBR4.SA_Vol_per_Volume_Lag_1", "GGBR4.SA_Vol_per_Volume_Lag_2", "GGBR4.SA_Vol_per_Volume_Lag_3", "GGBR4.SA_Vol_per_Volume_Lag_4", "GGBR4.SA_Vol_per_Volume_Lag_5", "GGBR4.SA_CMF_Lag_1", "GGBR4.SA_CMF_Lag_2", "GGBR4.SA_CMF_Lag_3", "GGBR4.SA_CMF_Lag_4", "GGBR4.SA_CMF_Lag_5", "GGBR4.SA_AD_Line_Lag_1", "GGBR4.SA_AD_Line_Lag_2", "GGBR4.SA_AD_Line_Lag_3", "GGBR4.SA_AD_Line_Lag_4", "GGBR4.SA_AD_Line_Lag_5", "RADL3.SA_Media_OHLC", "RADL3.SA_MFI_Historical", "RADL3.SA_Amihud_Historical", "RADL3.SA_Roll_Spread_Historical", "RADL3.<PERSON>_<PERSON><PERSON>_Historical", "RADL3.SA_Vol_per_Volume_Historical", "RADL3.SA_CMF_Historical", "RADL3.SA_AD_Line_Historical", "RADL3.SA_Segunda", "RADL3.SA_Terca", "RADL3.SA_Quarta", "RADL3.SA_Quinta", "RADL3.SA_Sexta", "RADL3.SA_Mes_1", "RADL3.SA_Mes_2", "RADL3.SA_Mes_3", "RADL3.SA_Mes_4", "RADL3.SA_Mes_5", "RADL3.SA_Mes_6", "RADL3.SA_Mes_7", "RADL3.SA_Mes_8", "RADL3.SA_Mes_9", "RADL3.SA_Mes_10", "RADL3.SA_Mes_11", "RADL3.SA_Mes_12", "RADL3.SA_Quarter_1", "RADL3.SA_Quarter_2", "RADL3.SA_Quarter_3", "RADL3.SA_Quarter_4", "RADL3.SA_Last_Day_Quarter", "RADL3.SA_Pre_Feriado_Brasil", "RADL3.SA_Sinal_Compra", "RADL3.SA_Sinal_Venda", "RADL3.SA_Media_OHLC_Futura", "RADL3.SA_Media_OHLC_PctChange_Lag_1", "RADL3.SA_Media_OHLC_PctChange_Lag_2", "RADL3.SA_Media_OHLC_PctChange_Lag_3", "RADL3.SA_Media_OHLC_PctChange_Lag_4", "RADL3.SA_Media_OHLC_PctChange_Lag_5", "RADL3.SA_Media_OHLC_PctChange_Lag_6", "RADL3.SA_Media_OHLC_PctChange_Lag_7", "RADL3.SA_Media_OHLC_PctChange_Lag_8", "RADL3.SA_Media_OHLC_PctChange_Lag_9", "RADL3.SA_Media_OHLC_PctChange_Lag_10", "RADL3.SA_Volume_Lag_1", "RADL3.SA_Volume_Lag_2", "RADL3.SA_Volume_Lag_3", "RADL3.SA_Volume_Lag_4", "RADL3.SA_Volume_Lag_5", "RADL3.SA_Spread_Lag_1", "RADL3.SA_Spread_Lag_2", "RADL3.SA_Spread_Lag_3", "RADL3.SA_Spread_Lag_4", "RADL3.SA_Spread_Lag_5", "RADL3.SA_Volatilidade_Lag_1", "RADL3.SA_Volatilidade_Lag_2", "RADL3.SA_Volatilidade_Lag_3", "RADL3.SA_Volatilidade_Lag_4", "RADL3.SA_Volatilidade_Lag_5", "RADL3.<PERSON>_Parkinson_Volatility_Lag_1", "RADL3.<PERSON>_Parkinson_Volatility_Lag_2", "RADL3.<PERSON>_Parkinson_Volatility_Lag_3", "RADL3.<PERSON>_Parkinson_Volatility_Lag_4", "RADL3.<PERSON>_Parkinson_Volatility_Lag_5", "RADL3.SA_EMV_Lag_1", "RADL3.SA_EMV_Lag_2", "RADL3.SA_EMV_Lag_3", "RADL3.SA_EMV_Lag_4", "RADL3.SA_EMV_Lag_5", "RADL3.SA_EMV_MA_Lag_1", "RADL3.SA_EMV_MA_Lag_2", "RADL3.SA_EMV_MA_Lag_3", "RADL3.SA_EMV_MA_Lag_4", "RADL3.SA_EMV_MA_Lag_5", "RADL3.SA_VO_Lag_1", "RADL3.SA_VO_Lag_2", "RADL3.SA_VO_Lag_3", "RADL3.SA_VO_Lag_4", "RADL3.SA_VO_Lag_5", "RADL3.SA_MFI_Lag_1", "RADL3.SA_MFI_Lag_2", "RADL3.SA_MFI_Lag_3", "RADL3.SA_MFI_Lag_4", "RADL3.SA_MFI_Lag_5", "RADL3.SA_Amihud_Lag_1", "RADL3.SA_Amihud_Lag_2", "RADL3.SA_Amihud_Lag_3", "RADL3.SA_Amihud_Lag_4", "RADL3.SA_Amihud_Lag_5", "RADL3.SA_Roll_Spread_Lag_1", "RADL3.SA_Roll_Spread_Lag_2", "RADL3.SA_Roll_Spread_Lag_3", "RADL3.SA_Roll_Spread_Lag_4", "RADL3.SA_Roll_Spread_Lag_5", "RADL3.<PERSON>_Hu<PERSON>_Lag_1", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_2", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_3", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_4", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_5", "RADL3.SA_Vol_per_Volume_Lag_1", "RADL3.SA_Vol_per_Volume_Lag_2", "RADL3.SA_Vol_per_Volume_Lag_3", "RADL3.SA_Vol_per_Volume_Lag_4", "RADL3.SA_Vol_per_Volume_Lag_5", "RADL3.SA_CMF_Lag_1", "RADL3.SA_CMF_Lag_2", "RADL3.SA_CMF_Lag_3", "RADL3.SA_CMF_Lag_4", "RADL3.SA_CMF_Lag_5", "RADL3.SA_AD_Line_Lag_1", "RADL3.SA_AD_Line_Lag_2", "RADL3.SA_AD_Line_Lag_3", "RADL3.SA_AD_Line_Lag_4", "RADL3.SA_AD_Line_Lag_5", "KLBN11.SA_Media_OHLC", "KLBN11.SA_MFI_Historical", "KLBN11.SA_Amihud_Historical", "KLBN11.SA_Roll_Spread_Historical", "KLBN11.SA_Hurst_Historical", "KLBN11.SA_Vol_per_Volume_Historical", "KLBN11.SA_CMF_Historical", "KLBN11.SA_AD_Line_Historical", "KLBN11.SA_Segunda", "KLBN11.SA_Terca", "KLBN11.SA_Quarta", "KLBN11.SA_Quinta", "KLBN11.SA_Sexta", "KLBN11.SA_Mes_1", "KLBN11.SA_Mes_2", "KLBN11.SA_Mes_3", "KLBN11.SA_Mes_4", "KLBN11.SA_Mes_5", "KLBN11.SA_Mes_6", "KLBN11.SA_Mes_7", "KLBN11.SA_Mes_8", "KLBN11.SA_Mes_9", "KLBN11.SA_Mes_10", "KLBN11.SA_Mes_11", "KLBN11.SA_Mes_12", "KLBN11.SA_Quarter_1", "KLBN11.SA_Quarter_2", "KLBN11.SA_Quarter_3", "KLBN11.SA_Quarter_4", "KLBN11.SA_Last_Day_Quarter", "KLBN11.SA_Pre_Feriado_Brasil", "KLBN11.SA_Sinal_Compra", "KLBN11.SA_Sinal_Venda", "KLBN11.SA_Media_OHLC_Futura", "KLBN11.SA_Media_OHLC_PctChange_Lag_1", "KLBN11.SA_Media_OHLC_PctChange_Lag_2", "KLBN11.SA_Media_OHLC_PctChange_Lag_3", "KLBN11.SA_Media_OHLC_PctChange_Lag_4", "KLBN11.SA_Media_OHLC_PctChange_Lag_5", "KLBN11.SA_Media_OHLC_PctChange_Lag_6", "KLBN11.SA_Media_OHLC_PctChange_Lag_7", "KLBN11.SA_Media_OHLC_PctChange_Lag_8", "KLBN11.SA_Media_OHLC_PctChange_Lag_9", "KLBN11.SA_Media_OHLC_PctChange_Lag_10", "KLBN11.SA_Volume_Lag_1", "KLBN11.SA_Volume_Lag_2", "KLBN11.SA_Volume_Lag_3", "KLBN11.SA_Volume_Lag_4", "KLBN11.SA_Volume_Lag_5", "KLBN11.SA_Spread_Lag_1", "KLBN11.SA_Spread_Lag_2", "KLBN11.SA_Spread_Lag_3", "KLBN11.SA_Spread_Lag_4", "KLBN11.SA_Spread_Lag_5", "KLBN11.SA_Volatilidade_Lag_1", "KLBN11.SA_Volatilidade_Lag_2", "KLBN11.SA_Volatilidade_Lag_3", "KLBN11.SA_Volatilidade_Lag_4", "KLBN11.SA_Volatilidade_Lag_5", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_1", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_2", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_3", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_4", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_5", "KLBN11.SA_EMV_Lag_1", "KLBN11.SA_EMV_Lag_2", "KLBN11.SA_EMV_Lag_3", "KLBN11.SA_EMV_Lag_4", "KLBN11.SA_EMV_Lag_5", "KLBN11.SA_EMV_MA_Lag_1", "KLBN11.SA_EMV_MA_Lag_2", "KLBN11.SA_EMV_MA_Lag_3", "KLBN11.SA_EMV_MA_Lag_4", "KLBN11.SA_EMV_MA_Lag_5", "KLBN11.SA_VO_Lag_1", "KLBN11.SA_VO_Lag_2", "KLBN11.SA_VO_Lag_3", "KLBN11.SA_VO_Lag_4", "KLBN11.SA_VO_Lag_5", "KLBN11.SA_MFI_Lag_1", "KLBN11.SA_MFI_Lag_2", "KLBN11.SA_MFI_Lag_3", "KLBN11.SA_MFI_Lag_4", "KLBN11.SA_MFI_Lag_5", "KLBN11.SA_Amihud_Lag_1", "KLBN11.SA_Amihud_Lag_2", "KLBN11.SA_Amihud_Lag_3", "KLBN11.SA_Amihud_Lag_4", "KLBN11.SA_Amihud_Lag_5", "KLBN11.SA_Roll_Spread_Lag_1", "KLBN11.SA_Roll_Spread_Lag_2", "KLBN11.SA_Roll_Spread_Lag_3", "KLBN11.SA_Roll_Spread_Lag_4", "KLBN11.SA_Roll_Spread_Lag_5", "KLBN11.SA_Hurst_Lag_1", "KLBN11.SA_Hurst_Lag_2", "KLBN11.SA_Hurst_Lag_3", "KLBN11.SA_Hurst_Lag_4", "KLBN11.<PERSON>_Hurst_Lag_5", "KLBN11.SA_Vol_per_Volume_Lag_1", "KLBN11.SA_Vol_per_Volume_Lag_2", "KLBN11.SA_Vol_per_Volume_Lag_3", "KLBN11.SA_Vol_per_Volume_Lag_4", "KLBN11.SA_Vol_per_Volume_Lag_5", "KLBN11.SA_CMF_Lag_1", "KLBN11.SA_CMF_Lag_2", "KLBN11.SA_CMF_Lag_3", "KLBN11.SA_CMF_Lag_4", "KLBN11.SA_CMF_Lag_5", "KLBN11.SA_AD_Line_Lag_1", "KLBN11.SA_AD_Line_Lag_2", "KLBN11.SA_AD_Line_Lag_3", "KLBN11.SA_AD_Line_Lag_4", "KLBN11.SA_AD_Line_Lag_5", "ABEV3.SA_Media_OHLC", "ABEV3.SA_MFI_Historical", "ABEV3.SA_Amihud_Historical", "ABEV3.SA_Roll_Spread_Historical", "ABEV3.SA_<PERSON><PERSON>_Historical", "ABEV3.SA_Vol_per_Volume_Historical", "ABEV3.SA_CMF_Historical", "ABEV3.SA_AD_Line_Historical", "ABEV3.SA_Segunda", "ABEV3.SA_Terca", "ABEV3.SA_Quarta", "ABEV3.SA_Quinta", "ABEV3.SA_Sexta", "ABEV3.SA_Mes_1", "ABEV3.SA_Mes_2", "ABEV3.SA_Mes_3", "ABEV3.SA_Mes_4", "ABEV3.SA_Mes_5", "ABEV3.SA_Mes_6", "ABEV3.SA_Mes_7", "ABEV3.SA_Mes_8", "ABEV3.SA_Mes_9", "ABEV3.SA_Mes_10", "ABEV3.SA_Mes_11", "ABEV3.SA_Mes_12", "ABEV3.SA_Quarter_1", "ABEV3.SA_Quarter_2", "ABEV3.SA_Quarter_3", "ABEV3.SA_Quarter_4", "ABEV3.SA_Last_Day_Quarter", "ABEV3.SA_Pre_Feriado_Brasil", "ABEV3.SA_Sinal_Compra", "ABEV3.SA_Sinal_Venda", "ABEV3.SA_Media_OHLC_Futura", "ABEV3.SA_Media_OHLC_PctChange_Lag_1", "ABEV3.SA_Media_OHLC_PctChange_Lag_2", "ABEV3.SA_Media_OHLC_PctChange_Lag_3", "ABEV3.SA_Media_OHLC_PctChange_Lag_4", "ABEV3.SA_Media_OHLC_PctChange_Lag_5", "ABEV3.SA_Media_OHLC_PctChange_Lag_6", "ABEV3.SA_Media_OHLC_PctChange_Lag_7", "ABEV3.SA_Media_OHLC_PctChange_Lag_8", "ABEV3.SA_Media_OHLC_PctChange_Lag_9", "ABEV3.SA_Media_OHLC_PctChange_Lag_10", "ABEV3.SA_Volume_Lag_1", "ABEV3.SA_Volume_Lag_2", "ABEV3.SA_Volume_Lag_3", "ABEV3.SA_Volume_Lag_4", "ABEV3.SA_Volume_Lag_5", "ABEV3.SA_Spread_Lag_1", "ABEV3.SA_Spread_Lag_2", "ABEV3.SA_Spread_Lag_3", "ABEV3.SA_Spread_Lag_4", "ABEV3.SA_Spread_Lag_5", "ABEV3.SA_Volatilidade_Lag_1", "ABEV3.SA_Volatilidade_Lag_2", "ABEV3.SA_Volatilidade_Lag_3", "ABEV3.SA_Volatilidade_Lag_4", "ABEV3.SA_Volatilidade_Lag_5", "ABEV3.SA_Parkinson_Volatility_Lag_1", "ABEV3.SA_Parkinson_Volatility_Lag_2", "ABEV3.SA_Parkinson_Volatility_Lag_3", "ABEV3.SA_Parkinson_Volatility_Lag_4", "ABEV3.SA_Parkinson_Volatility_Lag_5", "ABEV3.SA_EMV_Lag_1", "ABEV3.SA_EMV_Lag_2", "ABEV3.SA_EMV_Lag_3", "ABEV3.SA_EMV_Lag_4", "ABEV3.SA_EMV_Lag_5", "ABEV3.SA_EMV_MA_Lag_1", "ABEV3.SA_EMV_MA_Lag_2", "ABEV3.SA_EMV_MA_Lag_3", "ABEV3.SA_EMV_MA_Lag_4", "ABEV3.SA_EMV_MA_Lag_5", "ABEV3.SA_VO_Lag_1", "ABEV3.SA_VO_Lag_2", "ABEV3.SA_VO_Lag_3", "ABEV3.SA_VO_Lag_4", "ABEV3.SA_VO_Lag_5", "ABEV3.SA_MFI_Lag_1", "ABEV3.SA_MFI_Lag_2", "ABEV3.SA_MFI_Lag_3", "ABEV3.SA_MFI_Lag_4", "ABEV3.SA_MFI_Lag_5", "ABEV3.SA_Amihud_Lag_1", "ABEV3.SA_Amihud_Lag_2", "ABEV3.SA_Amihud_Lag_3", "ABEV3.SA_Amihud_Lag_4", "ABEV3.SA_Amihud_Lag_5", "ABEV3.SA_Roll_Spread_Lag_1", "ABEV3.SA_Roll_Spread_Lag_2", "ABEV3.SA_Roll_Spread_Lag_3", "ABEV3.SA_Roll_Spread_Lag_4", "ABEV3.SA_Roll_Spread_Lag_5", "ABEV3.SA_Hurst_Lag_1", "ABEV3.SA_Hurst_Lag_2", "ABEV3.SA_Hurst_Lag_3", "ABEV3.<PERSON>_<PERSON><PERSON>_Lag_4", "ABEV3.<PERSON>_<PERSON><PERSON>_Lag_5", "ABEV3.SA_Vol_per_Volume_Lag_1", "ABEV3.SA_Vol_per_Volume_Lag_2", "ABEV3.SA_Vol_per_Volume_Lag_3", "ABEV3.SA_Vol_per_Volume_Lag_4", "ABEV3.SA_Vol_per_Volume_Lag_5", "ABEV3.SA_CMF_Lag_1", "ABEV3.SA_CMF_Lag_2", "ABEV3.SA_CMF_Lag_3", "ABEV3.SA_CMF_Lag_4", "ABEV3.SA_CMF_Lag_5", "ABEV3.SA_AD_Line_Lag_1", "ABEV3.SA_AD_Line_Lag_2", "ABEV3.SA_AD_Line_Lag_3", "ABEV3.SA_AD_Line_Lag_4", "ABEV3.SA_AD_Line_Lag_5", "ODPV3.SA_Media_OHLC", "ODPV3.SA_MFI_Historical", "ODPV3.SA_Amihud_Historical", "ODPV3.SA_Roll_Spread_Historical", "ODPV3.SA_Hurst_Historical", "ODPV3.SA_Vol_per_Volume_Historical", "ODPV3.SA_CMF_Historical", "ODPV3.SA_AD_Line_Historical", "ODPV3.SA_Segunda", "ODPV3.SA_Terca", "ODPV3.SA_Quarta", "ODPV3.SA_Quinta", "ODPV3.SA_Sexta", "ODPV3.SA_Mes_1", "ODPV3.SA_Mes_2", "ODPV3.SA_Mes_3", "ODPV3.SA_Mes_4", "ODPV3.SA_Mes_5", "ODPV3.SA_Mes_6", "ODPV3.SA_Mes_7", "ODPV3.SA_Mes_8", "ODPV3.SA_Mes_9", "ODPV3.SA_Mes_10", "ODPV3.SA_Mes_11", "ODPV3.SA_Mes_12", "ODPV3.SA_Quarter_1", "ODPV3.SA_Quarter_2", "ODPV3.SA_Quarter_3", "ODPV3.SA_Quarter_4", "ODPV3.SA_Last_Day_Quarter", "ODPV3.SA_Pre_Feriado_Brasil", "ODPV3.SA_Sinal_Compra", "ODPV3.SA_Sinal_Venda", "ODPV3.SA_Media_OHLC_Futura", "ODPV3.SA_Media_OHLC_PctChange_Lag_1", "ODPV3.SA_Media_OHLC_PctChange_Lag_2", "ODPV3.SA_Media_OHLC_PctChange_Lag_3", "ODPV3.SA_Media_OHLC_PctChange_Lag_4", "ODPV3.SA_Media_OHLC_PctChange_Lag_5", "ODPV3.SA_Media_OHLC_PctChange_Lag_6", "ODPV3.SA_Media_OHLC_PctChange_Lag_7", "ODPV3.SA_Media_OHLC_PctChange_Lag_8", "ODPV3.SA_Media_OHLC_PctChange_Lag_9", "ODPV3.SA_Media_OHLC_PctChange_Lag_10", "ODPV3.SA_Volume_Lag_1", "ODPV3.SA_Volume_Lag_2", "ODPV3.SA_Volume_Lag_3", "ODPV3.SA_Volume_Lag_4", "ODPV3.SA_Volume_Lag_5", "ODPV3.SA_Spread_Lag_1", "ODPV3.SA_Spread_Lag_2", "ODPV3.SA_Spread_Lag_3", "ODPV3.SA_Spread_Lag_4", "ODPV3.SA_Spread_Lag_5", "ODPV3.SA_Volatilidade_Lag_1", "ODPV3.SA_Volatilidade_Lag_2", "ODPV3.SA_Volatilidade_Lag_3", "ODPV3.SA_Volatilidade_Lag_4", "ODPV3.SA_Volatilidade_Lag_5", "ODPV3.SA_Parkinson_Volatility_Lag_1", "ODPV3.SA_Parkinson_Volatility_Lag_2", "ODPV3.SA_Parkinson_Volatility_Lag_3", "ODPV3.SA_Parkinson_Volatility_Lag_4", "ODPV3.SA_Parkinson_Volatility_Lag_5", "ODPV3.SA_EMV_Lag_1", "ODPV3.SA_EMV_Lag_2", "ODPV3.SA_EMV_Lag_3", "ODPV3.SA_EMV_Lag_4", "ODPV3.SA_EMV_Lag_5", "ODPV3.SA_EMV_MA_Lag_1", "ODPV3.SA_EMV_MA_Lag_2", "ODPV3.SA_EMV_MA_Lag_3", "ODPV3.SA_EMV_MA_Lag_4", "ODPV3.SA_EMV_MA_Lag_5", "ODPV3.SA_VO_Lag_1", "ODPV3.SA_VO_Lag_2", "ODPV3.SA_VO_Lag_3", "ODPV3.SA_VO_Lag_4", "ODPV3.SA_VO_Lag_5", "ODPV3.SA_MFI_Lag_1", "ODPV3.SA_MFI_Lag_2", "ODPV3.SA_MFI_Lag_3", "ODPV3.SA_MFI_Lag_4", "ODPV3.SA_MFI_Lag_5", "ODPV3.SA_Amihud_Lag_1", "ODPV3.SA_Amihud_Lag_2", "ODPV3.SA_Amihud_Lag_3", "ODPV3.SA_Amihud_Lag_4", "ODPV3.SA_Amihud_Lag_5", "ODPV3.SA_Roll_Spread_Lag_1", "ODPV3.SA_Roll_Spread_Lag_2", "ODPV3.SA_Roll_Spread_Lag_3", "ODPV3.SA_Roll_Spread_Lag_4", "ODPV3.SA_Roll_Spread_Lag_5", "ODPV3.SA_Hurst_Lag_1", "ODPV3.SA_Hurst_Lag_2", "ODPV3.SA_Hurst_Lag_3", "ODPV3.SA_<PERSON>rst_Lag_4", "ODPV3.SA_<PERSON><PERSON>_Lag_5", "ODPV3.SA_Vol_per_Volume_Lag_1", "ODPV3.SA_Vol_per_Volume_Lag_2", "ODPV3.SA_Vol_per_Volume_Lag_3", "ODPV3.SA_Vol_per_Volume_Lag_4", "ODPV3.SA_Vol_per_Volume_Lag_5", "ODPV3.SA_CMF_Lag_1", "ODPV3.SA_CMF_Lag_2", "ODPV3.SA_CMF_Lag_3", "ODPV3.SA_CMF_Lag_4", "ODPV3.SA_CMF_Lag_5", "ODPV3.SA_AD_Line_Lag_1", "ODPV3.SA_AD_Line_Lag_2", "ODPV3.SA_AD_Line_Lag_3", "ODPV3.SA_AD_Line_Lag_4", "ODPV3.SA_AD_Line_Lag_5", "PRIO3.SA_Media_OHLC", "PRIO3.SA_MFI_Historical", "PRIO3.SA_Amihud_Historical", "PRIO3.SA_Roll_Spread_Historical", "PRIO3.<PERSON>_<PERSON><PERSON>_Historical", "PRIO3.SA_Vol_per_Volume_Historical", "PRIO3.SA_CMF_Historical", "PRIO3.SA_AD_Line_Historical", "PRIO3.SA_Segunda", "PRIO3.SA_Terca", "PRIO3.SA_Quarta", "PRIO3.SA_Quinta", "PRIO3.SA_Sexta", "PRIO3.SA_Mes_1", "PRIO3.SA_Mes_2", "PRIO3.SA_Mes_3", "PRIO3.SA_Mes_4", "PRIO3.SA_Mes_5", "PRIO3.SA_Mes_6", "PRIO3.SA_Mes_7", "PRIO3.SA_Mes_8", "PRIO3.SA_Mes_9", "PRIO3.SA_Mes_10", "PRIO3.SA_Mes_11", "PRIO3.SA_Mes_12", "PRIO3.SA_Quarter_1", "PRIO3.SA_Quarter_2", "PRIO3.SA_Quarter_3", "PRIO3.SA_Quarter_4", "PRIO3.SA_Last_Day_Quarter", "PRIO3.SA_Pre_Feriado_Brasil", "PRIO3.SA_Sinal_Compra", "PRIO3.SA_Sinal_Venda", "PRIO3.SA_Media_OHLC_Futura", "PRIO3.SA_Media_OHLC_PctChange_Lag_1", "PRIO3.SA_Media_OHLC_PctChange_Lag_2", "PRIO3.SA_Media_OHLC_PctChange_Lag_3", "PRIO3.SA_Media_OHLC_PctChange_Lag_4", "PRIO3.SA_Media_OHLC_PctChange_Lag_5", "PRIO3.SA_Media_OHLC_PctChange_Lag_6", "PRIO3.SA_Media_OHLC_PctChange_Lag_7", "PRIO3.SA_Media_OHLC_PctChange_Lag_8", "PRIO3.SA_Media_OHLC_PctChange_Lag_9", "PRIO3.SA_Media_OHLC_PctChange_Lag_10", "PRIO3.SA_Volume_Lag_1", "PRIO3.SA_Volume_Lag_2", "PRIO3.SA_Volume_Lag_3", "PRIO3.SA_Volume_Lag_4", "PRIO3.SA_Volume_Lag_5", "PRIO3.SA_Spread_Lag_1", "PRIO3.SA_Spread_Lag_2", "PRIO3.SA_Spread_Lag_3", "PRIO3.SA_Spread_Lag_4", "PRIO3.SA_Spread_Lag_5", "PRIO3.SA_Volatilidade_Lag_1", "PRIO3.SA_Volatilidade_Lag_2", "PRIO3.SA_Volatilidade_Lag_3", "PRIO3.SA_Volatilidade_Lag_4", "PRIO3.SA_Volatilidade_Lag_5", "PRIO3.SA_Parkinson_Volatility_Lag_1", "PRIO3.SA_Parkinson_Volatility_Lag_2", "PRIO3.SA_Parkinson_Volatility_Lag_3", "PRIO3.SA_Parkinson_Volatility_Lag_4", "PRIO3.SA_Parkinson_Volatility_Lag_5", "PRIO3.SA_EMV_Lag_1", "PRIO3.SA_EMV_Lag_2", "PRIO3.SA_EMV_Lag_3", "PRIO3.SA_EMV_Lag_4", "PRIO3.SA_EMV_Lag_5", "PRIO3.SA_EMV_MA_Lag_1", "PRIO3.SA_EMV_MA_Lag_2", "PRIO3.SA_EMV_MA_Lag_3", "PRIO3.SA_EMV_MA_Lag_4", "PRIO3.SA_EMV_MA_Lag_5", "PRIO3.SA_VO_Lag_1", "PRIO3.SA_VO_Lag_2", "PRIO3.SA_VO_Lag_3", "PRIO3.SA_VO_Lag_4", "PRIO3.SA_VO_Lag_5", "PRIO3.SA_MFI_Lag_1", "PRIO3.SA_MFI_Lag_2", "PRIO3.SA_MFI_Lag_3", "PRIO3.SA_MFI_Lag_4", "PRIO3.SA_MFI_Lag_5", "PRIO3.SA_Amihud_Lag_1", "PRIO3.SA_Amihud_Lag_2", "PRIO3.SA_Amihud_Lag_3", "PRIO3.SA_Amihud_Lag_4", "PRIO3.SA_Amihud_Lag_5", "PRIO3.SA_Roll_Spread_Lag_1", "PRIO3.SA_Roll_Spread_Lag_2", "PRIO3.SA_Roll_Spread_Lag_3", "PRIO3.SA_Roll_Spread_Lag_4", "PRIO3.SA_Roll_Spread_Lag_5", "PRIO3.SA_Hurst_Lag_1", "PRIO3.<PERSON>_<PERSON><PERSON>_Lag_2", "PRIO3.SA_<PERSON>rst_Lag_3", "PRIO3.<PERSON>_<PERSON><PERSON>_Lag_4", "PRIO3.<PERSON>_<PERSON><PERSON>_Lag_5", "PRIO3.SA_Vol_per_Volume_Lag_1", "PRIO3.SA_Vol_per_Volume_Lag_2", "PRIO3.SA_Vol_per_Volume_Lag_3", "PRIO3.SA_Vol_per_Volume_Lag_4", "PRIO3.SA_Vol_per_Volume_Lag_5", "PRIO3.SA_CMF_Lag_1", "PRIO3.SA_CMF_Lag_2", "PRIO3.SA_CMF_Lag_3", "PRIO3.SA_CMF_Lag_4", "PRIO3.SA_CMF_Lag_5", "PRIO3.SA_AD_Line_Lag_1", "PRIO3.SA_AD_Line_Lag_2", "PRIO3.SA_AD_Line_Lag_3", "PRIO3.SA_AD_Line_Lag_4", "PRIO3.SA_AD_Line_Lag_5", "VVEO3.SA_Media_OHLC", "VVEO3.SA_MFI_Historical", "VVEO3.SA_Amihud_Historical", "VVEO3.SA_Roll_Spread_Historical", "VVEO3.<PERSON>_<PERSON><PERSON>_Historical", "VVEO3.SA_Vol_per_Volume_Historical", "VVEO3.SA_CMF_Historical", "VVEO3.SA_AD_Line_Historical", "VVEO3.SA_Segunda", "VVEO3.SA_Terca", "VVEO3.SA_Quarta", "VVEO3.SA_Quinta", "VVEO3.SA_Sexta", "VVEO3.SA_Mes_1", "VVEO3.SA_Mes_2", "VVEO3.SA_Mes_3", "VVEO3.SA_Mes_4", "VVEO3.SA_Mes_5", "VVEO3.SA_Mes_6", "VVEO3.SA_Mes_7", "VVEO3.SA_Mes_8", "VVEO3.SA_Mes_9", "VVEO3.SA_Mes_10", "VVEO3.SA_Mes_11", "VVEO3.SA_Mes_12", "VVEO3.SA_Quarter_1", "VVEO3.SA_Quarter_2", "VVEO3.SA_Quarter_3", "VVEO3.SA_Quarter_4", "VVEO3.SA_Last_Day_Quarter", "VVEO3.SA_Pre_Feriado_Brasil", "VVEO3.SA_Sinal_Compra", "VVEO3.SA_Sinal_Venda", "VVEO3.SA_Media_OHLC_Futura", "VVEO3.SA_Media_OHLC_PctChange_Lag_1", "VVEO3.SA_Media_OHLC_PctChange_Lag_2", "VVEO3.SA_Media_OHLC_PctChange_Lag_3", "VVEO3.SA_Media_OHLC_PctChange_Lag_4", "VVEO3.SA_Media_OHLC_PctChange_Lag_5", "VVEO3.SA_Media_OHLC_PctChange_Lag_6", "VVEO3.SA_Media_OHLC_PctChange_Lag_7", "VVEO3.SA_Media_OHLC_PctChange_Lag_8", "VVEO3.SA_Media_OHLC_PctChange_Lag_9", "VVEO3.SA_Media_OHLC_PctChange_Lag_10", "VVEO3.SA_Volume_Lag_1", "VVEO3.SA_Volume_Lag_2", "VVEO3.SA_Volume_Lag_3", "VVEO3.SA_Volume_Lag_4", "VVEO3.SA_Volume_Lag_5", "VVEO3.SA_Spread_Lag_1", "VVEO3.SA_Spread_Lag_2", "VVEO3.SA_Spread_Lag_3", "VVEO3.SA_Spread_Lag_4", "VVEO3.SA_Spread_Lag_5", "VVEO3.SA_Volatilidade_Lag_1", "VVEO3.SA_Volatilidade_Lag_2", "VVEO3.SA_Volatilidade_Lag_3", "VVEO3.SA_Volatilidade_Lag_4", "VVEO3.SA_Volatilidade_Lag_5", "VVEO3.SA_Parkinson_Volatility_Lag_1", "VVEO3.SA_Parkinson_Volatility_Lag_2", "VVEO3.SA_Parkinson_Volatility_Lag_3", "VVEO3.SA_Parkinson_Volatility_Lag_4", "VVEO3.SA_Parkinson_Volatility_Lag_5", "VVEO3.SA_EMV_Lag_1", "VVEO3.SA_EMV_Lag_2", "VVEO3.SA_EMV_Lag_3", "VVEO3.SA_EMV_Lag_4", "VVEO3.SA_EMV_Lag_5", "VVEO3.SA_EMV_MA_Lag_1", "VVEO3.SA_EMV_MA_Lag_2", "VVEO3.SA_EMV_MA_Lag_3", "VVEO3.SA_EMV_MA_Lag_4", "VVEO3.SA_EMV_MA_Lag_5", "VVEO3.SA_VO_Lag_1", "VVEO3.SA_VO_Lag_2", "VVEO3.SA_VO_Lag_3", "VVEO3.SA_VO_Lag_4", "VVEO3.SA_VO_Lag_5", "VVEO3.SA_MFI_Lag_1", "VVEO3.SA_MFI_Lag_2", "VVEO3.SA_MFI_Lag_3", "VVEO3.SA_MFI_Lag_4", "VVEO3.SA_MFI_Lag_5", "VVEO3.SA_Amihud_Lag_1", "VVEO3.SA_Amihud_Lag_2", "VVEO3.SA_Amihud_Lag_3", "VVEO3.SA_Amihud_Lag_4", "VVEO3.SA_Amihud_Lag_5", "VVEO3.SA_Roll_Spread_Lag_1", "VVEO3.SA_Roll_Spread_Lag_2", "VVEO3.SA_Roll_Spread_Lag_3", "VVEO3.SA_Roll_Spread_Lag_4", "VVEO3.SA_Roll_Spread_Lag_5", "VVEO3.SA_Hurst_Lag_1", "VVEO3.<PERSON>_Hu<PERSON>_Lag_2", "VVEO3.<PERSON>_Hurst_Lag_3", "VVEO3.<PERSON>_<PERSON><PERSON>_Lag_4", "VVEO3.<PERSON>_<PERSON><PERSON>_Lag_5", "VVEO3.SA_Vol_per_Volume_Lag_1", "VVEO3.SA_Vol_per_Volume_Lag_2", "VVEO3.SA_Vol_per_Volume_Lag_3", "VVEO3.SA_Vol_per_Volume_Lag_4", "VVEO3.SA_Vol_per_Volume_Lag_5", "VVEO3.SA_CMF_Lag_1", "VVEO3.SA_CMF_Lag_2", "VVEO3.SA_CMF_Lag_3", "VVEO3.SA_CMF_Lag_4", "VVEO3.SA_CMF_Lag_5", "VVEO3.SA_AD_Line_Lag_1", "VVEO3.SA_AD_Line_Lag_2", "VVEO3.SA_AD_Line_Lag_3", "VVEO3.SA_AD_Line_Lag_4", "VVEO3.SA_AD_Line_Lag_5", "AGRO3.SA_Media_OHLC", "AGRO3.SA_MFI_Historical", "AGRO3.SA_Amihud_Historical", "AGRO3.SA_Roll_Spread_Historical", "AGRO3.SA_<PERSON>rst_Historical", "AGRO3.SA_Vol_per_Volume_Historical", "AGRO3.SA_CMF_Historical", "AGRO3.SA_AD_Line_Historical", "AGRO3.SA_Segunda", "AGRO3.SA_Terca", "AGRO3.SA_Quarta", "AGRO3.SA_Quinta", "AGRO3.SA_Sexta", "AGRO3.SA_Mes_1", "AGRO3.SA_Mes_2", "AGRO3.SA_Mes_3", "AGRO3.SA_Mes_4", "AGRO3.SA_Mes_5", "AGRO3.SA_Mes_6", "AGRO3.SA_Mes_7", "AGRO3.SA_Mes_8", "AGRO3.SA_Mes_9", "AGRO3.SA_Mes_10", "AGRO3.SA_Mes_11", "AGRO3.SA_Mes_12", "AGRO3.SA_Quarter_1", "AGRO3.SA_Quarter_2", "AGRO3.SA_Quarter_3", "AGRO3.SA_Quarter_4", "AGRO3.SA_Last_Day_Quarter", "AGRO3.SA_Pre_Feriado_Brasil", "AGRO3.SA_Sinal_Compra", "AGRO3.SA_Sinal_Venda", "AGRO3.SA_Media_OHLC_Futura", "AGRO3.SA_Media_OHLC_PctChange_Lag_1", "AGRO3.SA_Media_OHLC_PctChange_Lag_2", "AGRO3.SA_Media_OHLC_PctChange_Lag_3", "AGRO3.SA_Media_OHLC_PctChange_Lag_4", "AGRO3.SA_Media_OHLC_PctChange_Lag_5", "AGRO3.SA_Media_OHLC_PctChange_Lag_6", "AGRO3.SA_Media_OHLC_PctChange_Lag_7", "AGRO3.SA_Media_OHLC_PctChange_Lag_8", "AGRO3.SA_Media_OHLC_PctChange_Lag_9", "AGRO3.SA_Media_OHLC_PctChange_Lag_10", "AGRO3.SA_Volume_Lag_1", "AGRO3.SA_Volume_Lag_2", "AGRO3.SA_Volume_Lag_3", "AGRO3.SA_Volume_Lag_4", "AGRO3.SA_Volume_Lag_5", "AGRO3.SA_Spread_Lag_1", "AGRO3.SA_Spread_Lag_2", "AGRO3.SA_Spread_Lag_3", "AGRO3.SA_Spread_Lag_4", "AGRO3.SA_Spread_Lag_5", "AGRO3.SA_Volatilidade_Lag_1", "AGRO3.SA_Volatilidade_Lag_2", "AGRO3.SA_Volatilidade_Lag_3", "AGRO3.SA_Volatilidade_Lag_4", "AGRO3.SA_Volatilidade_Lag_5", "AGRO3.SA_Parkinson_Volatility_Lag_1", "AGRO3.SA_Parkinson_Volatility_Lag_2", "AGRO3.SA_Parkinson_Volatility_Lag_3", "AGRO3.SA_Parkinson_Volatility_Lag_4", "AGRO3.SA_Parkinson_Volatility_Lag_5", "AGRO3.SA_EMV_Lag_1", "AGRO3.SA_EMV_Lag_2", "AGRO3.SA_EMV_Lag_3", "AGRO3.SA_EMV_Lag_4", "AGRO3.SA_EMV_Lag_5", "AGRO3.SA_EMV_MA_Lag_1", "AGRO3.SA_EMV_MA_Lag_2", "AGRO3.SA_EMV_MA_Lag_3", "AGRO3.SA_EMV_MA_Lag_4", "AGRO3.SA_EMV_MA_Lag_5", "AGRO3.SA_VO_Lag_1", "AGRO3.SA_VO_Lag_2", "AGRO3.SA_VO_Lag_3", "AGRO3.SA_VO_Lag_4", "AGRO3.SA_VO_Lag_5", "AGRO3.SA_MFI_Lag_1", "AGRO3.SA_MFI_Lag_2", "AGRO3.SA_MFI_Lag_3", "AGRO3.SA_MFI_Lag_4", "AGRO3.SA_MFI_Lag_5", "AGRO3.SA_Amihud_Lag_1", "AGRO3.SA_Amihud_Lag_2", "AGRO3.SA_Amihud_Lag_3", "AGRO3.SA_Amihud_Lag_4", "AGRO3.SA_Amihud_Lag_5", "AGRO3.SA_Roll_Spread_Lag_1", "AGRO3.SA_Roll_Spread_Lag_2", "AGRO3.SA_Roll_Spread_Lag_3", "AGRO3.SA_Roll_Spread_Lag_4", "AGRO3.SA_Roll_Spread_Lag_5", "AGRO3.SA_Hurst_Lag_1", "AGRO3.SA_Hurst_Lag_2", "AGRO3.SA_Hurst_Lag_3", "AGRO3.SA_<PERSON>rst_Lag_4", "AGRO3.SA_<PERSON>rst_Lag_5", "AGRO3.SA_Vol_per_Volume_Lag_1", "AGRO3.SA_Vol_per_Volume_Lag_2", "AGRO3.SA_Vol_per_Volume_Lag_3", "AGRO3.SA_Vol_per_Volume_Lag_4", "AGRO3.SA_Vol_per_Volume_Lag_5", "AGRO3.SA_CMF_Lag_1", "AGRO3.SA_CMF_Lag_2", "AGRO3.SA_CMF_Lag_3", "AGRO3.SA_CMF_Lag_4", "AGRO3.SA_CMF_Lag_5", "AGRO3.SA_AD_Line_Lag_1", "AGRO3.SA_AD_Line_Lag_2", "AGRO3.SA_AD_Line_Lag_3", "AGRO3.SA_AD_Line_Lag_4", "AGRO3.SA_AD_Line_Lag_5", "CXSE3.SA_Media_OHLC", "CXSE3.SA_MFI_Historical", "CXSE3.SA_Amihud_Historical", "CXSE3.SA_Roll_Spread_Historical", "CXSE3.<PERSON>_<PERSON><PERSON>_Historical", "CXSE3.SA_Vol_per_Volume_Historical", "CXSE3.SA_CMF_Historical", "CXSE3.SA_AD_Line_Historical", "CXSE3.SA_Segunda", "CXSE3.SA_Terca", "CXSE3.SA_Quarta", "CXSE3.SA_Quinta", "CXSE3.SA_Sexta", "CXSE3.SA_Mes_1", "CXSE3.SA_Mes_2", "CXSE3.SA_Mes_3", "CXSE3.SA_Mes_4", "CXSE3.SA_Mes_5", "CXSE3.SA_Mes_6", "CXSE3.SA_Mes_7", "CXSE3.SA_Mes_8", "CXSE3.SA_Mes_9", "CXSE3.SA_Mes_10", "CXSE3.SA_Mes_11", "CXSE3.SA_Mes_12", "CXSE3.SA_Quarter_1", "CXSE3.SA_Quarter_2", "CXSE3.SA_Quarter_3", "CXSE3.SA_Quarter_4", "CXSE3.SA_Last_Day_Quarter", "CXSE3.SA_Pre_Feriado_Brasil", "CXSE3.SA_Sinal_Compra", "CXSE3.SA_Sinal_Venda", "CXSE3.SA_Media_OHLC_Futura", "CXSE3.SA_Media_OHLC_PctChange_Lag_1", "CXSE3.SA_Media_OHLC_PctChange_Lag_2", "CXSE3.SA_Media_OHLC_PctChange_Lag_3", "CXSE3.SA_Media_OHLC_PctChange_Lag_4", "CXSE3.SA_Media_OHLC_PctChange_Lag_5", "CXSE3.SA_Media_OHLC_PctChange_Lag_6", "CXSE3.SA_Media_OHLC_PctChange_Lag_7", "CXSE3.SA_Media_OHLC_PctChange_Lag_8", "CXSE3.SA_Media_OHLC_PctChange_Lag_9", "CXSE3.SA_Media_OHLC_PctChange_Lag_10", "CXSE3.SA_Volume_Lag_1", "CXSE3.SA_Volume_Lag_2", "CXSE3.SA_Volume_Lag_3", "CXSE3.SA_Volume_Lag_4", "CXSE3.SA_Volume_Lag_5", "CXSE3.SA_Spread_Lag_1", "CXSE3.SA_Spread_Lag_2", "CXSE3.SA_Spread_Lag_3", "CXSE3.SA_Spread_Lag_4", "CXSE3.SA_Spread_Lag_5", "CXSE3.SA_Volatilidade_Lag_1", "CXSE3.SA_Volatilidade_Lag_2", "CXSE3.SA_Volatilidade_Lag_3", "CXSE3.SA_Volatilidade_Lag_4", "CXSE3.SA_Volatilidade_Lag_5", "CXSE3.SA_Parkinson_Volatility_Lag_1", "CXSE3.SA_Parkinson_Volatility_Lag_2", "CXSE3.SA_Parkinson_Volatility_Lag_3", "CXSE3.SA_Parkinson_Volatility_Lag_4", "CXSE3.<PERSON>_Parkinson_Volatility_Lag_5", "CXSE3.SA_EMV_Lag_1", "CXSE3.SA_EMV_Lag_2", "CXSE3.SA_EMV_Lag_3", "CXSE3.SA_EMV_Lag_4", "CXSE3.SA_EMV_Lag_5", "CXSE3.SA_EMV_MA_Lag_1", "CXSE3.SA_EMV_MA_Lag_2", "CXSE3.SA_EMV_MA_Lag_3", "CXSE3.SA_EMV_MA_Lag_4", "CXSE3.SA_EMV_MA_Lag_5", "CXSE3.SA_VO_Lag_1", "CXSE3.SA_VO_Lag_2", "CXSE3.SA_VO_Lag_3", "CXSE3.SA_VO_Lag_4", "CXSE3.SA_VO_Lag_5", "CXSE3.SA_MFI_Lag_1", "CXSE3.SA_MFI_Lag_2", "CXSE3.SA_MFI_Lag_3", "CXSE3.SA_MFI_Lag_4", "CXSE3.SA_MFI_Lag_5", "CXSE3.SA_Amihud_Lag_1", "CXSE3.SA_Amihud_Lag_2", "CXSE3.SA_Amihud_Lag_3", "CXSE3.SA_Amihud_Lag_4", "CXSE3.SA_Amihud_Lag_5", "CXSE3.SA_Roll_Spread_Lag_1", "CXSE3.SA_Roll_Spread_Lag_2", "CXSE3.SA_Roll_Spread_Lag_3", "CXSE3.SA_Roll_Spread_Lag_4", "CXSE3.SA_Roll_Spread_Lag_5", "CXSE3.SA_Hurst_Lag_1", "CXSE3.<PERSON>_Hurst_Lag_2", "CXSE3.<PERSON>_Hurst_Lag_3", "CXSE3.<PERSON>_Hu<PERSON>_Lag_4", "CXSE3.<PERSON>_<PERSON><PERSON>_Lag_5", "CXSE3.SA_Vol_per_Volume_Lag_1", "CXSE3.SA_Vol_per_Volume_Lag_2", "CXSE3.SA_Vol_per_Volume_Lag_3", "CXSE3.SA_Vol_per_Volume_Lag_4", "CXSE3.SA_Vol_per_Volume_Lag_5", "CXSE3.SA_CMF_Lag_1", "CXSE3.SA_CMF_Lag_2", "CXSE3.SA_CMF_Lag_3", "CXSE3.SA_CMF_Lag_4", "CXSE3.SA_CMF_Lag_5", "CXSE3.SA_AD_Line_Lag_1", "CXSE3.SA_AD_Line_Lag_2", "CXSE3.SA_AD_Line_Lag_3", "CXSE3.SA_AD_Line_Lag_4", "CXSE3.SA_AD_Line_Lag_5", "CMIN3.SA_Media_OHLC", "CMIN3.SA_MFI_Historical", "CMIN3.SA_Amihud_Historical", "CMIN3.SA_Roll_Spread_Historical", "CMIN3.<PERSON>_<PERSON><PERSON>_Historical", "CMIN3.SA_Vol_per_Volume_Historical", "CMIN3.SA_CMF_Historical", "CMIN3.SA_AD_Line_Historical", "CMIN3.SA_Segunda", "CMIN3.SA_Terca", "CMIN3.SA_Quarta", "CMIN3.SA_Quinta", "CMIN3.SA_Sexta", "CMIN3.SA_Mes_1", "CMIN3.SA_Mes_2", "CMIN3.SA_Mes_3", "CMIN3.SA_Mes_4", "CMIN3.SA_Mes_5", "CMIN3.SA_Mes_6", "CMIN3.SA_Mes_7", "CMIN3.SA_Mes_8", "CMIN3.SA_Mes_9", "CMIN3.SA_Mes_10", "CMIN3.SA_Mes_11", "CMIN3.SA_Mes_12", "CMIN3.SA_Quarter_1", "CMIN3.SA_Quarter_2", "CMIN3.SA_Quarter_3", "CMIN3.SA_Quarter_4", "CMIN3.SA_Last_Day_Quarter", "CMIN3.SA_Pre_Feriado_Brasil", "CMIN3.SA_Sinal_Compra", "CMIN3.SA_Sinal_Venda", "CMIN3.SA_Media_OHLC_Futura", "CMIN3.SA_Media_OHLC_PctChange_Lag_1", "CMIN3.SA_Media_OHLC_PctChange_Lag_2", "CMIN3.SA_Media_OHLC_PctChange_Lag_3", "CMIN3.SA_Media_OHLC_PctChange_Lag_4", "CMIN3.SA_Media_OHLC_PctChange_Lag_5", "CMIN3.SA_Media_OHLC_PctChange_Lag_6", "CMIN3.SA_Media_OHLC_PctChange_Lag_7", "CMIN3.SA_Media_OHLC_PctChange_Lag_8", "CMIN3.SA_Media_OHLC_PctChange_Lag_9", "CMIN3.SA_Media_OHLC_PctChange_Lag_10", "CMIN3.SA_Volume_Lag_1", "CMIN3.SA_Volume_Lag_2", "CMIN3.SA_Volume_Lag_3", "CMIN3.SA_Volume_Lag_4", "CMIN3.SA_Volume_Lag_5", "CMIN3.SA_Spread_Lag_1", "CMIN3.SA_Spread_Lag_2", "CMIN3.SA_Spread_Lag_3", "CMIN3.SA_Spread_Lag_4", "CMIN3.SA_Spread_Lag_5", "CMIN3.SA_Volatilidade_Lag_1", "CMIN3.SA_Volatilidade_Lag_2", "CMIN3.SA_Volatilidade_Lag_3", "CMIN3.SA_Volatilidade_Lag_4", "CMIN3.SA_Volatilidade_Lag_5", "CMIN3.<PERSON>_Parkinson_Volatility_Lag_1", "CMIN3.<PERSON>_Parkinson_Volatility_Lag_2", "CMIN3.<PERSON>_Parkinson_Volatility_Lag_3", "CMIN3.<PERSON>_Parkinson_Volatility_Lag_4", "CMIN3.<PERSON>_Parkinson_Volatility_Lag_5", "CMIN3.SA_EMV_Lag_1", "CMIN3.SA_EMV_Lag_2", "CMIN3.SA_EMV_Lag_3", "CMIN3.SA_EMV_Lag_4", "CMIN3.SA_EMV_Lag_5", "CMIN3.SA_EMV_MA_Lag_1", "CMIN3.SA_EMV_MA_Lag_2", "CMIN3.SA_EMV_MA_Lag_3", "CMIN3.SA_EMV_MA_Lag_4", "CMIN3.SA_EMV_MA_Lag_5", "CMIN3.SA_VO_Lag_1", "CMIN3.SA_VO_Lag_2", "CMIN3.SA_VO_Lag_3", "CMIN3.SA_VO_Lag_4", "CMIN3.SA_VO_Lag_5", "CMIN3.SA_MFI_Lag_1", "CMIN3.SA_MFI_Lag_2", "CMIN3.SA_MFI_Lag_3", "CMIN3.SA_MFI_Lag_4", "CMIN3.SA_MFI_Lag_5", "CMIN3.SA_Amihud_Lag_1", "CMIN3.SA_Amihud_Lag_2", "CMIN3.SA_Amihud_Lag_3", "CMIN3.SA_Amihud_Lag_4", "CMIN3.SA_Amihud_Lag_5", "CMIN3.SA_Roll_Spread_Lag_1", "CMIN3.SA_Roll_Spread_Lag_2", "CMIN3.SA_Roll_Spread_Lag_3", "CMIN3.SA_Roll_Spread_Lag_4", "CMIN3.SA_Roll_Spread_Lag_5", "CMIN3.<PERSON>_Hu<PERSON>_Lag_1", "CMIN3.<PERSON>_Hu<PERSON>_Lag_2", "CMIN3.<PERSON>_<PERSON><PERSON>_Lag_3", "CMIN3.<PERSON>_<PERSON><PERSON>_Lag_4", "CMIN3.<PERSON>_<PERSON><PERSON>_Lag_5", "CMIN3.SA_Vol_per_Volume_Lag_1", "CMIN3.SA_Vol_per_Volume_Lag_2", "CMIN3.SA_Vol_per_Volume_Lag_3", "CMIN3.SA_Vol_per_Volume_Lag_4", "CMIN3.SA_Vol_per_Volume_Lag_5", "CMIN3.SA_CMF_Lag_1", "CMIN3.SA_CMF_Lag_2", "CMIN3.SA_CMF_Lag_3", "CMIN3.SA_CMF_Lag_4", "CMIN3.SA_CMF_Lag_5", "CMIN3.SA_AD_Line_Lag_1", "CMIN3.SA_AD_Line_Lag_2", "CMIN3.SA_AD_Line_Lag_3", "CMIN3.SA_AD_Line_Lag_4", "CMIN3.SA_AD_Line_Lag_5", "TUPY3.SA_Media_OHLC", "TUPY3.SA_MFI_Historical", "TUPY3.SA_Amihud_Historical", "TUPY3.SA_Roll_Spread_Historical", "TUPY3.<PERSON>_<PERSON><PERSON>_Historical", "TUPY3.SA_Vol_per_Volume_Historical", "TUPY3.SA_CMF_Historical", "TUPY3.SA_AD_Line_Historical", "TUPY3.SA_Segunda", "TUPY3.SA_Terca", "TUPY3.SA_Quarta", "TUPY3.SA_Quinta", "TUPY3.SA_Sexta", "TUPY3.SA_Mes_1", "TUPY3.SA_Mes_2", "TUPY3.SA_Mes_3", "TUPY3.SA_Mes_4", "TUPY3.SA_Mes_5", "TUPY3.SA_Mes_6", "TUPY3.SA_Mes_7", "TUPY3.SA_Mes_8", "TUPY3.SA_Mes_9", "TUPY3.SA_Mes_10", "TUPY3.SA_Mes_11", "TUPY3.SA_Mes_12", "TUPY3.SA_Quarter_1", "TUPY3.SA_Quarter_2", "TUPY3.SA_Quarter_3", "TUPY3.SA_Quarter_4", "TUPY3.SA_Last_Day_Quarter", "TUPY3.SA_Pre_Feriado_Brasil", "TUPY3.SA_Sinal_Compra", "TUPY3.SA_Sinal_Venda", "TUPY3.SA_Media_OHLC_Futura", "TUPY3.SA_Media_OHLC_PctChange_Lag_1", "TUPY3.SA_Media_OHLC_PctChange_Lag_2", "TUPY3.SA_Media_OHLC_PctChange_Lag_3", "TUPY3.SA_Media_OHLC_PctChange_Lag_4", "TUPY3.SA_Media_OHLC_PctChange_Lag_5", "TUPY3.SA_Media_OHLC_PctChange_Lag_6", "TUPY3.SA_Media_OHLC_PctChange_Lag_7", "TUPY3.SA_Media_OHLC_PctChange_Lag_8", "TUPY3.SA_Media_OHLC_PctChange_Lag_9", "TUPY3.SA_Media_OHLC_PctChange_Lag_10", "TUPY3.SA_Volume_Lag_1", "TUPY3.SA_Volume_Lag_2", "TUPY3.SA_Volume_Lag_3", "TUPY3.SA_Volume_Lag_4", "TUPY3.SA_Volume_Lag_5", "TUPY3.SA_Spread_Lag_1", "TUPY3.SA_Spread_Lag_2", "TUPY3.SA_Spread_Lag_3", "TUPY3.SA_Spread_Lag_4", "TUPY3.SA_Spread_Lag_5", "TUPY3.SA_Volatilidade_Lag_1", "TUPY3.SA_Volatilidade_Lag_2", "TUPY3.SA_Volatilidade_Lag_3", "TUPY3.SA_Volatilidade_Lag_4", "TUPY3.SA_Volatilidade_Lag_5", "TUPY3.SA_Parkinson_Volatility_Lag_1", "TUPY3.<PERSON>_Parkinson_Volatility_Lag_2", "TUPY3.SA_Parkinson_Volatility_Lag_3", "TUPY3.SA_Parkinson_Volatility_Lag_4", "TUPY3.<PERSON>_Parkinson_Volatility_Lag_5", "TUPY3.SA_EMV_Lag_1", "TUPY3.SA_EMV_Lag_2", "TUPY3.SA_EMV_Lag_3", "TUPY3.SA_EMV_Lag_4", "TUPY3.SA_EMV_Lag_5", "TUPY3.SA_EMV_MA_Lag_1", "TUPY3.SA_EMV_MA_Lag_2", "TUPY3.SA_EMV_MA_Lag_3", "TUPY3.SA_EMV_MA_Lag_4", "TUPY3.SA_EMV_MA_Lag_5", "TUPY3.SA_VO_Lag_1", "TUPY3.SA_VO_Lag_2", "TUPY3.SA_VO_Lag_3", "TUPY3.SA_VO_Lag_4", "TUPY3.SA_VO_Lag_5", "TUPY3.SA_MFI_Lag_1", "TUPY3.SA_MFI_Lag_2", "TUPY3.SA_MFI_Lag_3", "TUPY3.SA_MFI_Lag_4", "TUPY3.SA_MFI_Lag_5", "TUPY3.SA_Amihud_Lag_1", "TUPY3.SA_Amihud_Lag_2", "TUPY3.SA_Amihud_Lag_3", "TUPY3.SA_Amihud_Lag_4", "TUPY3.SA_Amihud_Lag_5", "TUPY3.SA_Roll_Spread_Lag_1", "TUPY3.SA_Roll_Spread_Lag_2", "TUPY3.SA_Roll_Spread_Lag_3", "TUPY3.SA_Roll_Spread_Lag_4", "TUPY3.SA_Roll_Spread_Lag_5", "TUPY3.SA_Hurst_Lag_1", "TUPY3.<PERSON>_Hu<PERSON>_Lag_2", "TUPY3.<PERSON>_Hurst_Lag_3", "TUPY3.<PERSON>_<PERSON><PERSON>_Lag_4", "TUPY3.<PERSON>_<PERSON><PERSON>_Lag_5", "TUPY3.SA_Vol_per_Volume_Lag_1", "TUPY3.SA_Vol_per_Volume_Lag_2", "TUPY3.SA_Vol_per_Volume_Lag_3", "TUPY3.SA_Vol_per_Volume_Lag_4", "TUPY3.SA_Vol_per_Volume_Lag_5", "TUPY3.SA_CMF_Lag_1", "TUPY3.SA_CMF_Lag_2", "TUPY3.SA_CMF_Lag_3", "TUPY3.SA_CMF_Lag_4", "TUPY3.SA_CMF_Lag_5", "TUPY3.SA_AD_Line_Lag_1", "TUPY3.SA_AD_Line_Lag_2", "TUPY3.SA_AD_Line_Lag_3", "TUPY3.SA_AD_Line_Lag_4", "TUPY3.SA_AD_Line_Lag_5", "TTEN3.SA_Media_OHLC", "TTEN3.SA_MFI_Historical", "TTEN3.SA_Amihud_Historical", "TTEN3.SA_Roll_Spread_Historical", "TTEN3.SA_<PERSON><PERSON>_Historical", "TTEN3.SA_Vol_per_Volume_Historical", "TTEN3.SA_CMF_Historical", "TTEN3.SA_AD_Line_Historical", "TTEN3.SA_Segunda", "TTEN3.SA_Terca", "TTEN3.SA_Quarta", "TTEN3.SA_Quinta", "TTEN3.SA_Sexta", "TTEN3.SA_Mes_1", "TTEN3.SA_Mes_2", "TTEN3.SA_Mes_3", "TTEN3.SA_Mes_4", "TTEN3.SA_Mes_5", "TTEN3.SA_Mes_6", "TTEN3.SA_Mes_7", "TTEN3.SA_Mes_8", "TTEN3.SA_Mes_9", "TTEN3.SA_Mes_10", "TTEN3.SA_Mes_11", "TTEN3.SA_Mes_12", "TTEN3.SA_Quarter_1", "TTEN3.SA_Quarter_2", "TTEN3.SA_Quarter_3", "TTEN3.SA_Quarter_4", "TTEN3.SA_Last_Day_Quarter", "TTEN3.SA_Pre_Feriado_Brasil", "TTEN3.SA_Sinal_Compra", "TTEN3.SA_Sinal_Venda", "TTEN3.SA_Media_OHLC_Futura", "TTEN3.SA_Media_OHLC_PctChange_Lag_1", "TTEN3.SA_Media_OHLC_PctChange_Lag_2", "TTEN3.SA_Media_OHLC_PctChange_Lag_3", "TTEN3.SA_Media_OHLC_PctChange_Lag_4", "TTEN3.SA_Media_OHLC_PctChange_Lag_5", "TTEN3.SA_Media_OHLC_PctChange_Lag_6", "TTEN3.SA_Media_OHLC_PctChange_Lag_7", "TTEN3.SA_Media_OHLC_PctChange_Lag_8", "TTEN3.SA_Media_OHLC_PctChange_Lag_9", "TTEN3.SA_Media_OHLC_PctChange_Lag_10", "TTEN3.SA_Volume_Lag_1", "TTEN3.SA_Volume_Lag_2", "TTEN3.SA_Volume_Lag_3", "TTEN3.SA_Volume_Lag_4", "TTEN3.SA_Volume_Lag_5", "TTEN3.SA_Spread_Lag_1", "TTEN3.SA_Spread_Lag_2", "TTEN3.SA_Spread_Lag_3", "TTEN3.SA_Spread_Lag_4", "TTEN3.SA_Spread_Lag_5", "TTEN3.SA_Volatilidade_Lag_1", "TTEN3.SA_Volatilidade_Lag_2", "TTEN3.SA_Volatilidade_Lag_3", "TTEN3.SA_Volatilidade_Lag_4", "TTEN3.SA_Volatilidade_Lag_5", "TTEN3.SA_Parkinson_Volatility_Lag_1", "TTEN3.SA_Parkinson_Volatility_Lag_2", "TTEN3.SA_Parkinson_Volatility_Lag_3", "TTEN3.SA_Parkinson_Volatility_Lag_4", "TTEN3.SA_Parkinson_Volatility_Lag_5", "TTEN3.SA_EMV_Lag_1", "TTEN3.SA_EMV_Lag_2", "TTEN3.SA_EMV_Lag_3", "TTEN3.SA_EMV_Lag_4", "TTEN3.SA_EMV_Lag_5", "TTEN3.SA_EMV_MA_Lag_1", "TTEN3.SA_EMV_MA_Lag_2", "TTEN3.SA_EMV_MA_Lag_3", "TTEN3.SA_EMV_MA_Lag_4", "TTEN3.SA_EMV_MA_Lag_5", "TTEN3.SA_VO_Lag_1", "TTEN3.SA_VO_Lag_2", "TTEN3.SA_VO_Lag_3", "TTEN3.SA_VO_Lag_4", "TTEN3.SA_VO_Lag_5", "TTEN3.SA_MFI_Lag_1", "TTEN3.SA_MFI_Lag_2", "TTEN3.SA_MFI_Lag_3", "TTEN3.SA_MFI_Lag_4", "TTEN3.SA_MFI_Lag_5", "TTEN3.SA_Amihud_Lag_1", "TTEN3.SA_Amihud_Lag_2", "TTEN3.SA_Amihud_Lag_3", "TTEN3.SA_Amihud_Lag_4", "TTEN3.SA_Amihud_Lag_5", "TTEN3.SA_Roll_Spread_Lag_1", "TTEN3.SA_Roll_Spread_Lag_2", "TTEN3.SA_Roll_Spread_Lag_3", "TTEN3.SA_Roll_Spread_Lag_4", "TTEN3.SA_Roll_Spread_Lag_5", "TTEN3.SA_Hurst_Lag_1", "TTEN3.SA_Hurst_Lag_2", "TTEN3.SA_Hurst_Lag_3", "TTEN3.<PERSON>_Hu<PERSON>_Lag_4", "TTEN3.<PERSON>_<PERSON><PERSON>_Lag_5", "TTEN3.SA_Vol_per_Volume_Lag_1", "TTEN3.SA_Vol_per_Volume_Lag_2", "TTEN3.SA_Vol_per_Volume_Lag_3", "TTEN3.SA_Vol_per_Volume_Lag_4", "TTEN3.SA_Vol_per_Volume_Lag_5", "TTEN3.SA_CMF_Lag_1", "TTEN3.SA_CMF_Lag_2", "TTEN3.SA_CMF_Lag_3", "TTEN3.SA_CMF_Lag_4", "TTEN3.SA_CMF_Lag_5", "TTEN3.SA_AD_Line_Lag_1", "TTEN3.SA_AD_Line_Lag_2", "TTEN3.SA_AD_Line_Lag_3", "TTEN3.SA_AD_Line_Lag_4", "TTEN3.SA_AD_Line_Lag_5", "MDIA3.SA_Media_OHLC", "MDIA3.SA_MFI_Historical", "MDIA3.SA_Amihud_Historical", "MDIA3.SA_Roll_Spread_Historical", "MDIA3.SA_<PERSON><PERSON>_Historical", "MDIA3.SA_Vol_per_Volume_Historical", "MDIA3.SA_CMF_Historical", "MDIA3.SA_AD_Line_Historical", "MDIA3.SA_Segunda", "MDIA3.SA_Terca", "MDIA3.SA_Quarta", "MDIA3.SA_Quinta", "MDIA3.SA_Sexta", "MDIA3.SA_Mes_1", "MDIA3.SA_Mes_2", "MDIA3.SA_Mes_3", "MDIA3.SA_Mes_4", "MDIA3.SA_Mes_5", "MDIA3.SA_Mes_6", "MDIA3.SA_Mes_7", "MDIA3.SA_Mes_8", "MDIA3.SA_Mes_9", "MDIA3.SA_Mes_10", "MDIA3.SA_Mes_11", "MDIA3.SA_Mes_12", "MDIA3.SA_Quarter_1", "MDIA3.SA_Quarter_2", "MDIA3.SA_Quarter_3", "MDIA3.SA_Quarter_4", "MDIA3.SA_Last_Day_Quarter", "MDIA3.SA_Pre_Feriado_Brasil", "MDIA3.SA_Sinal_Compra", "MDIA3.SA_Sinal_Venda", "MDIA3.SA_Media_OHLC_Futura", "MDIA3.SA_Media_OHLC_PctChange_Lag_1", "MDIA3.SA_Media_OHLC_PctChange_Lag_2", "MDIA3.SA_Media_OHLC_PctChange_Lag_3", "MDIA3.SA_Media_OHLC_PctChange_Lag_4", "MDIA3.SA_Media_OHLC_PctChange_Lag_5", "MDIA3.SA_Media_OHLC_PctChange_Lag_6", "MDIA3.SA_Media_OHLC_PctChange_Lag_7", "MDIA3.SA_Media_OHLC_PctChange_Lag_8", "MDIA3.SA_Media_OHLC_PctChange_Lag_9", "MDIA3.SA_Media_OHLC_PctChange_Lag_10", "MDIA3.SA_Volume_Lag_1", "MDIA3.SA_Volume_Lag_2", "MDIA3.SA_Volume_Lag_3", "MDIA3.SA_Volume_Lag_4", "MDIA3.SA_Volume_Lag_5", "MDIA3.SA_Spread_Lag_1", "MDIA3.SA_Spread_Lag_2", "MDIA3.SA_Spread_Lag_3", "MDIA3.SA_Spread_Lag_4", "MDIA3.SA_Spread_Lag_5", "MDIA3.SA_Volatilidade_Lag_1", "MDIA3.SA_Volatilidade_Lag_2", "MDIA3.SA_Volatilidade_Lag_3", "MDIA3.SA_Volatilidade_Lag_4", "MDIA3.SA_Volatilidade_Lag_5", "MDIA3.SA_Parkinson_Volatility_Lag_1", "MDIA3.SA_Parkinson_Volatility_Lag_2", "MDIA3.SA_Parkinson_Volatility_Lag_3", "MDIA3.SA_Parkinson_Volatility_Lag_4", "MDIA3.SA_Parkinson_Volatility_Lag_5", "MDIA3.SA_EMV_Lag_1", "MDIA3.SA_EMV_Lag_2", "MDIA3.SA_EMV_Lag_3", "MDIA3.SA_EMV_Lag_4", "MDIA3.SA_EMV_Lag_5", "MDIA3.SA_EMV_MA_Lag_1", "MDIA3.SA_EMV_MA_Lag_2", "MDIA3.SA_EMV_MA_Lag_3", "MDIA3.SA_EMV_MA_Lag_4", "MDIA3.SA_EMV_MA_Lag_5", "MDIA3.SA_VO_Lag_1", "MDIA3.SA_VO_Lag_2", "MDIA3.SA_VO_Lag_3", "MDIA3.SA_VO_Lag_4", "MDIA3.SA_VO_Lag_5", "MDIA3.SA_MFI_Lag_1", "MDIA3.SA_MFI_Lag_2", "MDIA3.SA_MFI_Lag_3", "MDIA3.SA_MFI_Lag_4", "MDIA3.SA_MFI_Lag_5", "MDIA3.SA_Amihud_Lag_1", "MDIA3.SA_Amihud_Lag_2", "MDIA3.SA_Amihud_Lag_3", "MDIA3.SA_Amihud_Lag_4", "MDIA3.SA_Amihud_Lag_5", "MDIA3.SA_Roll_Spread_Lag_1", "MDIA3.SA_Roll_Spread_Lag_2", "MDIA3.SA_Roll_Spread_Lag_3", "MDIA3.SA_Roll_Spread_Lag_4", "MDIA3.SA_Roll_Spread_Lag_5", "MDIA3.SA_Hurst_Lag_1", "MDIA3.SA_Hurst_Lag_2", "MDIA3.SA_Hurst_Lag_3", "MDIA3.SA_<PERSON>rst_Lag_4", "MDIA3.<PERSON>_<PERSON><PERSON>_Lag_5", "MDIA3.SA_Vol_per_Volume_Lag_1", "MDIA3.SA_Vol_per_Volume_Lag_2", "MDIA3.SA_Vol_per_Volume_Lag_3", "MDIA3.SA_Vol_per_Volume_Lag_4", "MDIA3.SA_Vol_per_Volume_Lag_5", "MDIA3.SA_CMF_Lag_1", "MDIA3.SA_CMF_Lag_2", "MDIA3.SA_CMF_Lag_3", "MDIA3.SA_CMF_Lag_4", "MDIA3.SA_CMF_Lag_5", "MDIA3.SA_AD_Line_Lag_1", "MDIA3.SA_AD_Line_Lag_2", "MDIA3.SA_AD_Line_Lag_3", "MDIA3.SA_AD_Line_Lag_4", "MDIA3.SA_AD_Line_Lag_5", "CPFE3.SA_Media_OHLC", "CPFE3.SA_MFI_Historical", "CPFE3.SA_Amihud_Historical", "CPFE3.SA_Roll_Spread_Historical", "CPFE3.SA_<PERSON><PERSON>_Historical", "CPFE3.SA_Vol_per_Volume_Historical", "CPFE3.SA_CMF_Historical", "CPFE3.SA_AD_Line_Historical", "CPFE3.SA_Segunda", "CPFE3.SA_Terca", "CPFE3.SA_Quarta", "CPFE3.SA_Quinta", "CPFE3.SA_Sexta", "CPFE3.SA_Mes_1", "CPFE3.SA_Mes_2", "CPFE3.SA_Mes_3", "CPFE3.SA_Mes_4", "CPFE3.SA_Mes_5", "CPFE3.SA_Mes_6", "CPFE3.SA_Mes_7", "CPFE3.SA_Mes_8", "CPFE3.SA_Mes_9", "CPFE3.SA_Mes_10", "CPFE3.SA_Mes_11", "CPFE3.SA_Mes_12", "CPFE3.SA_Quarter_1", "CPFE3.SA_Quarter_2", "CPFE3.SA_Quarter_3", "CPFE3.SA_Quarter_4", "CPFE3.SA_Last_Day_Quarter", "CPFE3.SA_Pre_Feriado_Brasil", "CPFE3.SA_Sinal_Compra", "CPFE3.SA_Sinal_Venda", "CPFE3.SA_Media_OHLC_Futura", "CPFE3.SA_Media_OHLC_PctChange_Lag_1", "CPFE3.SA_Media_OHLC_PctChange_Lag_2", "CPFE3.SA_Media_OHLC_PctChange_Lag_3", "CPFE3.SA_Media_OHLC_PctChange_Lag_4", "CPFE3.SA_Media_OHLC_PctChange_Lag_5", "CPFE3.SA_Media_OHLC_PctChange_Lag_6", "CPFE3.SA_Media_OHLC_PctChange_Lag_7", "CPFE3.SA_Media_OHLC_PctChange_Lag_8", "CPFE3.SA_Media_OHLC_PctChange_Lag_9", "CPFE3.SA_Media_OHLC_PctChange_Lag_10", "CPFE3.SA_Volume_Lag_1", "CPFE3.SA_Volume_Lag_2", "CPFE3.SA_Volume_Lag_3", "CPFE3.SA_Volume_Lag_4", "CPFE3.SA_Volume_Lag_5", "CPFE3.SA_Spread_Lag_1", "CPFE3.SA_Spread_Lag_2", "CPFE3.SA_Spread_Lag_3", "CPFE3.SA_Spread_Lag_4", "CPFE3.SA_Spread_Lag_5", "CPFE3.SA_Volatilidade_Lag_1", "CPFE3.SA_Volatilidade_Lag_2", "CPFE3.SA_Volatilidade_Lag_3", "CPFE3.SA_Volatilidade_Lag_4", "CPFE3.SA_Volatilidade_Lag_5", "CPFE3.SA_Parkinson_Volatility_Lag_1", "CPFE3.<PERSON>_Parkinson_Volatility_Lag_2", "CPFE3.SA_Parkinson_Volatility_Lag_3", "CPFE3.SA_Parkinson_Volatility_Lag_4", "CPFE3.<PERSON>_Parkinson_Volatility_Lag_5", "CPFE3.SA_EMV_Lag_1", "CPFE3.SA_EMV_Lag_2", "CPFE3.SA_EMV_Lag_3", "CPFE3.SA_EMV_Lag_4", "CPFE3.SA_EMV_Lag_5", "CPFE3.SA_EMV_MA_Lag_1", "CPFE3.SA_EMV_MA_Lag_2", "CPFE3.SA_EMV_MA_Lag_3", "CPFE3.SA_EMV_MA_Lag_4", "CPFE3.SA_EMV_MA_Lag_5", "CPFE3.SA_VO_Lag_1", "CPFE3.SA_VO_Lag_2", "CPFE3.SA_VO_Lag_3", "CPFE3.SA_VO_Lag_4", "CPFE3.SA_VO_Lag_5", "CPFE3.SA_MFI_Lag_1", "CPFE3.SA_MFI_Lag_2", "CPFE3.SA_MFI_Lag_3", "CPFE3.SA_MFI_Lag_4", "CPFE3.SA_MFI_Lag_5", "CPFE3.SA_Amihud_Lag_1", "CPFE3.SA_Amihud_Lag_2", "CPFE3.SA_Amihud_Lag_3", "CPFE3.SA_Amihud_Lag_4", "CPFE3.SA_Amihud_Lag_5", "CPFE3.SA_Roll_Spread_Lag_1", "CPFE3.SA_Roll_Spread_Lag_2", "CPFE3.SA_Roll_Spread_Lag_3", "CPFE3.SA_Roll_Spread_Lag_4", "CPFE3.SA_Roll_Spread_Lag_5", "CPFE3.SA_Hurst_Lag_1", "CPFE3.<PERSON>_Hu<PERSON>_Lag_2", "CPFE3.SA_Hurst_Lag_3", "CPFE3.<PERSON>_<PERSON><PERSON>_Lag_4", "CPFE3.<PERSON>_<PERSON><PERSON>_Lag_5", "CPFE3.SA_Vol_per_Volume_Lag_1", "CPFE3.SA_Vol_per_Volume_Lag_2", "CPFE3.SA_Vol_per_Volume_Lag_3", "CPFE3.SA_Vol_per_Volume_Lag_4", "CPFE3.SA_Vol_per_Volume_Lag_5", "CPFE3.SA_CMF_Lag_1", "CPFE3.SA_CMF_Lag_2", "CPFE3.SA_CMF_Lag_3", "CPFE3.SA_CMF_Lag_4", "CPFE3.SA_CMF_Lag_5", "CPFE3.SA_AD_Line_Lag_1", "CPFE3.SA_AD_Line_Lag_2", "CPFE3.SA_AD_Line_Lag_3", "CPFE3.SA_AD_Line_Lag_4", "CPFE3.SA_AD_Line_Lag_5"]}