# Configuração centralizada para todos os scripts de análise financeira
# Este arquivo contém todos os parâmetros utilizados nos scripts da pasta src/

# ============================================================================
# CONFIGURAÇÕES GERAIS
# ============================================================================
general:
  # Configurações de matplotlib
  matplotlib_backend: 'Agg'  # Backend para salvar sem display
  matplotlib_style: 'default'
  
  # Configurações de warnings
  suppress_warnings: true
  
  # Configurações de random seed para reprodutibilidade
  random_seed: 42

# ============================================================================
# CONFIGURAÇÕES DE DADOS
# ============================================================================
data:
  # Arquivos de entrada
  files:
    carteira: 'carteira.csv'
    acoes_listadas_b3: 'acoes-listadas-b3.csv'
    acoes_diversificacao: 'results/csv/correlation_data/acoes_diversificacao.csv'
  
  # Períodos de dados históricos
  periods:
    # Para análises gerais
    default_period: '18mo'  # 18 meses
    correlation_period: '1y' # 1 ano para correlação
    
  # Datas específicas
  dates:
    portfolio_start_date: '2025-06-26'  # Data de início da carteira
    tims3_correction_limit: '2025-07-02'  # Data limite para correção TIMS3
  
  # Configurações de correção de dados
  corrections:
    tims3_division_factor: 100  # Fator de divisão para correção TIMS3
    replace_zero_with_previous: true  # Substituir zeros com valor anterior

# ============================================================================
# CONFIGURAÇÕES DE MÉDIAS MÓVEIS
# ============================================================================
moving_averages:
  # Janelas das médias móveis (em dias)
  windows:
    mm10: 10
    mm25: 25
    mm50: 50
    mm100: 100
    mm200: 200
  
  # Configurações específicas
  use_ohlc_average: true  # Usar média OHLC nos dias anteriores, OHL no dia atual (trading intraday)
  exponential_smoothing: true  # Usar suavização exponencial

# ============================================================================
# CONFIGURAÇÕES DO FILTRO BUTTERWORTH
# ============================================================================
butterworth:
  # Ordem do filtro
  filter_order: 2
  
  # Frequências de corte para cada média móvel
  cutoff_frequencies:
    mm10: 0.18   # Filtro mais rápido
    mm25: 0.05   # Filtro intermediário
    mm100: 0.02  # Filtro lento
    mm200: 0.009 # Filtro mais lento
  
  # Tipo de filtro
  filter_type: 'low'  # Passa-baixa
  analog: false


# ============================================================================
# CONFIGURAÇÕES DE CORRELAÇÃO
# ============================================================================
correlation:
  # Número de ações para análise de diversificação
  diversification_stocks: 40
  
  # Threshold de correlação para eliminação
  high_correlation_threshold: 0.9
  
  # Priorizar ações que pagam dividendos
  prioritize_dividend_stocks: true
  
  # Configurações do mapa de calor
  heatmap:
    figsize: [12, 10]
    colormap: 'coolwarm'
    center: 0

# ============================================================================
# CONFIGURAÇÕES DE VISUALIZAÇÃO
# ============================================================================
visualization:
  # Configurações gerais de gráficos
  figure_size: [16, 12]
  dpi: 100
  
  # Configurações de cores
  colors:
    price: '#1f77b4'      # Azul para preços
    mm10: 'blue'          # Azul para MM10
    mm25: 'green'         # Verde para MM25
    mm50: 'orange'        # Laranja para MM50
    mm100: 'purple'       # Roxo para MM100
    mm200: 'red'          # Vermelho para MM200
    volume: 'gray'        # Cinza para volume
    spread: 'brown'       # Marrom para spread
    prediction: 'orange'  # Laranja para previsões
    trend_up: 'green'     # Verde para tendência alta
    trend_down: 'red'     # Vermelho para tendência baixa
    trend_neutral: 'gray' # Cinza para tendência neutra
  
  # Configurações de linha
  line_widths:
    price: 2.5
    mm10: 5
    mm25: 2
    mm50: 2
    mm100: 2
    mm200: 2
    prediction: 3
  
  # Configurações de transparência
  alpha_values:
    main_lines: 0.8
    fill_areas: 0.3
    volume_bars: 0.6
  
  # Posição da legenda
  legend_position: 'upper left'  # Evitar sobreposição com dados

# ============================================================================
# CONFIGURAÇÕES DE ESTRATÉGIA DE TRADING
# ============================================================================
trading_strategy:
  # Configurações de sinais
  signals:
    buy_signal: 'cross_above'   # Cruzamento de baixo para cima
    sell_signal: 'cross_below'  # Cruzamento de cima para baixo
    
  # Linha de referência para posição inicial
  initial_position_reference: 'mm25'  # Usar apenas MM25
  
  # Configurações de execução
  no_action_last_day: true  # Não executar ações no último dia
  only_sell_owned_stocks: true  # Vender apenas ações em carteira
  
  # Configurações de contabilidade
  subtract_sales_from_invested: true  # Subtrair vendas do valor investido
  track_both_invested_and_current: true  # Rastrear investido e atual

# ============================================================================
# CONFIGURAÇÕES DE SAÍDA
# ============================================================================
output:
  # Diretórios de saída
  directories:
    results: 'results'
    figures: 'results/figures'
    csv: 'results/csv'
    individual_analysis: 'results/csv/individual_analysis'
    correlation_data: 'results/csv/correlation_data'
    kalman_analysis: 'results/csv/kalman_analysis'
    lstm_analysis: 'results/csv/lstm_analysis'
    butterworth_analysis: 'results/csv/butterworth_analysis'
    portfolio_analysis: 'results/csv/portfolio_analysis'
  
  # Configurações de arquivo
  file_formats:
    figures: 'png'
    data: 'csv'
  
  # Limpeza de arquivos antigos
  clean_old_files: true  # Limpar figuras e CSVs antigos antes de gerar novos

# ============================================================================
# CONFIGURAÇÕES DE API
# ============================================================================
api:
  # Chaves de API (manter em variáveis de ambiente em produção)
  keys:
    alpha_vantage: 'ELGIUR9A6KBUAP47'
    twelve_data: 'aadf74ae14d74fd7ac40a433dad50f51'
  
  # Configurações de timeout e retry
  timeout: 30  # segundos
  max_retries: 3
  retry_delay: 1  # segundos

# ============================================================================
# CONFIGURAÇÕES DE SPREAD
# ============================================================================
spread:
  # Configurações do edge_rolling
  window: 20  # Janela para cálculo do spread
  sign: true  # Manter sinal do spread
  
  # Conversão para percentual
  convert_to_percentage: true
  percentage_multiplier: 100
  
  # Fallback para volatilidade quando spread não disponível
  use_volatility_fallback: true
  volatility_window: 20

# ============================================================================
# CONFIGURAÇÕES DO XGBOOST
# ============================================================================
xgboost:
  # Período de dados para treinamento
  data_period: '15y'  # 15 anos de dados históricos

  # Configurações de cache histórico
  cache:
    use_cache: true  # Usar cache para acelerar downloads
    use_unified: true  # Usar cache unificado (MAIS EFICIENTE - todas as ações em um arquivo)
    use_optimized: true  # Usar cache Parquet otimizado (requer pyarrow)
    force_download: false  # Forçar download completo (ignorar cache)
    force_training: true  # Forçar treinamento dos modelos (ignorar cache de modelos)
    max_days_outdated: 3  # Máximo de dias desatualizados antes de forçar atualização

  # Definição dos sinais (NOVA LÓGICA: comparação com dia anterior)
  signal_horizon: 1  # Não usado mais - sinais agora baseados na comparação com dia anterior

  # Features
  features:
    # Lags da média OHLC (quantos dias passados usar)
    ohlc_lags: 10  # Usar 10 dias passados da média OHLC
    pct_threshold: 0.5  # Threshold para considerar pct_change como relevante (0.5%)

    # Lags das features econométricas (quantos dias passados usar)
    econometric_lags: 5  # Usar 5 dias passados das features econométricas

    # Janelas para cálculos
    volatility_window: 20  # Janela para cálculo da volatilidade
    spread_multiplier: 0.5  # Multiplicador para estimar spread baseado na volatilidade

  # Parâmetros do modelo XGBoost
  model_params:
    n_estimators: 100      # Número de árvores
    max_depth: 6           # Profundidade máxima das árvores
    learning_rate: 0.1     # Taxa de aprendizado
    random_state: 42       # Seed para reprodutibilidade
    eval_metric: 'logloss' # Métrica de avaliação

  # Divisão dos dados
  test_size: 0.2  # 20% para teste

  # Divisão temporal dos dados
  temporal_split:
    use_temporal_split: true  # Usar divisão temporal ao invés de aleatória

  # Normalização
  use_standard_scaler: true  # Usar StandardScaler para normalizar features

  # Threshold de probabilidade para sinais
  probability_threshold: 0.50  # Sinais só são gerados se probabilidade > 0.55

  # Configurações específicas para cálculos
  calculations:
    emv_volume_divisor: 1000000  # Divisor para volume no cálculo EMV (Ease of Movement)

  estimator:
    use_weighted_training: false  # Usar pesos no treinamento para amenizar impacto de grandes variações

# ============================================================================
# CONFIGURAÇÕES DO LIGHTGBM
# ============================================================================
lightgbm:
  # Período de dados para treinamento (mesmo do XGBoost)
  data_period: '15y'  # 15 anos de dados históricos

  # Configurações de cache histórico (mesmo do XGBoost)
  cache:
    use_cache: true  # Usar cache para acelerar downloads
    use_unified: true  # Usar cache unificado (MAIS EFICIENTE - todas as ações em um arquivo)
    use_optimized: true  # Usar cache Parquet otimizado (requer pyarrow)
    force_download: false  # Forçar download completo (ignorar cache)
    force_training: true  # Forçar treinamento dos modelos (ignorar cache de modelos)
    max_days_outdated: 3  # Máximo de dias desatualizados antes de forçar atualização

  # Definição dos sinais (mesma lógica do XGBoost)
  signal_horizon: 1  # Não usado mais - sinais agora baseados na comparação com dia anterior

  # Features (mesmas do XGBoost)
  features:
    # Lags da média OHLC (quantos dias passados usar)
    ohlc_lags: 10  # Usar 10 dias passados da média OHLC
    pct_threshold: 0.5  # Threshold para considerar pct_change como relevante (0.5%)

    # Lags das features econométricas (quantos dias passados usar)
    econometric_lags: 5  # Usar 5 dias passados das features econométricas

    # Janelas para cálculos
    volatility_window: 20  # Janela para cálculo da volatilidade
    spread_multiplier: 0.5  # Multiplicador para estimar spread baseado na volatilidade

  # Parâmetros do modelo LightGBM (otimizados para melhor R²)
  model_params:
    # Parâmetros básicos otimizados
    n_estimators: 500      # Mais árvores para melhor aprendizado (era 100)
    max_depth: 8           # Maior profundidade para capturar padrões complexos (era 6)
    learning_rate: 0.05    # Taxa menor para convergência mais estável (era 0.1)
    random_state: 42       # Seed para reprodutibilidade

    # Parâmetros de estrutura das árvores
    num_leaves: 200        # Mais folhas para maior complexidade (era 63)
    min_child_samples: 10  # Menos amostras por folha para maior granularidade (era 20)
    min_child_weight: 0.001 # Peso mínimo para divisão

    # Regularização para evitar overfitting
    reg_alpha: 0.1         # Regularização L1
    reg_lambda: 0.1        # Regularização L2

    # Amostragem para robustez
    subsample: 0.8         # 80% das amostras por árvore (era 1.0)
    colsample_bytree: 0.8  # 80% das features por árvore (era 1.0)
    subsample_freq: 1      # Frequência de subamostragem

    # Parâmetros específicos para regressão
    min_split_gain: 0.0    # Ganho mínimo para divisão
    min_data_in_leaf: 5    # Dados mínimos por folha

    # Controle de velocidade vs precisão
    feature_fraction_bynode: 0.8  # Fração de features por nó
    extra_trees: false     # Não usar extremely randomized trees

    # Early stopping implícito
    early_stopping_rounds: 50  # Parar se não melhorar por 50 rounds

  # Divisão dos dados (mesma do XGBoost)
  test_size: 0.2  # 20% para teste

  # Divisão temporal dos dados (mesma do XGBoost)
  temporal_split:
    use_temporal_split: true  # Usar divisão temporal ao invés de aleatória

  # Normalização (mesma do XGBoost)
  use_standard_scaler: true  # Usar StandardScaler para normalizar features

  # Threshold de probabilidade para sinais (mesmo do XGBoost)
  probability_threshold: 0.50  # Sinais só são gerados se predição > 0.50%

  # Configurações específicas para cálculos (mesmas do XGBoost)
  calculations:
    emv_volume_divisor: 1000000  # Divisor para volume no cálculo EMV (Ease of Movement)

  # Configurações do estimador
  estimator:
    use_weighted_training: false  # Usar pesos no treinamento para amenizar impacto de grandes variações

    # Otimização de hiperparâmetros
    hyperparameter_tuning:
      enabled: false  # Ativar para busca automática de hiperparâmetros (demora mais)
      method: 'optuna'  # Método: 'optuna', 'grid_search', 'random_search'
      n_trials: 100     # Número de tentativas para Optuna
      cv_folds: 3       # Folds para validação cruzada

      # Espaço de busca para otimização
      search_space:
        n_estimators: [200, 300, 500, 800, 1000]
        max_depth: [6, 8, 10, 12, 15]
        learning_rate: [0.01, 0.03, 0.05, 0.08, 0.1]
        num_leaves: [100, 150, 200, 300, 500]
        min_child_samples: [5, 10, 15, 20, 30]
        reg_alpha: [0.0, 0.01, 0.1, 0.5, 1.0]
        reg_lambda: [0.0, 0.01, 0.1, 0.5, 1.0]
        subsample: [0.6, 0.7, 0.8, 0.9, 1.0]
        colsample_bytree: [0.6, 0.7, 0.8, 0.9, 1.0]

    # Configurações avançadas para melhorar R²
    advanced_features:
      use_feature_selection: true   # Seleção automática de features importantes
      feature_selection_threshold: 0.001  # Threshold de importância mínima
      use_polynomial_features: false  # Adicionar features polinomiais (cuidado com overfitting)
      polynomial_degree: 2          # Grau das features polinomiais
      use_interaction_features: false  # Adicionar interações entre features
      max_interaction_features: 10  # Máximo de features de interação

# ============================================================================
# CONFIGURAÇÕES DO LSTM
# ============================================================================
lstm:
  # Parâmetros da arquitetura
  units: 50  # Número de unidades LSTM
  dropout: 0.2  # Taxa de dropout
  recurrent_dropout: 0.0  # Taxa de dropout recorrente
  dense_units: 25  # Unidades da camada densa

  # Configurações de bias
  use_bias: true  # Usar bias na camada LSTM
  dense_use_bias: true  # Usar bias na camada densa
  output_use_bias: true  # Usar bias na camada de saída

  # Inicializadores de pesos
  kernel_initializer: 'glorot_uniform'  # Inicializador dos pesos da camada LSTM
  recurrent_initializer: 'orthogonal'  # Inicializador dos pesos recorrentes
  bias_initializer: 'zeros'  # Inicializador do bias da camada LSTM
  dense_kernel_initializer: 'glorot_uniform'  # Inicializador dos pesos da camada densa
  dense_bias_initializer: 'zeros'  # Inicializador do bias da camada densa
  output_kernel_initializer: 'glorot_uniform'  # Inicializador dos pesos da saída
  output_bias_initializer: 'zeros'  # Inicializador do bias da saída

  # Normalização
  normalize_target: true  # Normalizar o target (variação percentual) com StandardScaler

  # Saturação da saída (para corrigir viés em valores intermediários)
  use_saturation: true  # Ativar saturação na saída
  saturation_type: 'clip'  # Tipo de saturação: 'tanh', 'sigmoid', 'clip'
  saturation_factor: 3.0  # Fator de escala da saturação (limite para clipping)

  # Parâmetros de treinamento
  epochs: 5  # Número de épocas (reduzido para teste)
  batch_size: 32  # Tamanho do batch
  patience: 3  # Paciência para early stopping (reduzido para teste)
  optimizer: 'adam'  # Otimizador (adam, sgd, rmsprop)
  learning_rate: 0.001  # Taxa de aprendizado

  # Parâmetros de sequência
  sequence_length: 60  # Comprimento da sequência (janela de dias)
# ============================================================================
# CONFIGURAÇÕES DE SIMULAÇÃO E ANÁLISE DE CARTEIRA
# ============================================================================
simulation:

  # Configurações de trading
  trading:
    # Quantidade fixa de ações para comprar a cada sinal
    fixed_quantity_per_stock: 50  # Comprar 100 ações por vez (se capital permitir)

  # Configurações específicas para comparação de estratégias
  comparison:
    # Capital inicial para comparação de estratégias (pode ser diferente do capital da carteira)
    initial_capital: 1000.0  # R$ 1000 para comparação
    simulation_period: '1y'  # 1 ano para comparação

    # Configurações do método bootstrap
    bootstrap:
      num_scenarios: 10  # Número de cenários bootstrap
      stocks_per_scenario: 31  # Número de ações por cenário
      random_seed: 42  # Seed base para reprodutibilidade

  # Configurações para análise de carteira
  portfolio_analysis:
    # Capital inicial para análise de carteira
    initial_capital: 1000.0  # R$ 1000 para análise de carteira

# ============================================================================
# CONFIGURAÇÕES DE PERFORMANCE
# ============================================================================
performance:
  # Configurações de memória para TensorFlow
  tensorflow_memory_growth: true

  # Configurações de processamento paralelo
  parallel_processing: false  # Desabilitado por padrão
  max_workers: 4  # Número máximo de workers para processamento paralelo
